{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostList.vue", "mtime": 1748720501657}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AdminForumPostList.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"forum-container\">\n        <div class=\"forum-header\">\n            <h2>维修员论坛</h2>\n            <div class=\"forum-actions\">\n                <el-input\n                    placeholder=\"搜索帖子\"\n                    v-model=\"keyword\"\n                    class=\"search-input\"\n                    @keyup.enter.native=\"searchPosts\"\n                >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchPosts\"></el-button>\n                </el-input>\n                <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"createPost\" class=\"create-btn\">发布帖子</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"post in posts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"warning\" v-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                        <el-button\n                            size=\"mini\"\n                            type=\"text\"\n                            @click=\"toggleTop(post.id, !post.isTop)\">\n                            {{ post.isTop ? '取消置顶' : '置顶' }}\n                        </el-button>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <el-pagination\n                    background\n                    layout=\"prev, pager, next\"\n                    :total=\"total\"\n                    :page-size=\"pageSize\"\n                    :current-page.sync=\"currentPage\"\n                    @current-change=\"handlePageChange\"\n                >\n                </el-pagination>\n            </div>\n\n            <el-empty v-if=\"posts.length === 0\" description=\"暂无帖子\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostList',\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0,\n            keyword: ''\n        };\n    },\n    created() {\n        // 检查管理员登录状态\n        if (!sessionStorage.getItem('admin')) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n\n            // 使用实际的管理员ID\n            const admin = JSON.parse(sessionStorage.getItem('admin'));\n            const userId = admin.id; // 管理员ID\n\n            const params = {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: userId,\n                isAdminRequest: true // 标记这是管理员请求\n            };\n\n            if (this.keyword) {\n                params.keyword = this.keyword;\n            }\n\n            this.$get('/forum/post/list', params)\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePageChange(page) {\n            this.currentPage = page;\n            this.fetchPosts();\n        },\n        searchPosts() {\n            this.currentPage = 1;\n            this.fetchPosts();\n        },\n        createPost() {\n            this.$router.push('/admin/home/<USER>/create');\n        },\n        toggleTop(id, isTop) {\n            this.$put(`/forum/post/top/${id}?isTop=${isTop}`)\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');\n                        this.fetchPosts(); // 重新加载帖子列表\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('置顶操作失败:', err);\n                    this.$message.error('置顶操作失败');\n                });\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/admin/home/<USER>/post/${id}`);\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.forum-container {\n    padding: 20px;\n}\n\n.forum-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.forum-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.search-input {\n    width: 300px;\n    margin-right: 10px;\n}\n\n.create-btn {\n    margin-left: 10px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n    cursor: pointer;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n}\n\n.pagination-container {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}