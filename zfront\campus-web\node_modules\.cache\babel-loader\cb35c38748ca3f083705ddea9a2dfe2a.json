{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue", "mtime": 1745149170618}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumAudit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,eAAA;AACA,EAAA,IAAA,EAAA,iBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,SAAA,EAAA,SAHA;AAIA,MAAA,mBAAA,EAAA,KAJA;AAKA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OALA;AASA,MAAA,WAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,OAAA,EAAA,iBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AADA,OATA;AAeA,MAAA,UAAA,EAAA;AAfA,KAAA;AAiBA,GApBA;AAqBA,EAAA,QAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA,UAAA,KAAA,SAAA,KAAA,SAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA;;AACA,aAAA,KAAA,KAAA;AACA;AAVA,GArBA;AAiCA,EAAA,OAjCA,qBAiCA;AACA;AACA,QAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;;AAEA,SAAA,UAAA;AACA,GA1CA;AA2CA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACA,UAAA,MAAA,GAAA,KAAA,CAAA,EAAA,CAJA,CAIA;;AAEA,WAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,GAFA;AAEA;AACA,QAAA,MAAA,EAAA,MAHA;AAIA,QAAA,cAAA,EAAA,IAJA,CAIA;;AAJA,OAAA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OAZA,EAaA,KAbA,CAaA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAhBA,EAiBA,OAjBA,CAiBA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAnBA;AAoBA,KA3BA;AA4BA,IAAA,cA5BA,4BA4BA,CACA;AACA,KA9BA;AA+BA,IAAA,cA/BA,0BA+BA,EA/BA,EA+BA;AACA,WAAA,OAAA,CAAA,IAAA,kCAAA,EAAA;AACA,KAjCA;AAkCA,IAAA,WAlCA,uBAkCA,EAlCA,EAkCA;AAAA;;AACA,WAAA,QAAA,CAAA,YAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,6BAAA,EAAA,gBACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,YAAA,MAAA,CAAA,UAAA,GAFA,CAEA;;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,SAZA;AAaA,OAlBA,EAkBA,KAlBA,CAkBA,YAAA,CACA;AACA,OApBA;AAqBA,KAxDA;AAyDA,IAAA,UAzDA,sBAyDA,EAzDA,EAyDA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA7DA;AA8DA,IAAA,aA9DA,2BA8DA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,IAAA;;AAEA,UAAA,MAAA,CAAA,IAAA,6BAAA,MAAA,CAAA,UAAA,CAAA,MAAA,8BAAA,kBAAA,CAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,mBAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,UAAA,GAHA,CAGA;;AACA,aAJA,MAIA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,WATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WAbA,EAcA,OAdA,CAcA,YAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,WAhBA;AAiBA;AACA,OAtBA;AAuBA,KAtFA;AAuFA,IAAA,UAvFA,sBAuFA,EAvFA,EAuFA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,OAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA,EAAA;;AACA,QAAA,MAAA,CAAA,IAAA,uBAAA,EAAA,yBAAA,OAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,UAAA,GAFA,CAEA;;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,SAZA;AAaA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA,CACA;AACA,OArBA;AAsBA,KA9GA;AA+GA,IAAA,mBA/GA,iCA+GA;AACA,UAAA,KAAA,SAAA,KAAA,SAAA,EAAA;AACA,eAAA,SAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,SAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,SAAA;AACA;;AACA,aAAA,MAAA;AACA,KAxHA;AAyHA,IAAA,UAzHA,sBAyHA,OAzHA,EAyHA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA;AA7HA;AA3CA,CAAA", "sourcesContent": ["<template>\n    <div class=\"audit-posts-container\">\n        <div class=\"page-header\">\n            <h2>帖子审核</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已通过\" name=\"approved\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已通过</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <template v-if=\"post.status === 0\">\n                            <el-button size=\"mini\" type=\"success\" @click=\"approvePost(post.id)\">通过</el-button>\n                            <el-button size=\"mini\" type=\"danger\" @click=\"rejectPost(post.id)\">拒绝</el-button>\n                        </template>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" :description=\"getEmptyDescription()\"></el-empty>\n        </div>\n\n        <!-- 拒绝原因对话框 -->\n        <el-dialog title=\"拒绝原因\" :visible.sync=\"rejectDialogVisible\" width=\"30%\">\n            <el-form :model=\"rejectForm\" :rules=\"rejectRules\" ref=\"rejectForm\">\n                <el-form-item label=\"原因\" prop=\"reason\">\n                    <el-input\n                        type=\"textarea\"\n                        v-model=\"rejectForm.reason\"\n                        placeholder=\"请输入拒绝原因\"\n                        :rows=\"4\"\n                    ></el-input>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"rejectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"submitting\">确定</el-button>\n            </span>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumAudit',\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'pending',\n            rejectDialogVisible: false,\n            rejectForm: {\n                postId: null,\n                reason: ''\n            },\n            rejectRules: {\n                reason: [\n                    { required: true, message: '请输入拒绝原因', trigger: 'blur' },\n                    { min: 5, max: 200, message: '原因长度在5到200个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'approved') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        if (!sessionStorage.getItem('admin')) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n            // 使用实际的管理员ID\n            const admin = JSON.parse(sessionStorage.getItem('admin'));\n            const userId = admin.id; // 管理员ID\n\n            this.$get('/forum/post/list', {\n                pageNum: 1,\n                pageSize: 100, // 设置较大的页面大小以获取所有帖子\n                userId: userId,\n                isAdminRequest: true // 标记这是管理员请求\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/admin/home/<USER>/post/${id}`);\n        },\n        approvePost(id) {\n            this.$confirm('确定通过这篇帖子吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'info'\n            }).then(() => {\n                this.$put(`/forum/post/audit/${id}?status=1`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('审核通过成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '操作失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('审核操作失败:', err);\n                        this.$message.error('审核操作失败');\n                    });\n            }).catch(() => {\n                // 取消操作\n            });\n        },\n        rejectPost(id) {\n            this.rejectForm.postId = id;\n            this.rejectForm.reason = '';\n            this.rejectDialogVisible = true;\n        },\n        confirmReject() {\n            this.$refs.rejectForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n\n                    this.$put(`/forum/post/audit/${this.rejectForm.postId}?status=2&reason=${encodeURIComponent(this.rejectForm.reason)}`)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('拒绝成功');\n                                this.rejectDialogVisible = false;\n                                this.fetchPosts(); // 重新加载帖子列表\n                            } else {\n                                this.$message.error(res.data.msg || '操作失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('拒绝操作失败:', err);\n                            this.$message.error('拒绝操作失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const adminId = JSON.parse(sessionStorage.getItem('admin')).id;\n                this.$del(`/forum/post/${id}?operatorId=${adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        getEmptyDescription() {\n            if (this.activeTab === 'pending') {\n                return '暂无待审核帖子';\n            } else if (this.activeTab === 'approved') {\n                return '暂无已通过帖子';\n            } else if (this.activeTab === 'rejected') {\n                return '暂无已拒绝帖子';\n            }\n            return '暂无帖子';\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.audit-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}