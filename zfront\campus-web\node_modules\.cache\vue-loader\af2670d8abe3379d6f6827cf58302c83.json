{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue?vue&type=style&index=0&id=9a0665a0&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue", "mtime": 1748720501687}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubm90aWZpY2F0aW9ucy1jb250YWluZXIgewogICAgcGFkZGluZzogMjBweDsKfQoKLnBhZ2UtaGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLm5vdGlmaWNhdGlvbi1jYXJkIHsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3M7Cn0KCi5ub3RpZmljYXRpb24tY2FyZC51bnJlYWQgewogICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKfQoKLm5vdGlmaWNhdGlvbi1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9Cgoubm90aWZpY2F0aW9uLWNvbnRlbnQgewogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5ub3RpZmljYXRpb24tYWN0aW9ucyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIGdhcDogMTBweDsKfQoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIHBhZGRpbmctcmlnaHQ6IDIwcHg7Cn0KCi5sb2FkaW5nLWNhcmQgewogICAgcGFkZGluZzogMjBweDsKfQo="}, {"version": 3, "sources": ["Notifications.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "Notifications.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"notifications-container\">\n        <div class=\"page-header\">\n            <h2>消息通知</h2>\n            <div class=\"header-actions\">\n                <el-button type=\"text\" @click=\"markAllAsRead\" :disabled=\"notifications.length === 0\">全部标为已读</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"notification in notifications\" :key=\"notification.id\" class=\"notification-card\" :class=\"{ 'unread': !notification.isRead }\">\n                <div class=\"notification-header\">\n                    <div class=\"notification-type\">\n                        <el-tag type=\"primary\" v-if=\"notification.type === 1\">点赞</el-tag>\n                        <el-tag type=\"success\" v-else-if=\"notification.type === 2\">评论</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"notification.type === 3\">删除</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"notification.type === 4\">审核未通过</el-tag>\n                    </div>\n                    <div class=\"notification-time\">{{ formatDate(notification.createTime) }}</div>\n                </div>\n                <div class=\"notification-content\">{{ notification.content }}</div>\n                <div class=\"notification-actions\">\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"viewRelatedPost(notification)\"\n                        v-if=\"notification.type !== 3\"\n                    >\n                        查看详情\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"markAsRead(notification)\"\n                        v-if=\"!notification.isRead\"\n                    >\n                        标为已读\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"deleteNotification(notification.id)\"\n                    >\n                        删除\n                    </el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"notifications.length === 0\" description=\"暂无通知\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'Notifications',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            notifications: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    created() {\n        this.fetchNotifications();\n    },\n    methods: {\n        fetchNotifications() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$get('/forum/notification/list', {\n                userId: userId,\n                pageNum: this.currentPage,\n                pageSize: this.pageSize\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.notifications = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取通知列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取通知列表失败:', err);\n                    this.$message.error('获取通知列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange() {\n            this.fetchNotifications();\n        },\n        viewRelatedPost(notification) {\n            if (notification.relatedId && notification.type !== 3) {\n                // 如果是点赞或评论通知，跳转到相关帖子\n                this.$router.push(`/home/<USER>/post/${notification.relatedId}`);\n                \n                // 如果通知未读，标记为已读\n                if (!notification.isRead) {\n                    this.markAsRead(notification);\n                }\n            }\n        },\n        markAsRead(notification) {\n            this.$put(`/forum/notification/read/${notification.id}`)\n                .then(res => {\n                    if (res.data.status) {\n                        notification.isRead = true;\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '标记已读失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记已读失败:', err);\n                    this.$message.error('标记已读失败');\n                });\n        },\n        markAllAsRead() {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$put('/forum/notification/read/all', { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('全部标记为已读');\n                        // 更新通知状态\n                        this.notifications.forEach(notification => {\n                            notification.isRead = true;\n                        });\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记全部已读失败:', err);\n                    this.$message.error('标记全部已读失败');\n                });\n        },\n        deleteNotification(id) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$del(`/forum/notification/${id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('删除成功');\n                        // 从列表中移除\n                        this.notifications = this.notifications.filter(n => n.id !== id);\n                        // 如果当前页没有数据了，且不是第一页，则跳转到上一页\n                        if (this.notifications.length === 0 && this.currentPage > 1) {\n                            this.currentPage--;\n                            this.fetchNotifications();\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '删除失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('删除通知失败:', err);\n                    this.$message.error('删除通知失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.notifications-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.notification-card {\n    margin-bottom: 15px;\n    transition: background-color 0.3s;\n}\n\n.notification-card.unread {\n    background-color: #f0f9ff;\n}\n\n.notification-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.notification-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.notification-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}