{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accepted.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accepted.vue", "mtime": 1748722900827}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Accepted.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA,SAAA,QAAA,QAAA,MAAA;AACA,OAAA,IAAA,MAAA,uBAAA,C,CACA;;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,IAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,CAAA,GAAA,CADA;AAEA,MAAA,KAAA,EAAA,EAFA;AAGA;AACA,MAAA,YAAA,EAAA,IAJA;AAKA;AAEA;AACA,MAAA,mBAAA,EAAA,KARA;AASA,MAAA,aAAA,EAAA,IATA;AAUA,MAAA,WAAA,EAAA,IAVA;AAYA;AACA,MAAA,WAAA,EAAA,KAbA;AAcA,MAAA,eAAA,EAAA;AAdA,KAAA;AAgBA,GAtBA;AAuBA,EAAA,QAAA,kCACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA;AAGA;AACA,IAAA,aAJA,2BAIA;AACA;AACA,UAAA,SAAA,GAAA;AACA,cAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA;AAEA,aAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AAGA,aAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA;AAAA;AAHA,OAAA,CAFA,CAQA;;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,CAAA,KAAA,GAAA,KAAA,KAAA,CATA,CAWA;;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,IAAA,IAAA,CAAA,KAAA,KAAA,SAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA,GAAA,GAAA;;AACA,YAAA,SAAA,CAAA,KAAA,CAAA,EAAA;AACA,UAAA,SAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA,EAZA,CAmBA;;AACA,aAAA,MAAA,CAAA,OAAA,CAAA,SAAA,EAAA,GAAA,CAAA;AAAA;AAAA,YAAA,MAAA;AAAA,YAAA,IAAA;;AAAA,eAAA;AACA,UAAA,MAAA,EAAA,QAAA,CAAA,MAAA,CADA;AAEA,UAAA,UAAA,EAAA,IAAA,CAAA,IAFA;AAGA,UAAA,KAAA,EAAA,IAAA,CAAA;AAHA,SAAA;AAAA,OAAA,CAAA;AAKA;AA7BA,IAvBA;AAsDA,EAAA,OAtDA,qBAsDA;AACA,SAAA,OAAA;AACA,GAxDA;AAyDA,EAAA,OAAA,EAAA;AACA,IAAA,GADA,eACA,EADA,EACA;AAAA;;AACA,WAAA,IAAA,CAAA,uBAAA,EAAA,EACA,IADA,CACA,YAAA;AACA,QAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA,EAAA,SAAA;;AACA,QAAA,KAAA,CAAA,OAAA;AACA,OAJA;AAKA,KAPA;AAQA,IAAA,OARA,qBAQA;AAAA;;AACA,WAAA,IAAA,CAAA,gBAAA,EAAA;AAAA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CACA,MADA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,KAAA,KAAA,CAAA;AAAA,WADA,EAEA,GAFA,CAEA,UAAA,IAAA,EAAA;AACA;AACA,gBAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,EAAA;AACA,aAJA,CAMA;;;AACA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA;AAAA,gBAAA,IAAA,EAAA;AAAA,eAAA;AACA,aATA,CAWA;;;AACA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA;AAAA,gBAAA,IAAA,EAAA;AAAA,eAAA;AACA,aAdA,CAgBA;;;AACA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA;AAAA,gBAAA,IAAA,EAAA;AAAA,eAAA;AACA,aAnBA,CAqBA;;;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,IAAA,CAAA,EAAA,EAAA,OAAA,EAAA,IAAA,CAAA,SAAA,EAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAEA,mBAAA,IAAA;AACA,WA3BA,CAAA;AA4BA,SA9BA,MA8BA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,MAAA,CAAA,KAAA;AACA,OArCA,EAsCA,KAtCA,CAsCA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA,OAzCA;AA0CA,KAnDA;AAqDA,IAAA,UArDA,sBAqDA,EArDA,EAqDA;AAAA;;AACA,WAAA,IAAA,CAAA,kBAAA,EAAA;AAAA,QAAA,EAAA,EAAA;AAAA,OAAA,EAAA;AAAA,OACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;AACA,SAFA,MAEA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAPA;AAQA,KA9DA;;AAgEA;AACA;AACA;AACA;AACA,IAAA,UApEA,sBAoEA,IApEA,EAoEA;AAAA;;AACA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA,CAHA,CAKA;;AACA,WAAA,IAAA,CAAA,sBAAA,EAAA;AAAA,QAAA,MAAA,EAAA,IAAA,CAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,GAAA;;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,SAHA,MAGA;AACA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AACA;AACA,OAVA,EAWA,KAXA,CAWA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,KAAA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,OAfA;AAgBA,KA1FA;;AA4FA;AACA;AACA;AACA;AACA,IAAA,QAhGA,oBAgGA,IAhGA,EAgGA;AACA,WAAA,eAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA;AAnGA,GAzDA;AA+JA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,sBACA,IADA,EACA;AACA,UAAA,CAAA,IAAA,EAAA,OAAA,GAAA;;AACA,UAAA;AACA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,GAAA,IAAA,CAAA,WAAA,EAAA;AACA,YAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,YAAA,GAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,YAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,YAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AAEA,yBAAA,IAAA,cAAA,KAAA,cAAA,GAAA,cAAA,KAAA,cAAA,OAAA;AACA,OAVA,CAUA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,CAAA,EAAA,IAAA;AACA,eAAA,MAAA;AACA;AACA;AAjBA;AA/JA,CAAA", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>已接受任务</span>\n            </div>\n\n            <!-- 状态分类标签 -->\n            <div class=\"status-tabs\">\n                <el-tabs v-model=\"activeStatus\" type=\"card\">\n                    <el-tab-pane\n                        v-for=\"group in tasksByStatus\"\n                        :key=\"group.status\"\n                        :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                        :name=\"group.status.toString()\"\n                    >\n                        <el-card\n                            class=\"box-card\"\n                            v-for=\"item in group.tasks\"\n                            :key=\"item.id\"\n                            style=\"margin-top: 20px\"\n                        >\n                <div slot=\"header\" class=\"clearfix\"\n                     style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span style=\"display: flex;align-items: center\">\n                            <el-tag v-if=\"item\" :type=\"item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')\"\n                                    style=\"margin-right: 5px\">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>\n                            任务名称：{{item ? item.taskTitle : ''}}\n                        </span>\n                    <!--                    <el-button style=\"float: right; padding: 3px 0\" type=\"text\">查看接受人信息</el-button>-->\n                    <div>\n                        <!-- 已完成任务显示查看评价按钮 -->\n                        <el-button\n                            style=\"float: right; padding: 3px 0\"\n                            type=\"text\"\n                            v-show=\"item && item.state == 2\"\n                            @click=\"viewRemark(item)\">查看评价\n                        </el-button>\n\n                        <!-- 在线交流按钮 - 服务中的任务显示 -->\n                        <el-button v-show=\"item && item.state == 1\"\n                        style=\"float: right; padding: 3px 0; margin-right: 10px\" type=\"text\" @click=\"openChat(item)\">\n                            <i class=\"el-icon-chat-dot-round\"></i> 在线交流\n                        </el-button>\n\n                        <!-- 未完成任务显示取消任务按钮 -->\n                        <el-popconfirm title=\"确定取消任务吗\" @confirm=\"del(item.id)\">\n                            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" slot=\"reference\"\n                                       v-show=\"item && item.state != 2\">取消任务\n                            </el-button>\n                        </el-popconfirm>\n                    </div>\n                </div>\n                <div>\n                    任务内容：{{item ? item.taskContext : ''}}\n                </div>\n                <el-collapse style=\"margin-top: 20px\" v-model=\"activeNames\">\n                    <el-collapse-item title=\"用户信息\" name=\"1\">\n                        <el-card class=\"box-card\" v-if=\"item && item.publish\">\n                            <div slot=\"header\" class=\"clearfix\">\n                                <span>用户姓名：{{item.publish.username || '未知用户'}}</span>\n                                <!-- <el-button style=\"float: right; padding: 3px 0\" type=\"text\">投诉</el-button> -->\n                            </div>\n\n                            <div class=\"box_center\">\n                                <table cellspacing=\"0\">\n                                    <tr>\n                                        <th>电话</th>\n                                        <th>角色</th>\n                                        <th>类别</th>\n                                        <th>子类别</th>\n                                        <th>任务金额</th>\n                                        <th>接受任务时间</th>\n                                    </tr>\n                                    <tr align=\"center\">\n                                        <td>{{item.publish.phone || '无'}}</td>\n                                        <td>{{item.publish.role ? item.publish.role.name : '无'}}</td>\n                                        <td>{{item.dept ? item.dept.name : '无'}}</td>\n                                        <td>{{item.type ? item.type.name : '无'}}</td>\n                                        <td><i class=\"el-icon-money\" style=\"color: red;\">{{item.reward || 0}}元</i></td>\n                                        <td>\n                                            <!-- 添加调试信息 -->\n                                            <span v-if=\"item.orderTime\">{{item.orderTime | formatDate}}</span>\n                                            <span v-else>无时间数据</span>\n                                        </td>\n                                    </tr>\n                                </table>\n                            </div>\n                        </el-card>\n                        <el-card class=\"box-card\" v-else>\n                            <div class=\"box_center\">\n                                <p>暂无发布人信息</p>\n                            </div>\n                        </el-card>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"查看钱款\" name=\"2\" v-if=\"item && item.state == 2\">\n                        <el-card class=\"box-card\">\n                            <div>\n                                <p>实收款：<i class=\"el-icon-money\" style=\"color: red;\">{{item.reward || 0}}元</i></p>\n                                <p>到款时间：\n                                    <span v-if=\"item.endTime\">{{item.endTime | formatDate}}</span>\n                                    <span v-else>无</span>\n                                </p>\n                            </div>\n                        </el-card>\n                    </el-collapse-item>\n                </el-collapse>\n                        </el-card>\n\n                        <!-- 当前状态下没有任务时显示 -->\n                        <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                            <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无已接受任务</span>\n                        </div>\n                    </el-tab-pane>\n                </el-tabs>\n            </div>\n\n            <!-- 没有任何任务时显示 -->\n            <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                <span><i class=\"el-icon-refresh-right\"></i>暂无已接受任务</span>\n            </div>\n        </el-card>\n\n        <!-- 评价对话框 -->\n        <el-dialog\n            :title=\"currentTask ? '任务「' + currentTask.taskTitle + '」的评价' : '任务评价'\"\n            :visible.sync=\"remarkDialogVisible\"\n            width=\"550px\"\n            center>\n            <div v-if=\"currentRemark\">\n                <div class=\"remark-info\">\n                    <div class=\"task-info\" v-if=\"currentTask\">\n                        <div class=\"task-header\">\n                            <i class=\"el-icon-info-circle\"></i> 任务信息\n                        </div>\n                        <div class=\"task-details\">\n                            <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>\n                            <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>\n                            <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>\n                        </div>\n                    </div>\n\n                    <div class=\"star-rating\">\n                        <span class=\"rating-label\">评分：</span>\n                        <el-rate\n                            v-model=\"currentRemark.star\"\n                            disabled\n                            show-score\n                            text-color=\"#ff9900\">\n                        </el-rate>\n                    </div>\n                    <div class=\"remark-content\">\n                        <span class=\"content-label\">评价内容：</span>\n                        <p>{{currentRemark.remark}}</p>\n                    </div>\n                    <div class=\"remark-time\" v-if=\"currentRemark.createTime\">\n                        <span>评价时间：{{currentRemark.createTime | formatDate}}</span>\n                    </div>\n                </div>\n            </div>\n            <div v-else class=\"no-remark\">\n                <i class=\"el-icon-warning-outline\"></i>\n                <p>用户尚未评价此任务</p>\n                <div class=\"task-info\" v-if=\"currentTask\">\n                    <div class=\"task-header\">\n                        <i class=\"el-icon-info-circle\"></i> 任务信息\n                    </div>\n                    <div class=\"task-details\">\n                        <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>\n                        <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>\n                        <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>\n                    </div>\n                </div>\n            </div>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"remarkDialogVisible = false\">关 闭</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 聊天组件 -->\n        <Chat\n            :visible.sync=\"chatVisible\"\n            :task=\"currentChatTask\"\n            :current-user-id=\"user.id\"\n            v-if=\"currentChatTask\" />\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\";\n    import Chat from '@/components/Chat.vue';\n    // 不再使用导入的formatDate函数，因为我们直接在过滤器中实现了日期格式化\n\n    export default {\n        name: \"Accepted\",\n        components: {\n            Chat\n        },\n        data() {\n            return {\n                activeNames: ['1'],\n                tasks: [],\n                // 当前选中的状态\n                activeStatus: '-1',\n                // drawer:false,\n\n                // 评价相关\n                remarkDialogVisible: false,\n                currentRemark: null,\n                currentTask: null,\n\n                // 聊天相关\n                chatVisible: false,\n                currentChatTask: null\n            };\n        },\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射 - 已接受任务只有服务中和已完成两种状态\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    '1': { name: '服务中', tasks: [] },\n                    '2': { name: '已完成', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.tasks;\n\n                // 按状态分组\n                this.tasks.forEach(task => {\n                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '1';\n                    if (statusMap[state]) {\n                        statusMap[state].tasks.push(task);\n                    }\n                });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.newList()\n        },\n        methods: {\n            del(id) {\n                this.$put('/task/takerCancel/' + id)\n                .then(() => {\n                    this.$notifyMsg('成功', '取消任务成功', 'success')\n                    this.newList()\n                })\n            },\n            newList() {\n                this.$get(\"/task/accepted\", {id: this.user.id})\n                .then(res => {\n                    // 确保每个任务对象都有必要的属性\n                    if (res.data.task && Array.isArray(res.data.task)) {\n                        // 过滤掉状态为0（待接单）的任务，因为已接受任务不应该包含待接单状态\n                        this.tasks = res.data.task\n                            .filter(task => task.state !== 0)\n                            .map(task => {\n                                // 确保publish对象存在\n                                if (!task.publish) {\n                                    task.publish = {};\n                                }\n\n                                // 确保publish.role对象存在\n                                if (!task.publish.role) {\n                                    task.publish.role = { name: '未知' };\n                                }\n\n                                // 确保publish.dept对象存在\n                                if (!task.publish.dept) {\n                                    task.publish.dept = { name: '未知' };\n                                }\n\n                                // 确保publish.type对象存在\n                                if (!task.publish.type) {\n                                    task.publish.type = { name: '未知' };\n                                }\n\n                                // 打印orderTime以便调试\n                                console.log('任务ID:', task.id, '接受时间:', task.orderTime, '状态:', task.state);\n\n                                return task;\n                            });\n                    } else {\n                        this.tasks = [];\n                    }\n                    console.log('获取到的任务列表:', this.tasks);\n                })\n                .catch(error => {\n                    console.error('获取任务列表失败:', error);\n                    this.tasks = [];\n                });\n            },\n\n            acceptTask(id) {\n                this.$put('task/takerAccept', { id: id }) // 仅传递任务ID\n                .then((res) => {\n                    if (res.data.status) {\n                        this.$msg(res.data.msg, \"success\");\n                    } else {\n                        console.error('错误详情:', res.data.msg);\n                    }\n                });\n            },\n\n            /**\n             * 查看任务评价\n             * @param {Object} task 任务对象\n             */\n            viewRemark(task) {\n                // 直接显示对话框，不需要确认\n                this.currentTask = task;\n                this.remarkDialogVisible = true;\n\n                // 根据任务ID查询评价信息\n                this.$get('/student/remark/list', { taskId: task.id })\n                .then(res => {\n                    console.log('评价查询结果:', res);\n                    if (res.data && res.data.rows && res.data.rows.length > 0) {\n                        // 找到评价\n                        this.currentRemark = res.data.rows[0];\n                    } else {\n                        // 没有评价\n                        this.currentRemark = null;\n                    }\n                })\n                .catch(error => {\n                    console.error('查询评价失败:', error);\n                    this.currentRemark = null;\n                    this.$message.error('查询评价信息失败，请稍后重试');\n                });\n            },\n\n            /**\n             * 打开在线交流\n             * @param {Object} task 任务对象\n             */\n            openChat(task) {\n                this.currentChatTask = task;\n                this.chatVisible = true;\n            }\n        },\n\n        filters: {\n            formatDate(time) {\n                if (!time) return '无';\n                try {\n                    // 直接使用字符串格式化\n                    const date = new Date(time);\n                    const year = date.getFullYear();\n                    const month = String(date.getMonth() + 1).padStart(2, '0');\n                    const day = String(date.getDate()).padStart(2, '0');\n                    const hours = String(date.getHours()).padStart(2, '0');\n                    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n                    return `${year}-${month}-${day} ${hours}:${minutes}`;\n                } catch (e) {\n                    console.error('日期格式化错误:', e, time);\n                    return '格式错误';\n                }\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n\n        .box_center table {\n            margin: 0 auto;\n            width: 100%;\n            border-collapse: collapse;\n            border: 1px solid #ebeef5;\n        }\n\n        .box_center th, td {\n            padding: 12px 15px;\n            text-align: center;\n            border: 1px solid #ebeef5;\n        }\n\n        .box_center th {\n            background-color: #F5F7FA;\n            color: #606266;\n            font-weight: bold;\n        }\n\n        .status-tabs {\n            margin-bottom: 20px;\n\n            /deep/ .el-tabs__header {\n                margin-bottom: 15px;\n            }\n\n            /deep/ .el-tabs__item {\n                height: 40px;\n                line-height: 40px;\n                font-size: 14px;\n                color: #606266;\n\n                &.is-active {\n                    color: #409EFF;\n                    font-weight: bold;\n                }\n            }\n        }\n    }\n\n    /* 评价对话框样式 */\n    .remark-info {\n        padding: 15px;\n\n        .task-info {\n            margin-bottom: 20px;\n            border: 1px solid #ebeef5;\n            border-radius: 4px;\n            overflow: hidden;\n\n            .task-header {\n                background-color: #f5f7fa;\n                padding: 10px 15px;\n                font-weight: bold;\n                color: #606266;\n                border-bottom: 1px solid #ebeef5;\n\n                i {\n                    color: #409EFF;\n                    margin-right: 5px;\n                }\n            }\n\n            .task-details {\n                padding: 15px;\n\n                p {\n                    margin: 5px 0;\n                    line-height: 1.6;\n\n                    span {\n                        font-weight: bold;\n                        margin-right: 5px;\n                        color: #606266;\n                    }\n                }\n            }\n        }\n\n        .star-rating {\n            margin-bottom: 15px;\n            display: flex;\n            align-items: center;\n\n            .rating-label {\n                font-weight: bold;\n                margin-right: 10px;\n                min-width: 70px;\n                color: #606266;\n            }\n        }\n\n        .remark-content {\n            margin-bottom: 15px;\n\n            .content-label {\n                font-weight: bold;\n                display: block;\n                margin-bottom: 10px;\n                color: #606266;\n            }\n\n            p {\n                background-color: #f5f7fa;\n                padding: 15px;\n                border-radius: 4px;\n                margin: 0;\n                line-height: 1.6;\n            }\n        }\n\n        .remark-time {\n            text-align: right;\n            color: #909399;\n            font-size: 12px;\n            margin-top: 10px;\n        }\n    }\n\n    .no-remark {\n        text-align: center;\n        padding: 20px 0;\n        color: #909399;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin-bottom: 20px;\n        }\n\n        .task-info {\n            margin-top: 20px;\n            text-align: left;\n            border: 1px solid #ebeef5;\n            border-radius: 4px;\n            overflow: hidden;\n\n            .task-header {\n                background-color: #f5f7fa;\n                padding: 10px 15px;\n                font-weight: bold;\n                color: #606266;\n                border-bottom: 1px solid #ebeef5;\n\n                i {\n                    font-size: 14px;\n                    display: inline;\n                    color: #409EFF;\n                    margin-right: 5px;\n                    margin-bottom: 0;\n                }\n            }\n\n            .task-details {\n                padding: 15px;\n\n                p {\n                    margin: 5px 0;\n                    line-height: 1.6;\n                    font-size: 14px;\n\n                    span {\n                        font-weight: bold;\n                        margin-right: 5px;\n                        color: #606266;\n                    }\n                }\n            }\n        }\n    }\n</style>"], "sourceRoot": "src/views/user/children"}]}