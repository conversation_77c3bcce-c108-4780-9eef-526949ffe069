{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\userremark.vue?vue&type=style&index=0&id=e4af9560&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\userremark.vue", "mtime": 1748720501127}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnBhZ2luYXRpb24td3JhcHBlciB7CiAgbWFyZ2luLXRvcDogMjBweDsKfQo="}, {"version": 3, "sources": ["userremark.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmMA;AACA;AACA", "file": "userremark.vue", "sourceRoot": "src/views/remark", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"星级\" prop=\"star\">\n        <el-input\n          v-model=\"queryParams.star\"\n          placeholder=\"请输入星级\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务id\" prop=\"taskId\">\n        <el-input\n          v-model=\"queryParams.taskId\"\n          placeholder=\"请输入任务id\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form> -->\n\n\n    <el-table v-loading=\"loading\" :data=\"remarkList\"  @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n       <el-table-column label=\"任务id\" align=\"center\" prop=\"taskId\" />\n      <el-table-column label=\"评价人id\" align=\"center\" prop=\"publishId\" />\n\n      <el-table-column label=\"评分\" min-width=\"100\" align=\"center\" prop=\"star\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            show-text>\n          </el-rate>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"remark\" />\n    </el-table>\n    \n    <div class=\"pagination-wrapper\">\n      <common-pagination\n        :total=\"total\"\n        :current-page.sync=\"currentPage\"\n        :page-size.sync=\"pageSize\"\n        @pagination-change=\"handlePaginationChange\">\n      </common-pagination>\n    </div>\n\n    <!-- 添加或修改remark对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n      \n        <el-form-item label=\"星级\" prop=\"star\">\n            <el-rate\n                v-model=\"form.star\"\n                show-text>\n            </el-rate>\n        </el-form-item>\n        <el-form-item label=\"评价内容\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n        </el-form-item>\n      \n      </el-form>\n\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRemark } from \"@/api/remark/remark\";\nimport {mapState} from \"vuex\"\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n  name: \"UserRemark\",\n  components: {\n    CommonPagination\n  },\n  data() {\n    return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // remark表格数据\n        remarkList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n\n        // 查询参数\n        queryParams: {\n            pageNum: 1,\n            pageSize: 10,\n            star: null,\n            taskId: null,\n            acceptId: null,\n            publishId: null\n        },\n\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n        },\n        tasks: [],\n        // 当前页码\n        currentPage: 1,\n        // 每页显示条数\n        pageSize: 10,\n    };\n  },\n  computed: {\n      ...mapState('user', ['user'])\n  },\n  created() {\n    this.getList();\n    this.retrieveData();\n  },\n  methods: {\n    /** 查询'评价我的'列表 搜索接受任务人为自己 */\n    getList() {\n      this.loading = true;\n      listRemark({\n        acceptId: this.user.id,\n        pageNum: this.currentPage,\n        pageSize: this.pageSize\n      }).then(res => {\n        this.remarkList = res.data.rows;\n        this.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    //任务列表\n    retrieveData() {\n        this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n        // console.log(res.data.task)\n        this.tasks = res.data.task\n        })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        star: null,\n        remark: null,\n        taskId: null,\n        acceptId: null,\n        publishId: null\n      };\n    //   this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handlePaginationChange() {\n      this.getList();\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.pagination-wrapper {\n  margin-top: 20px;\n}\n</style>\n"]}]}