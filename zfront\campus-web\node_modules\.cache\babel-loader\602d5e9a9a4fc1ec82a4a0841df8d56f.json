{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue", "mtime": 1745151314454}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostCreate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,eAAA;AACA,EAAA,IAAA,EAAA,sBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA,EAHA,CAGA;;AAHA,OADA;AAMA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,OAAA,EAAA,iBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CADA;AAKA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,EAAA;AAAA,UAAA,GAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AALA,OANA;AAgBA,MAAA,UAAA,EAAA;AAhBA,KAAA;AAkBA,GArBA;AAsBA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,UAAA,GAAA,IAAA;;AAEA,UAAA,KAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA,CAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CACA;;;AACA,cAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,yBAAA;AACA,aAHA,MAGA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,WARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,YAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WAZA,EAaA,OAbA,CAaA,YAAA;AACA,YAAA,KAAA,CAAA,UAAA,GAAA,KAAA;AACA,WAfA;AAgBA;AACA,OArBA;AAsBA,KAxBA;AAyBA,IAAA,MAzBA,oBAyBA;AACA,WAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA;AA3BA;AAtBA,CAAA", "sourcesContent": ["<template>\n    <div class=\"create-post-container\">\n        <div class=\"page-header\">\n            <h2>发布帖子</h2>\n        </div>\n\n        <el-form :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">发布</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostCreate',\n    data() {\n        return {\n            postForm: {\n                title: '',\n                content: '',\n                authorId: JSON.parse(sessionStorage.getItem('admin')).id // 使用管理员ID\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    methods: {\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    this.$post('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('发布成功'); // 管理员发布无需审核\n                                this.$router.push('/admin/home/<USER>/posts');\n                            } else {\n                                this.$message.error(res.data.msg || '发布失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('发布帖子失败:', err);\n                            this.$message.error('发布帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.create-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}