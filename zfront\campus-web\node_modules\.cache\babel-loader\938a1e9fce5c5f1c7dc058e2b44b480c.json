{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue", "mtime": 1748720502030}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["MyPosts.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,OAAA,gBAAA,MAAA,mCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,gBAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,SAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,CAJA;AAKA,MAAA,QAAA,EAAA,EALA;AAMA,MAAA,KAAA,EAAA;AANA,KAAA;AAQA,GAdA;AAeA,EAAA,QAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA,UAAA,KAAA,SAAA,KAAA,KAAA,EAAA;AACA,eAAA,KAAA,KAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,WAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,SAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AAAA,SAAA,CAAA;AACA;;AACA,aAAA,KAAA,KAAA;AACA;AAZA,GAfA;AA6BA,EAAA,OA7BA,qBA6BA;AACA,SAAA,YAAA;AACA,GA/BA;AAgCA,EAAA,OAAA,EAAA;AACA,IAAA,YADA,0BACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA,CAFA,CAIA;;AACA,WAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,OAAA,EAAA,KAAA,WADA;AAEA,QAAA,QAAA,EAAA,KAAA,QAFA;AAGA,QAAA,MAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA,MAJA,CAIA;;AAJA,OAAA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA,CAAA,KAAA,CAAA,MAAA;AACA,SALA,MAKA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OAfA,EAgBA,KAhBA,CAgBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAnBA,EAoBA,OApBA,CAoBA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAtBA;AAuBA,KA7BA;AA8BA,IAAA,cA9BA,4BA8BA,CACA;AACA,KAhCA;AAiCA,IAAA,UAjCA,wBAiCA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA,KAnCA;AAoCA,IAAA,cApCA,0BAoCA,EApCA,EAoCA;AACA,WAAA,OAAA,CAAA,IAAA,4BAAA,EAAA;AACA,KAtCA;AAuCA,IAAA,QAvCA,oBAuCA,EAvCA,EAuCA;AACA,WAAA,OAAA,CAAA,IAAA,4BAAA,EAAA;AACA,KAzCA;AA0CA,IAAA,UA1CA,sBA0CA,EA1CA,EA0CA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA;;AACA,QAAA,MAAA,CAAA,IAAA,uBAAA,EAAA,GAAA;AAAA,UAAA,UAAA,EAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,YAAA,GAFA,CAEA;;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,SAZA;AAaA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA,CACA;AACA,OArBA;AAsBA,KAjEA;AAkEA,IAAA,UAlEA,sBAkEA,OAlEA,EAkEA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,KAtEA;AAuEA,IAAA,sBAvEA,oCAuEA;AACA,WAAA,YAAA;AACA;AAzEA;AAhCA,CAAA", "sourcesContent": ["<template>\n    <div class=\"my-posts-container\">\n        <div class=\"page-header\">\n            <h2>我的帖子</h2>\n            <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"全部\" name=\"all\"></el-tab-pane>\n                <el-tab-pane label=\"已发布\" name=\"published\"></el-tab-pane>\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已发布</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost(post.id)\">编辑</el-button>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" description=\"暂无帖子\"></el-empty>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'MyPosts',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'all',\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'all') {\n                return this.posts;\n            } else if (this.activeTab === 'published') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        this.fetchMyPosts();\n    },\n    methods: {\n        fetchMyPosts() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n\n            // 使用authorId参数来过滤当前用户的帖子\n            this.$get('/forum/post/list', {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: userId,\n                authorId: userId // 现在后端支持按作者ID过滤\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        // 不需要再进行前端过滤，因为后端已经过滤了\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                        console.log('获取到我的帖子：', this.posts.length);\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/home/<USER>/post/${id}`);\n        },\n        editPost(id) {\n            this.$router.push(`/home/<USER>/edit/${id}`);\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const userId = JSON.parse(sessionStorage.getItem('user')).id;\n                this.$del(`/forum/post/${id}`, { operatorId: userId })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchMyPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        },\n        handlePaginationChange() {\n            this.fetchMyPosts();\n        }\n    }\n};\n</script>\n\n<style scoped>\n.my-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/forum"}]}