{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accept.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accept.vue", "mtime": 1746181287829}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Accept.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Accept.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n   <div class=\"content\">\n        <el-alert\n                :title=\"user.role.name + '- - -任务平台共' + tasks.length + '个任务'\"\n                :closable=\"false\"\n                type=\"info\">\n        </el-alert>\n\n        <div>\n            <el-card class=\"box-card\">\n                <div slot=\"header\" class=\"clearfix\">\n                    <span>可接受任务</span>\n                </div>\n\n                <!-- 状态分类标签 -->\n                <div class=\"status-tabs\">\n                    <el-tabs v-model=\"activeStatus\" type=\"card\">\n                        <el-tab-pane\n                            v-for=\"group in tasksByStatus\"\n                            :key=\"group.status\"\n                            :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                            :name=\"group.status.toString()\"\n                        >\n                            <el-card\n                                class=\"box-card\"\n                                v-for=\"item in group.tasks\"\n                                :key=\"item.id\"\n                                style=\"margin-top: 20px\"\n                            >\n                    <div slot=\"header\" class=\"clearfix\"\n                         style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span>\n                            <el-tag type=\"success\" style=\"margin-right: 5px\">待解决</el-tag>\n                            {{item.taskTitle}}\n                            <i class=\"el-icon-money\" style=\"margin-left: 20px;color: red;\"> {{item.reward}}元</i>\n                        </span>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\"\n                                   v-show=\"user.id != item.publish.id\" @click=\"acceptTask(item.id)\">接受任务\n                        </el-button>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\"\n                                   v-show=\"user.id == item.publish.id\">本人任务\n                        </el-button>\n                    </div>\n                    <div class=\"text item\">\n                        <!-- 发布人和任务内容 -->\n                        <p class=\"el-icon-s-custom\">{{item.publish.username}}<span style=\"margin-left: 10px;\">{{item.taskContext}}</span>\n                        </p>\n\n                        <!-- 任务详细信息表格 -->\n                        <div class=\"task-details\">\n                            <table class=\"info-table\">\n                                <tr>\n                                    <th>类别</th>\n                                    <th>子类别</th>\n                                    <th>地址</th>\n                                    <th>详细地址</th>\n                                </tr>\n                                <tr>\n                                    <td>{{item.dept ? item.dept.name : '未知'}}</td>\n                                    <td>{{item.type ? item.type.name : '未知'}}</td>\n                                    <td>{{item.province ? item.province + ' ' + item.city + ' ' + item.district : '未知'}}</td>\n                                    <td>{{item.address || '未知'}}</td>\n                                </tr>\n                            </table>\n                        </div>\n\n                        <span style=\"float: right\">{{item.createTime | formatDate}}</span>\n                    </div>\n                            </el-card>\n\n                            <!-- 当前状态下没有任务时显示 -->\n                            <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                                <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无可接受任务</span>\n                            </div>\n                        </el-tab-pane>\n                    </el-tabs>\n                </div>\n\n                <!-- 没有任何任务时显示 -->\n                <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                    <span><i class=\"el-icon-refresh-right\"></i>暂无可接受任务</span>\n                </div>\n            </el-card>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\";\n    import {formatDate} from '@/util/date';\n\n    export default {\n        name: \"Index\",\n        data() {\n            return {\n                tasks: [],\n                refreshInterval: null,\n                // 当前选中的状态\n                activeStatus: '-1'\n            }\n        },\n\n        methods: {\n            acceptTask(id) {\n                this.$msgbox({\n                    title: '提示',\n                    message: '确定接受此任务吗？',\n                    showCancelButton: true,\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    beforeClose: ((action, instance, done) => {\n                        if (action == 'confirm') {\n                            instance.confirmButtonText = '执行中...';\n                            const data = {\n                                id: id,\n                                acceptId: this.user.id\n                            };\n\n                            this.$put('task/takerAccept', data)\n                                .then((res) => {\n                                    if (res.data.status) {\n                                        this.$msg(res.data.msg, \"success\");\n                                        this.$router.push('/home/<USER>');\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\");\n                                    }\n                                    done();\n                                    instance.confirmButtonText = '确定';\n                                })\n                                .catch(() => {\n                                    this.$msg(\"接受任务失败，请稍后重试\", \"error\");\n                                    done();\n                                    instance.confirmButtonText = '确定';\n                                });\n                        } else {\n                            done();\n                        }\n                    })\n                }).catch(() => {});\n            },\n            loadTasks() {\n                this.$get(\"/task\", { id: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.tasks = res.data.task\n                        } else {\n                            this.$msg(res.data.msg, \"error\")\n                        }\n                    })\n            }\n        },\n\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 过滤出可接受的任务（状态为0的待接单任务）\n            availableTasks() {\n                return this.tasks.filter(task => task.state === 0);\n            },\n\n            // 过滤出当前维修员可接受的任务（匹配类别和子类别）\n            matchingTasks() {\n                if (this.user.role && this.user.role.id === 13) {\n                    return this.availableTasks.filter(task => {\n                        // 检查类别和子类别是否匹配\n                        const deptMatch = !task.deptId || task.deptId === this.user.deptId;\n                        const typeMatch = !task.typeId || task.typeId === this.user.classId;\n                        return deptMatch && typeMatch;\n                    });\n                }\n                return this.availableTasks;\n            },\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    // '0': { name: '待接单', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.availableTasks;\n\n                // // 按状态分组 - 对于Accept页面，我们只关心状态为0的待接单任务\n                // this.availableTasks.forEach(task => {\n                //     statusMap['0'].tasks.push(task);\n                // });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.loadTasks()\n        },\n        mounted() {\n            // 定期刷新任务列表\n            this.refreshInterval = setInterval(() => {\n                this.loadTasks()\n            }, 30000) // 每30秒刷新一次\n        },\n        beforeDestroy() {\n            // 清除定时器\n            if (this.refreshInterval) {\n                clearInterval(this.refreshInterval);\n            }\n        },\n\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n\n        .card h2 {\n            font-weight: normal;\n            font-size: 18px;\n\n            span {\n                font-size: 12px;\n                display: inline-block;\n                border: 1px solid red;\n                padding: 1px 3px;\n            }\n        }\n    }\n\n\n    /deep/ .el-alert--info.is-light {\n        height: 50px;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 130px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n    /deep/ .el-card {\n        margin-bottom: 20px;\n    }\n\n    .task-details {\n        margin: 15px 0;\n\n        .info-table {\n            width: 100%;\n            border-collapse: collapse;\n            border: 1px solid #EBEEF5;\n\n            th, td {\n                padding: 10px;\n                text-align: center;\n                border: 1px solid #EBEEF5;\n            }\n\n            th {\n                background-color: #F5F7FA;\n                color: #606266;\n                font-weight: bold;\n            }\n\n            td {\n                color: #606266;\n            }\n        }\n    }\n\n    .status-tabs {\n        margin-bottom: 20px;\n\n        /deep/ .el-tabs__header {\n            margin-bottom: 15px;\n        }\n\n        /deep/ .el-tabs__item {\n            height: 40px;\n            line-height: 40px;\n            font-size: 14px;\n            color: #606266;\n\n            &.is-active {\n                color: #409EFF;\n                font-weight: bold;\n            }\n        }\n    }\n</style>"]}]}