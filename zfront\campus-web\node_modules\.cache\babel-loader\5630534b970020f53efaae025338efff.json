{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue", "mtime": 1745332269335}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,eAAA;AACA,EAAA,IAAA,EAAA,oBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OADA;AAOA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,OAAA,EAAA,iBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CADA;AAKA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,EAAA;AAAA,UAAA,GAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AALA,OAPA;AAiBA,MAAA,OAAA,EAAA,IAjBA;AAkBA,MAAA,UAAA,EAAA,KAlBA;AAmBA,MAAA,OAAA,EAAA;AAnBA,KAAA;AAqBA,GAxBA;AAyBA,EAAA,OAzBA,qBAyBA;AACA;AACA,QAAA,KAAA,GAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA;;AACA,QAAA,CAAA,KAAA,EAAA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;;AAEA,SAAA,OAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA;AACA,SAAA,eAAA;AACA,GApCA;AAqCA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,6BACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,KAAA,MAAA,CAAA,MAAA,CAAA,EAAA;AAEA,WAAA,IAAA,uBAAA,MAAA,GAAA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA;AACA,YAAA,EAAA,EAAA,IAAA,CAAA,EADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,OAAA,EAAA,IAAA,CAAA,OAHA;AAIA,YAAA,QAAA,EAAA,IAAA,CAAA;AAJA,WAAA,CAFA,CASA;;AACA,cAAA,IAAA,CAAA,QAAA,KAAA,KAAA,CAAA,OAAA,EAAA;AACA,YAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA;;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,IAAA,kCAAA,MAAA;AACA;AACA,SAdA,MAcA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;;AACA,UAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,yBAAA;AACA;AACA,OApBA,EAqBA,KArBA,CAqBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AACA,QAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,yBAAA;AACA,OAzBA,EA0BA,OA1BA,CA0BA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OA5BA;AA6BA,KAlCA;AAmCA,IAAA,UAnCA,wBAmCA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,IAAA,CADA,CAGA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,QAAA,GAAA,MAAA,CAAA,OAAA;;AAEA,UAAA,MAAA,CAAA,IAAA,CAAA,aAAA,EAAA,MAAA,CAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,IAAA,kCAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,aAHA,MAGA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,WARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WAZA,EAaA,OAbA,CAaA,YAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,WAfA;AAgBA;AACA,OAxBA;AAyBA,KA7DA;AA8DA,IAAA,MA9DA,oBA8DA;AACA,WAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA;AAhEA;AArCA,CAAA", "sourcesContent": ["<template>\n    <div class=\"edit-post-container\">\n        <div class=\"page-header\">\n            <h2>编辑帖子</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <el-form v-else :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">保存</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostEdit',\n    data() {\n        return {\n            postForm: {\n                id: null,\n                title: '',\n                content: '',\n                authorId: null\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            loading: true,\n            submitting: false,\n            adminId: null\n        };\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        const post = res.data.post;\n                        this.postForm = {\n                            id: post.id,\n                            title: post.title,\n                            content: post.content,\n                            authorId: post.authorId\n                        };\n                        \n                        // 检查是否是自己的帖子\n                        if (post.authorId !== this.adminId) {\n                            this.$message.warning('您只能编辑自己发布的帖子');\n                            this.$router.push(`/admin/home/<USER>/post/${postId}`);\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                        this.$router.push('/admin/home/<USER>/posts');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                    this.$router.push('/admin/home/<USER>/posts');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    // 确保使用正确的作者ID\n                    this.postForm.authorId = this.adminId;\n                    \n                    this.$put('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('更新成功');\n                                this.$router.push(`/admin/home/<USER>/post/${this.postForm.id}`);\n                            } else {\n                                this.$message.error(res.data.msg || '更新失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('更新帖子失败:', err);\n                            this.$message.error('更新帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.edit-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}