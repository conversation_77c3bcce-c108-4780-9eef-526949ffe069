{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\myremark.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\myremark.vue", "mtime": 1740055088736}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSZW1hcmssIGdldFJlbWFyaywgZGVsUmVtYXJrLCBhZGRSZW1hcmssIHVwZGF0ZVJlbWFyayB9IGZyb20gIkAvYXBpL3JlbWFyay9yZW1hcmsiOwppbXBvcnQge21hcFN0YXRlfSBmcm9tICJ2dWV4IgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZW1hcmsiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAgIC8vIOmBrue9qeWxggogICAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgICAgaWRzOiBbXSwKICAgICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgICBzaW5nbGU6IHRydWUsCiAgICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgICAvLyDmgLvmnaHmlbAKICAgICAgICB0b3RhbDogMCwKICAgICAgICAvLyByZW1hcmvooajmoLzmlbDmja4KICAgICAgICByZW1hcmtMaXN0OiBbXSwKICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgICBzdGFyOiBudWxsLAogICAgICAgICAgICB0YXNrSWQ6IG51bGwsCiAgICAgICAgICAgIGFjY2VwdElkOiBudWxsLAogICAgICAgICAgICBwdWJsaXNoSWQ6IG51bGwKICAgICAgICB9LAogICAgICAgIC8vIOihqOWNleWPguaVsAogICAgICAgIGZvcm06IHt9LAogICAgICAgIC8vIOihqOWNleagoemqjAogICAgICAgIHJ1bGVzOiB7CiAgICAgICAgfSwKICAgICAgICB0YXNrczogW10sCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgICAgICAgICAgLi4ubWFwU3RhdGUoJ3VzZXInLCBbJ3VzZXInXSkKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMucmV0cmlldmVEYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+icmVtYXJr5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0UmVtYXJrKHsicHVibGlzaElkIiA6IHRoaXMudXNlci5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnJlbWFya0xpc3QgPSByZXMuZGF0YS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5Lu75Yqh5YiX6KGoCiAgICByZXRyaWV2ZURhdGEoKSB7CiAgICAgICAgdGhpcy4kZ2V0KCIvdGFzay9wdWJsaXNoZWQiLCB7aWQ6IHRoaXMudXNlci5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMuZGF0YS50YXNrKQogICAgICAgIHRoaXMudGFza3MgPSByZXMuZGF0YS50YXNrCiAgICAgICAgfSkKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgc3RhcjogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwsCiAgICAgICAgdGFza0lkOiBudWxsLAogICAgICAgIGFjY2VwdElkOiBudWxsLAogICAgICAgIHB1Ymxpc2hJZDogbnVsbAogICAgICB9OwogICAgLy8gICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcwogICAgICBnZXRSZW1hcmsoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS5cmVtYXJrIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVSZW1hcmsodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRSZW1hcmsodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5q2k5a2m55SfJywgJ+aPkOekuicsIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgZGVsUmVtYXJrKGlkcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgICAgIH0pOyAgICAgICAgICAKICAgICAgICAgICAgfSk7CiAgICB9LAogICAgLy/lrpjnvZHmj5DkvpvnmoTmlrnms5XvvIzlroPpu5jorqTmnInkuKrlj4LmlbB2YWx1Ze+8jOaJgOiOt+WPluWIsOeahOWwseaYr+aUueWPmOeahOWAvO+8jOeUqOWug+i/m+ihjOWunumZhemcgOaxguaTjeS9nAogICAgY2hhbmdlVmFsdWUodmFsdWUpewogICAgICBjb25zb2xlLmxvZyh2YWx1ZSk7CiAgICB9CiAgICAKICB9Cn07Cg=="}, {"version": 3, "sources": ["myremark.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "myremark.vue", "sourceRoot": "src/views/remark", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"星级\" prop=\"star\">\n        <el-input\n          v-model=\"queryParams.star\"\n          placeholder=\"请输入星级\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务id\" prop=\"taskId\">\n        <el-input\n          v-model=\"queryParams.taskId\"\n          placeholder=\"请输入任务id\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"送货人id\" prop=\"acceptId\">\n        <el-input\n          v-model=\"queryParams.acceptId\"\n          placeholder=\"请输入送货人id\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"评价人id\" prop=\"publishId\">\n        <el-input\n          v-model=\"queryParams.publishId\"\n          placeholder=\"请输入评价人id\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form> -->\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n        >删除</el-button>\n      </el-col>\n      <!-- <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar> -->\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"remarkList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务id\" align=\"center\" prop=\"taskId\" />\n      <el-table-column label=\"送货人id\" align=\"center\" prop=\"acceptId\" />\n\n      <el-table-column label=\"评分\" min-width=\"100\" align=\"center\" prop=\"star\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            show-text>\n          </el-rate>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"remark\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <!-- <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    /> -->\n\n    <!-- 添加或修改remark对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n      \n        <el-form-item label=\"评分\" prop=\"star\">\n            <el-rate\n                v-model=\"form.star\"\n                show-text\n                @change=\"changeValue\">\n            </el-rate>\n        </el-form-item>\n\n        <el-form-item label=\"评价内容\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRemark, getRemark, delRemark, addRemark, updateRemark } from \"@/api/remark/remark\";\nimport {mapState} from \"vuex\"\n\nexport default {\n  name: \"Remark\",\n  data() {\n    return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // remark表格数据\n        remarkList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 查询参数\n        queryParams: {\n            pageNum: 1,\n            pageSize: 10,\n            star: null,\n            taskId: null,\n            acceptId: null,\n            publishId: null\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n        },\n        tasks: [],\n    };\n  },\n  computed: {\n            ...mapState('user', ['user'])\n  },\n  created() {\n    this.getList();\n    this.retrieveData();\n  },\n  methods: {\n    /** 查询remark列表 */\n    getList() {\n      this.loading = true;\n      listRemark({\"publishId\" : this.user.id}).then(res => {\n        this.remarkList = res.data.rows;\n        this.total = res.total;\n        this.loading = false;\n      });\n    },\n    //任务列表\n    retrieveData() {\n        this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n        console.log(res.data.task)\n        this.tasks = res.data.task\n        })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        star: null,\n        remark: null,\n        taskId: null,\n        acceptId: null,\n        publishId: null\n      };\n    //   this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    \n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getRemark(id).then(response => {\n        this.form = response.data.data;\n        this.open = true;\n        this.title = \"修改remark\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateRemark(this.form).then(response => {\n              this.$message(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addRemark(this.form).then(response => {\n              this.$message(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除此学生', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning',\n            }).then(() => {\n            delRemark(ids).then(response => {\n                this.open = false;\n                this.getList();\n            });\n            this.$message({\n                type: 'success',\n                message: '删除成功!'\n                });\n\n            }).catch(() => {\n            this.$message({\n                type: 'info',\n                message: '已取消删除'\n            });          \n            });\n    },\n    //官网提供的方法，它默认有个参数value，所获取到的就是改变的值，用它进行实际需求操作\n    changeValue(value){\n      console.log(value);\n    }\n    \n  }\n};\n</script>\n"]}]}