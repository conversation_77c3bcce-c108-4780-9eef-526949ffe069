{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\CommonPagination.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\CommonPagination.vue", "mtime": 1748720498695}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDb21tb25QYWdpbmF0aW9uJywKICBwcm9wczogewogICAgLy8g5oC75pWw5o2u5p2h5pWwCiAgICB0b3RhbDogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgLy8g5b2T5YmN6aG156CBCiAgICBjdXJyZW50UGFnZTogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDEKICAgIH0sCiAgICAvLyDmr4/pobXmmL7npLrmnaHmlbAKICAgIHBhZ2VTaXplOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogMTAKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNpemVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpwYWdlU2l6ZScsIHZhbCk7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpjdXJyZW50UGFnZScsIDEpOwogICAgICB0aGlzLiRlbWl0KCdwYWdpbmF0aW9uLWNoYW5nZScpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpjdXJyZW50UGFnZScsIHZhbCk7CiAgICAgIHRoaXMuJGVtaXQoJ3BhZ2luYXRpb24tY2hhbmdlJyk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "sources": ["CommonPagination.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA,eAAA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,KAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA,KAFA;AAMA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAPA;AAWA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA;AAZA,GAFA;AAmBA,EAAA,OAAA,EAAA;AACA,IAAA,gBADA,4BACA,GADA,EACA;AACA,WAAA,KAAA,CAAA,iBAAA,EAAA,GAAA;AACA,WAAA,KAAA,CAAA,oBAAA,EAAA,CAAA;AACA,WAAA,KAAA,CAAA,mBAAA;AACA,KALA;AAMA,IAAA,mBANA,+BAMA,GANA,EAMA;AACA,WAAA,KAAA,CAAA,oBAAA,EAAA,GAAA;AACA,WAAA,KAAA,CAAA,mBAAA;AACA;AATA;AAnBA,CAAA", "sourcesContent": ["<template>\r\n    <div class=\"pagination-container\">\r\n        <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"currentPage\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\">\r\n        </el-pagination>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'CommonPagination',\r\n    props: {\r\n        // 总数据条数\r\n        total: {\r\n            type: Number,\r\n            required: true\r\n        },\r\n        // 当前页码\r\n        currentPage: {\r\n            type: Number,\r\n            default: 1\r\n        },\r\n        // 每页显示条数\r\n        pageSize: {\r\n            type: Number,\r\n            default: 10\r\n        }\r\n    },\r\n    methods: {\r\n        handleSizeChange(val) {\r\n            this.$emit('update:pageSize', val);\r\n            this.$emit('update:currentPage', 1);\r\n            this.$emit('pagination-change');\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.$emit('update:currentPage', val);\r\n            this.$emit('pagination-change');\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.pagination-container {\r\n    margin-top: 20px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 20px;\r\n}\r\n</style> "], "sourceRoot": "src/components"}]}