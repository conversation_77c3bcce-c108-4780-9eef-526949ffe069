<template>
    <div class="content">
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>已发布任务</span>
            </div>

            <!-- 状态分类标签 -->
            <div class="status-tabs">
                <el-tabs v-model="activeStatus" type="card">
                    <el-tab-pane
                        v-for="group in tasksByStatus"
                        :key="group.status"
                        :label="group.statusName + ' (' + group.tasks.length + ')'"
                        :name="group.status.toString()"
                    >
                        <el-card
                            class="box-card"
                            v-for="item in group.tasks"
                            :key="item.id"
                            style="margin-top: 20px"
                        >
                <div slot="header" class="clearfix"
                     style="display: flex; align-items: center; justify-content: space-between">
                        <span style="display: flex;align-items: center">
                            <el-tag :type="item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')"
                                    style="margin-right: 5px">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>
                            {{item.taskTitle}}
                        </span>

                    <!-- 评价按钮 -->
                    <el-button v-show="item.state == 2"
                    style="float: right; padding: 3px 0" type="text" @click="remark(item)">订单评价</el-button>

                    <!-- 在线交流按钮 -->
                    <el-button v-show="item.state == 1"
                    style="float: right; padding: 3px 0; margin-right: 10px" type="text" @click="openChat(item)">
                        <i class="el-icon-chat-dot-round"></i> 在线交流
                    </el-button>

                    <el-button style="float: right; padding: 3px 0" type="text" v-show="item.state != 0"
                               @click="receiver(item)">查看维修员信息
                    </el-button>
                    <template>
<!--                        <i class="el-icon-edit" style="cursor: pointer; color: #66b1ff" v-show="item.state == 0"/>-->
                        <el-popconfirm title="确定取消任务吗？" @confirm="cancel(item.id)" v-show="item.state == 0">
                            <el-button style="float: right; padding: 3px 0" type="text" slot="reference">取消任务
                            </el-button>
                        </el-popconfirm>
                    </template>
                </div>

                <el-steps :active="item.state + 1" finish-status="success">
                    <el-step title="发布成功" :description="item.createTime | formatDate"></el-step>
                    <el-step title="服务中" :description="item.orderTime ? transform(item.orderTime):'暂时没人服务'"></el-step>
                    <el-step title="完成时间" :description="item.endTime ? transform(item.endTime):''"></el-step>
                </el-steps>

                <el-collapse style="margin-top: 20px" v-model="activeNames">
                    <el-collapse-item title="任务内容" name="1">
                        <div>{{item.taskContext}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="任务金额" name="2">
                        <div><i class="el-icon-money" style="color: red;"> {{item.reward}}元</i></div>
                    </el-collapse-item>
                    <el-collapse-item title="发布时间" name="3">
                        <div>{{item.createTime | formatDate}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="类别" name="4">
                        <div>{{item.dept ? item.dept.name : '未设置'}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="子类别" name="5">
                        <div>{{item.type ? item.type.name : '未设置'}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="地址" name="6" v-if="item.province">
                        <div>{{item.province}} {{item.city}} {{item.district}}</div>
                    </el-collapse-item>
                    <el-collapse-item title="详细地址" name="7" v-if="item.address">
                        <div>{{item.address}}</div>
                    </el-collapse-item>
                </el-collapse>

                <el-button type="primary" style="float: right;margin:10px 0;" @click="completeTask(item.id)"
                           v-show="item.state==1">完成任务
                </el-button>

                        </el-card>

                        <!-- 当前状态下没有任务时显示 -->
                        <div style="text-align: center; margin-top: 20px;" v-if="group.tasks.length === 0">
                            <span><i class="el-icon-refresh-right"></i>该状态下暂无发布任务</span>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <!-- 没有任何任务时显示 -->
            <div style="text-align: center" v-if="tasks.length === 0">
                <span><i class="el-icon-refresh-right"></i>暂无发布任务</span>
            </div>
        </el-card>

        <el-drawer
                title="维修员信息"
                :visible.sync="drawer"
                direction="rtl">
            <div class="content_drawer">
                <el-card class="box-card" v-if="recipientInformation != ''">
                    <el-collapse v-model="drawerNames">
                        <el-collapse-item title="姓名" name="1">
                            <div>{{recipientInformation.username}}</div>
                        </el-collapse-item>
                        <el-collapse-item title="电话" name="2">
                            <div>{{recipientInformation.phone}}</div>
                        </el-collapse-item>
                        <el-collapse-item title="角色" name="3">
                            <div>{{recipientInformation.role.name}}</div>
                        </el-collapse-item>
                        <el-collapse-item title="类别" name="4">
                            <div>{{recipientInformation.dept.name}}</div>
                        </el-collapse-item>
                        <el-collapse-item title="子类别" name="5">
                            <div>{{recipientInformation.type.name}}</div>
                        </el-collapse-item>
                    </el-collapse>
                </el-card>
            </div>
        </el-drawer>

        <!-- 添加或修改remark对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form"  :rules="rules" label-width="80px" >
            <el-form-item label="星级" prop="star">
                <el-rate
                    v-model="form.star"
                    show-text>
                </el-rate>
            </el-form-item>
            <el-form-item label="评价内容" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入评价内容" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="exit">取 消</el-button>
        </div>
        </el-dialog>

        <!-- 聊天组件 -->
        <Chat
            :visible.sync="chatVisible"
            :task="currentChatTask"
            :current-user-id="user.id"
            v-if="currentChatTask" />
    </div>
</template>

<script>
    import {mapState} from "vuex"
    import {formatDate} from '@/util/date';
    import { addRemark, } from "@/api/remark/remark";
    import Chat from '@/components/Chat.vue';

    export default {
        name: "Published",
        components: {
            Chat
        },
        data() {
            return {
                // 是否显示弹出层
                open: false,
                activeNames: ['1', '2', '3', '4', '5', '6', '7'],
                drawerNames: ['1', '2', '3', '4', '5'],
                tasks: [],
                drawer: false,
                recipientInformation: [],
                // 当前选中的状态
                activeStatus: '-1',
                // 表单参数
                form: {},
                // 表单校验
                rules: {
                },
                // 弹出层标题
                title: "",
                // 当前选中的任务
                currentTask: null,
                // 聊天相关
                chatVisible: false,
                currentChatTask: null,
            };
        },
        computed: {
            ...mapState('user', ['user']),

            // 按状态分组的任务
            tasksByStatus() {
                // 定义状态映射
                const statusMap = {
                    '-1': { name: '全部', tasks: [] },
                    '0': { name: '待接单', tasks: [] },
                    '1': { name: '服务中', tasks: [] },
                    '2': { name: '已完成', tasks: [] }
                };

                // 添加"全部"分类
                statusMap['-1'].tasks = this.tasks;

                // 按状态分组
                this.tasks.forEach(task => {
                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '0';
                    if (statusMap[state]) {
                        statusMap[state].tasks.push(task);
                    }
                });

                // 转换为数组格式，方便在模板中使用
                return Object.entries(statusMap).map(([status, data]) => ({
                    status: parseInt(status),
                    statusName: data.name,
                    tasks: data.tasks
                }));
            }
        },
        created() {
            this.retrieveData()
        },
        methods: {
            retrieveData() {
                this.$get("/task/published", {id: this.user.id}).then(res => {
                    console.log(res.data.task)
                    this.tasks = res.data.task
                })
            },
            receiver(val) {
                console.log(val)
                this.recipientInformation = val.accept;
                // console.log(this.recipientInformation)
                this.drawer = true
            },

            transform(time) {
                let date = new Date(time);
                return formatDate(date, 'yyyy-MM-dd hh:mm');
            },

            cancel(id) {
                this.$del("/task/" + id)
                    .then(res => {
                        this.retrieveData()
                        this.$notifyMsg('成功', res.data.msg, "success");
                    })
            },
            completeTask(id) {
                this.$msgbox({
                    title: '提示',
                    message: '确定该维修员完成此任务了吗？',
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    beforeClose: ((action, instance, done) => {
                        if (action == 'confirm') {
                            // instance.confirmButtonLoading = true;
                            instance.confirmButtonText = '执行中...';
                            this.$put('task/' + id)
                                .then((res) => {
                                    done();
                                    instance.confirmButtonLoading = false;
                                    this.$msg(res.data.msg, "success");
                                    this.retrieveData();

                                    // 如果返回了更新后的用户信息，更新当前用户数据
                                    if (res.data.data && res.data.data.id) {
                                        // 检查返回的用户是否是当前登录用户
                                        if (res.data.data.id === this.user.id) {
                                            console.log('更新当前用户余额信息:', res.data.data);
                                            this.$store.commit('user/setUser', res.data.data);
                                        }
                                    }
                                })
                        } else {
                            done();
                        }
                    })
                }).catch(() => {
                })
            },
            // 评价用户
            remark(task){
                this.currentTask = task;
                this.open = true;
                this.title = "添加评价";
            },

            /** 提交按钮 */
            submitForm() {
                if(this.form.star==null){
                    this.$message("请输入星级");
                    return;
                }

                if(this.form.remark==null || this.form.remark.trim() === ''){
                    this.$message("请输入评价内容");
                    return;
                }

                if(!this.currentTask || !this.currentTask.accept){
                    this.$message.error("任务信息不完整，无法提交评价");
                    return;
                }

                const aid = this.currentTask.accept.id;
                const taskid = this.currentTask.id;
                const pid = this.currentTask.publish.id;

                console.log('提交评价:', aid, taskid, pid);

                addRemark({
                    "star": this.form.star,
                    "remark": this.form.remark,
                    "acceptId": aid,
                    "publishId": pid,
                    "taskId": taskid,
                }).then(() => {
                    this.$message.success("评价提交成功");
                    this.open = false;
                    this.reset();
                    this.retrieveData(); // 刷新任务列表
                }).catch(error => {
                    console.error('评价提交失败:', error);
                    this.$message.error("评价提交失败，请稍后重试");
                });
            },

            // 取消按钮
            exit() {
                this.open = false;
                this.reset();
                this.currentTask = null;
            },

            // 表单重置
            reset() {
                this.form = {
                    id: null,
                    star: null,
                    remark: null,
                };
            },

            /**
             * 打开在线交流
             * @param {Object} task 任务对象
             */
            openChat(task) {
                this.currentChatTask = task;
                this.chatVisible = true;
            },
        },
        filters: {
            formatDate(time) {
                let date = new Date(time);
                return formatDate(date, 'yyyy-MM-dd hh:mm');
            }
        }
    }
</script>

<style scoped lang="less">
    .content {
        background: #FFf;
        margin: 0 15px;
        padding: 15px;
    }

    .status-tabs {
        margin-bottom: 20px;

        /deep/ .el-tabs__header {
            margin-bottom: 15px;
        }

        /deep/ .el-tabs__item {
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            color: #606266;

            &.is-active {
                color: #409EFF;
                font-weight: bold;
            }
        }
    }
</style>
