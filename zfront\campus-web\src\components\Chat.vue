<template>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="visible"
        width="600px"
        :before-close="handleClose"
        class="chat-dialog">
        
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages" ref="messagesContainer">
                <div 
                    v-for="message in messages" 
                    :key="message.id"
                    :class="['message-item', message.senderId === currentUserId ? 'sent' : 'received']">
                    
                    <div class="message-info">
                        <span class="sender-name">
                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}
                        </span>
                        <span class="send-time">{{ formatTime(message.sendTime) }}</span>
                    </div>
                    
                    <div class="message-content">
                        {{ message.content }}
                    </div>
                </div>
                
                <!-- 无消息提示 -->
                <div v-if="messages.length === 0" class="no-messages">
                    <i class="el-icon-chat-dot-round"></i>
                    <p>开始聊天吧！</p>
                </div>
            </div>
            
            <!-- 消息输入区域 -->
            <div class="chat-input">
                <el-input
                    v-model="newMessage"
                    type="textarea"
                    :rows="3"
                    placeholder="输入消息..."
                    @keydown.enter.native="handleEnterKey"
                    :disabled="!connected">
                </el-input>
                
                <div class="input-actions">
                    <span v-if="!connected" class="connection-status">
                        <i class="el-icon-loading"></i> 连接中...
                    </span>
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="sendMessage"
                        :disabled="!newMessage.trim() || !connected">
                        发送
                    </el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

export default {
    name: 'Chat',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        task: {
            type: Object,
            required: true
        },
        currentUserId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            stompClient: null,
            connected: false,
            messages: [],
            newMessage: '',
            subscription: null
        }
    },
    computed: {
        dialogTitle() {
            if (!this.task) return '在线交流'
            return `任务交流 - ${this.task.taskTitle}`
        },
        
        // 获取对方用户ID
        otherUserId() {
            if (!this.task) return null
            
            // 如果当前用户是发布者，对方就是接受者
            if (this.currentUserId === this.task.publishId) {
                return this.task.acceptId
            }
            // 如果当前用户是接受者，对方就是发布者
            else if (this.currentUserId === this.task.acceptId) {
                return this.task.publishId
            }
            
            return null
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.openChat()
            } else {
                this.closeChat()
            }
        }
    },
    methods: {
        // 打开聊天
        openChat() {
            this.connectWebSocket()
            this.loadChatHistory()
        },
        
        // 关闭聊天
        closeChat() {
            this.disconnectWebSocket()
            this.messages = []
            this.newMessage = ''
        },
        
        // 连接WebSocket
        connectWebSocket() {
            if (this.stompClient && this.connected) {
                return
            }

            console.log('开始连接WebSocket...')

            try {
                // 添加更详细的连接信息
                const wsUrl = '/ws'
                console.log('WebSocket连接地址:', wsUrl)

                const socket = new SockJS(wsUrl)
                this.stompClient = Stomp.over(socket)

                // 设置心跳
                this.stompClient.heartbeat.outgoing = 20000
                this.stompClient.heartbeat.incoming = 20000

                // 启用调试日志
                this.stompClient.debug = function(str) {
                    console.log('STOMP: ' + str)
                }

                // 设置连接超时
                const connectPromise = new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('连接超时'))
                    }, 10000) // 10秒超时

                    this.stompClient.connect({},
                        (frame) => {
                            clearTimeout(timeout)
                            console.log('WebSocket连接成功:', frame)
                            this.connected = true

                            // 订阅个人消息队列
                            const subscriptionUrl = `/user/${this.currentUserId}/queue/messages`
                            console.log('订阅消息队列:', subscriptionUrl)

                            this.subscription = this.stompClient.subscribe(
                                subscriptionUrl,
                                (message) => {
                                    console.log('收到WebSocket消息:', message)
                                    try {
                                        const chatMessage = JSON.parse(message.body)
                                        this.handleReceivedMessage(chatMessage)
                                    } catch (e) {
                                        console.error('解析消息失败:', e, message.body)
                                    }
                                }
                            )

                            // 发送加入聊天的消息
                            const joinMessage = {
                                senderId: this.currentUserId,
                                taskId: this.task.id
                            }
                            console.log('发送加入聊天消息:', joinMessage)

                            this.stompClient.send('/app/chat.addUser', {}, JSON.stringify(joinMessage))
                            resolve()
                        },
                        (error) => {
                            clearTimeout(timeout)
                            console.error('WebSocket连接失败:', error)
                            this.connected = false
                            reject(error)
                        }
                    )
                })

                connectPromise.catch((error) => {
                    console.error('WebSocket连接异常:', error)
                    this.$message.error('连接聊天服务失败，请稍后重试')
                })

            } catch (error) {
                console.error('创建WebSocket连接时发生错误:', error)
                this.$message.error('无法建立聊天连接')
            }
        },
        
        // 断开WebSocket连接
        disconnectWebSocket() {
            if (this.subscription) {
                this.subscription.unsubscribe()
                this.subscription = null
            }
            
            if (this.stompClient && this.connected) {
                this.stompClient.disconnect(() => {
                    console.log('WebSocket连接已断开')
                })
            }
            
            this.stompClient = null
            this.connected = false
        },
        
        // 发送消息
        sendMessage() {
            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {
                return
            }
            
            const message = {
                taskId: this.task.id,
                senderId: this.currentUserId,
                receiverId: this.otherUserId,
                content: this.newMessage.trim(),
                messageType: 'text'
            }
            
            try {
                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))
                this.newMessage = ''
            } catch (error) {
                console.error('发送消息失败:', error)
                this.$message.error('发送消息失败，请重试')
            }
        },
        
        // 处理接收到的消息
        handleReceivedMessage(message) {
            console.log('收到消息:', message)

            // 检查消息是否属于当前任务
            if (message.taskId === this.task.id) {
                // 避免重复添加消息（如果消息有ID的话）
                let exists = false
                if (message.id) {
                    exists = this.messages.find(m => m.id === message.id)
                }

                if (!exists) {
                    // 确保消息有必要的字段
                    if (!message.sendTime) {
                        message.sendTime = new Date()
                    }

                    this.messages.push(message)
                    console.log('消息已添加到列表，当前消息数量:', this.messages.length)

                    this.$nextTick(() => {
                        this.scrollToBottom()
                    })
                }
            }
        },
        
        // 加载聊天历史记录
        loadChatHistory() {
            this.$get('/api/chat/history', {
                taskId: this.task.id,
                userId: this.currentUserId
            }).then(res => {
                if (res.data.status) {
                    this.messages = res.data.messages || []
                    this.$nextTick(() => {
                        this.scrollToBottom()
                    })
                } else {
                    console.error('加载聊天记录失败:', res.data.msg)
                }
            }).catch(error => {
                console.error('加载聊天记录失败:', error)
            })
        },
        
        // 滚动到底部
        scrollToBottom() {
            const container = this.$refs.messagesContainer
            if (container) {
                container.scrollTop = container.scrollHeight
            }
        },
        
        // 处理Enter键
        handleEnterKey(event) {
            if (event.ctrlKey || event.shiftKey) {
                // Ctrl+Enter 或 Shift+Enter 换行
                return
            } else {
                // 单独Enter发送消息
                event.preventDefault()
                this.sendMessage()
            }
        },
        
        // 格式化时间
        formatTime(time) {
            if (!time) return ''
            
            const date = new Date(time)
            const now = new Date()
            const diff = now - date
            
            // 如果是今天
            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
                return date.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                })
            }
            
            // 如果是昨天或更早
            return date.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            })
        },
        
        // 处理对话框关闭
        handleClose() {
            this.$emit('update:visible', false)
        }
    },
    
    beforeDestroy() {
        this.disconnectWebSocket()
    }
}
</script>

<style scoped lang="less">
.chat-dialog {
    /deep/ .el-dialog__body {
        padding: 0;
    }
}

.chat-container {
    height: 500px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;

    .message-item {
        margin-bottom: 15px;

        &.sent {
            text-align: right;

            .message-info {
                justify-content: flex-end;
            }

            .message-content {
                background-color: #409EFF;
                color: white;
                margin-left: auto;
                margin-right: 0;
            }
        }

        &.received {
            text-align: left;

            .message-info {
                justify-content: flex-start;
            }

            .message-content {
                background-color: white;
                color: #303133;
                margin-left: 0;
                margin-right: auto;
            }
        }
    }

    .message-info {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        font-size: 12px;
        color: #909399;

        .sender-name {
            font-weight: bold;
            margin-right: 8px;
        }

        .send-time {
            font-size: 11px;
        }
    }

    .message-content {
        display: inline-block;
        max-width: 70%;
        padding: 10px 15px;
        border-radius: 10px;
        word-wrap: break-word;
        white-space: pre-wrap;
        line-height: 1.4;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .no-messages {
        text-align: center;
        color: #909399;
        margin-top: 50px;

        i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        p {
            font-size: 16px;
            margin: 0;
        }
    }
}

.chat-input {
    padding: 15px;
    background-color: white;

    .input-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .connection-status {
            color: #909399;
            font-size: 12px;

            i {
                margin-right: 5px;
            }
        }
    }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
