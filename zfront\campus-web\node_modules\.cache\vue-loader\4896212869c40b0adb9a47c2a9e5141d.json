{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\advise.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\advise.vue", "mtime": 1737774014078}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RBZHZpc2UsIGdldEFkdmlzZSwgZGVsQWR2aXNlLCBhZGRBZHZpc2UsIHVwZGF0ZUFkdmlzZSB9IGZyb20gIkAvYXBpL25vdGljZS9hZHZpc2UiOwppbXBvcnQge2Zvcm1hdERhdGV9IGZyb20gJ0AvdXRpbC9kYXRlJzsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQWR2aXNlIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOmAmuefpeWFrOWRiuWFrOWRiuihqOagvOaVsOaNrgogICAgICBhZHZpc2VMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBub3RpY2VUaXRsZTogbnVsbCwKICAgICAgICBub3RpY2VDb250ZW50OiBudWxsLAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIG5vdGljZVRpdGxlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5ZGK5qCH6aKY5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoumAmuefpeWFrOWRiuWFrOWRiuWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdEFkdmlzZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmFkdmlzZUxpc3QgPSByZXNwb25zZS5kYXRhLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDpgJrnn6XlhazlkYoiOwogICAgfSwKCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3Qgbm90aWNlSWQgPSByb3cubm90aWNlSWQgfHwgdGhpcy5pZHMKICAgICAgZ2V0QWR2aXNlKG5vdGljZUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuemAmuefpeWFrOWRiuWFrOWRiiI7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5ub3RpY2VJZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZUFkdmlzZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZEFkdmlzZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3Qgbm90aWNlSWRzID0gcm93Lm5vdGljZUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmraTlrabnlJ8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIGRlbEFkdmlzZShub3RpY2VJZHMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgICB9KTsgICAgICAgICAgCiAgICAgICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgbm90aWNlSWQ6IG51bGwsCiAgICAgICAgbm90aWNlVGl0bGU6IG51bGwsCiAgICAgICAgbm90aWNlQ29udGVudDogbnVsbCwKICAgICAgICBjcmVhdGVCeTogbnVsbCwKICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICAvLyB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5ub3RpY2VJZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICB0cmFuc2Zvcm0odGltZSkgewogICAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUodGltZSk7CiAgICAgICAgICAgICAgcmV0dXJuIGZvcm1hdERhdGUoZGF0ZSwgJ3l5eXktTU0tZGQgaGg6bW0nKTsKICAgIH0KICB9LAoKfTsK"}, {"version": 3, "sources": ["advise.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "advise.vue", "sourceRoot": "src/views/notice", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n        <el-input\n          v-model=\"queryParams.noticeTitle\"\n          placeholder=\"请输入公告标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n        <el-input\n          v-model=\"queryParams.noticeContent\"\n          placeholder=\"请输入公告内容\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n        >发布公告</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n        >删除</el-button>\n      </el-col>\n      <!-- <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar> -->\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"adviseList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n      <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n      <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ transform(scope.row.createTime) }}</span>\n        </template> \n      </el-table-column>\n\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    /> -->\n\n    <!-- 添加或修改通知公告公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"743px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n          <el-input v-model=\"form.noticeTitle\" placeholder=\"请输入公告标题\" />\n        </el-form-item>\n        <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n          <el-input\n                        resize=\"none\"\n                        type=\"textarea\"\n                        :autosize=\"{ minRows: 6, maxRows: 10}\"\n                        placeholder=\"请输入公告内容\"\n                        v-model=\"form.noticeContent\" style=\"padding: 0\">\n          </el-input>\n          <!-- <quill-editor v-model=\"form.noticeContent\" placeholder=\"请输入公告内容\" /> -->\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n        <el-form-item label=\"创建者\" prop=\"remark\">\n          <el-input v-model=\"form.createBy\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listAdvise, getAdvise, delAdvise, addAdvise, updateAdvise } from \"@/api/notice/advise\";\n  import {formatDate} from '@/util/date';\n\n  export default {\n    name: \"Advise\",\n    data() {\n      return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 通知公告公告表格数据\n        adviseList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          noticeTitle: null,\n          noticeContent: null,\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n          noticeTitle: [\n            { required: true, message: \"公告标题不能为空\", trigger: \"blur\" }\n          ],\n        }\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询通知公告公告列表 */\n      getList() {\n        this.loading = true;\n        listAdvise(this.queryParams).then(response => {\n          this.adviseList = response.data.rows;\n          this.total = response.total;\n          this.loading = false;\n        });\n      },\n      /** 新增按钮操作 */\n      handleAdd() {\n        this.reset();\n        this.open = true;\n        this.title = \"添加通知公告\";\n      },\n\n      /** 修改按钮操作 */\n      handleUpdate(row) {\n        this.reset();\n        const noticeId = row.noticeId || this.ids\n        getAdvise(noticeId).then(response => {\n          this.form = response.data.data;\n          this.open = true;\n          this.title = \"修改通知公告公告\";\n        });\n      },\n\n      /** 提交按钮 */\n      submitForm() {\n        this.$refs[\"form\"].validate(valid => {\n          if (valid) {\n            if (this.form.noticeId != null) {\n              updateAdvise(this.form).then(response => {\n                this.$message(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n            } else {\n              addAdvise(this.form).then(response => {\n                this.$message(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n            }\n          }\n        });\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const noticeIds = row.noticeId || this.ids;\n        this.$confirm('是否确认删除此学生', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning',\n            }).then(() => {\n            delAdvise(noticeIds).then(response => {\n                this.open = false;\n                this.getList();\n            });\n            this.$message({\n                type: 'success',\n                message: '删除成功!'\n                });\n\n            }).catch(() => {\n            this.$message({\n                type: 'info',\n                message: '已取消删除'\n            });          \n            });\n      },\n      // 取消按钮\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      // 表单重置\n      reset() {\n        this.form = {\n          noticeId: null,\n          noticeTitle: null,\n          noticeContent: null,\n          createBy: null,\n          createTime: null,\n          remark: null\n        };\n        // this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      // 多选框选中数据\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.noticeId)\n        this.single = selection.length!==1\n        this.multiple = !selection.length\n      },\n      transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    },\n\n  };\n</script>\n"]}]}