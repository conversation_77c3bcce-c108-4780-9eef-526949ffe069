<template>
    <div class="content">
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>已接受任务</span>
            </div>

            <!-- 状态分类标签 -->
            <div class="status-tabs">
                <el-tabs v-model="activeStatus" type="card">
                    <el-tab-pane
                        v-for="group in tasksByStatus"
                        :key="group.status"
                        :label="group.statusName + ' (' + group.tasks.length + ')'"
                        :name="group.status.toString()"
                    >
                        <el-card
                            class="box-card"
                            v-for="item in group.tasks"
                            :key="item.id"
                            style="margin-top: 20px"
                        >
                <div slot="header" class="clearfix"
                     style="display: flex; align-items: center; justify-content: space-between">
                        <span style="display: flex;align-items: center">
                            <el-tag v-if="item" :type="item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')"
                                    style="margin-right: 5px">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>
                            任务名称：{{item ? item.taskTitle : ''}}
                        </span>
                    <!--                    <el-button style="float: right; padding: 3px 0" type="text">查看接受人信息</el-button>-->
                    <div>
                        <!-- 已完成任务显示查看评价按钮 -->
                        <el-button
                            style="float: right; padding: 3px 0"
                            type="text"
                            v-show="item && item.state == 2"
                            @click="viewRemark(item)">查看评价
                        </el-button>

                        <!-- 在线交流按钮 - 服务中的任务显示 -->
                        <el-button v-show="item && item.state == 1"
                        style="float: right; padding: 3px 0; margin-right: 10px" type="text" @click="openChat(item)">
                            <i class="el-icon-chat-dot-round"></i> 在线交流
                        </el-button>

                        <!-- 未完成任务显示取消任务按钮 -->
                        <el-popconfirm title="确定取消任务吗" @confirm="del(item.id)">
                            <el-button style="float: right; padding: 3px 0" type="text" slot="reference"
                                       v-show="item && item.state != 2">取消任务
                            </el-button>
                        </el-popconfirm>
                    </div>
                </div>
                <div>
                    任务内容：{{item ? item.taskContext : ''}}
                </div>
                <el-collapse style="margin-top: 20px" v-model="activeNames">
                    <el-collapse-item title="用户信息" name="1">
                        <el-card class="box-card" v-if="item && item.publish">
                            <div slot="header" class="clearfix">
                                <span>用户姓名：{{item.publish.username || '未知用户'}}</span>
                                <!-- <el-button style="float: right; padding: 3px 0" type="text">投诉</el-button> -->
                            </div>

                            <div class="box_center">
                                <table cellspacing="0">
                                    <tr>
                                        <th>电话</th>
                                        <th>角色</th>
                                        <th>类别</th>
                                        <th>子类别</th>
                                        <th>任务金额</th>
                                        <th>接受任务时间</th>
                                    </tr>
                                    <tr align="center">
                                        <td>{{item.publish.phone || '无'}}</td>
                                        <td>{{item.publish.role ? item.publish.role.name : '无'}}</td>
                                        <td>{{item.dept ? item.dept.name : '无'}}</td>
                                        <td>{{item.type ? item.type.name : '无'}}</td>
                                        <td><i class="el-icon-money" style="color: red;">{{item.reward || 0}}元</i></td>
                                        <td>
                                            <!-- 添加调试信息 -->
                                            <span v-if="item.orderTime">{{item.orderTime | formatDate}}</span>
                                            <span v-else>无时间数据</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </el-card>
                        <el-card class="box-card" v-else>
                            <div class="box_center">
                                <p>暂无发布人信息</p>
                            </div>
                        </el-card>
                    </el-collapse-item>
                    <el-collapse-item title="查看钱款" name="2" v-if="item && item.state == 2">
                        <el-card class="box-card">
                            <div>
                                <p>实收款：<i class="el-icon-money" style="color: red;">{{item.reward || 0}}元</i></p>
                                <p>到款时间：
                                    <span v-if="item.endTime">{{item.endTime | formatDate}}</span>
                                    <span v-else>无</span>
                                </p>
                            </div>
                        </el-card>
                    </el-collapse-item>
                </el-collapse>
                        </el-card>

                        <!-- 当前状态下没有任务时显示 -->
                        <div style="text-align: center; margin-top: 20px;" v-if="group.tasks.length === 0">
                            <span><i class="el-icon-refresh-right"></i>该状态下暂无已接受任务</span>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <!-- 没有任何任务时显示 -->
            <div style="text-align: center" v-if="tasks.length === 0">
                <span><i class="el-icon-refresh-right"></i>暂无已接受任务</span>
            </div>
        </el-card>

        <!-- 评价对话框 -->
        <el-dialog
            :title="currentTask ? '任务「' + currentTask.taskTitle + '」的评价' : '任务评价'"
            :visible.sync="remarkDialogVisible"
            width="550px"
            center>
            <div v-if="currentRemark">
                <div class="remark-info">
                    <div class="task-info" v-if="currentTask">
                        <div class="task-header">
                            <i class="el-icon-info-circle"></i> 任务信息
                        </div>
                        <div class="task-details">
                            <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>
                            <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>
                            <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>
                        </div>
                    </div>

                    <div class="star-rating">
                        <span class="rating-label">评分：</span>
                        <el-rate
                            v-model="currentRemark.star"
                            disabled
                            show-score
                            text-color="#ff9900">
                        </el-rate>
                    </div>
                    <div class="remark-content">
                        <span class="content-label">评价内容：</span>
                        <p>{{currentRemark.remark}}</p>
                    </div>
                    <div class="remark-time" v-if="currentRemark.createTime">
                        <span>评价时间：{{currentRemark.createTime | formatDate}}</span>
                    </div>
                </div>
            </div>
            <div v-else class="no-remark">
                <i class="el-icon-warning-outline"></i>
                <p>用户尚未评价此任务</p>
                <div class="task-info" v-if="currentTask">
                    <div class="task-header">
                        <i class="el-icon-info-circle"></i> 任务信息
                    </div>
                    <div class="task-details">
                        <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>
                        <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>
                        <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="remarkDialogVisible = false">关 闭</el-button>
            </span>
        </el-dialog>

        <!-- 聊天组件 -->
        <Chat
            :visible.sync="chatVisible"
            :task="currentChatTask"
            :current-user-id="user.id"
            v-if="currentChatTask" />
    </div>
</template>

<script>
    import {mapState} from "vuex";
    import Chat from '@/components/Chat.vue';
    // 不再使用导入的formatDate函数，因为我们直接在过滤器中实现了日期格式化

    export default {
        name: "Accepted",
        components: {
            Chat
        },
        data() {
            return {
                activeNames: ['1'],
                tasks: [],
                // 当前选中的状态
                activeStatus: '-1',
                // drawer:false,

                // 评价相关
                remarkDialogVisible: false,
                currentRemark: null,
                currentTask: null,

                // 聊天相关
                chatVisible: false,
                currentChatTask: null
            };
        },
        computed: {
            ...mapState('user', ['user']),

            // 按状态分组的任务
            tasksByStatus() {
                // 定义状态映射 - 已接受任务只有服务中和已完成两种状态
                const statusMap = {
                    '-1': { name: '全部', tasks: [] },
                    '1': { name: '服务中', tasks: [] },
                    '2': { name: '已完成', tasks: [] }
                };

                // 添加"全部"分类
                statusMap['-1'].tasks = this.tasks;

                // 按状态分组
                this.tasks.forEach(task => {
                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '1';
                    if (statusMap[state]) {
                        statusMap[state].tasks.push(task);
                    }
                });

                // 转换为数组格式，方便在模板中使用
                return Object.entries(statusMap).map(([status, data]) => ({
                    status: parseInt(status),
                    statusName: data.name,
                    tasks: data.tasks
                }));
            }
        },
        created() {
            this.newList()
        },
        methods: {
            del(id) {
                this.$put('/task/takerCancel/' + id)
                .then(() => {
                    this.$notifyMsg('成功', '取消任务成功', 'success')
                    this.newList()
                })
            },
            newList() {
                this.$get("/task/accepted", {id: this.user.id})
                .then(res => {
                    // 确保每个任务对象都有必要的属性
                    if (res.data.task && Array.isArray(res.data.task)) {
                        // 过滤掉状态为0（待接单）的任务，因为已接受任务不应该包含待接单状态
                        this.tasks = res.data.task
                            .filter(task => task.state !== 0)
                            .map(task => {
                                // 确保publish对象存在
                                if (!task.publish) {
                                    task.publish = {};
                                }

                                // 确保publish.role对象存在
                                if (!task.publish.role) {
                                    task.publish.role = { name: '未知' };
                                }

                                // 确保publish.dept对象存在
                                if (!task.publish.dept) {
                                    task.publish.dept = { name: '未知' };
                                }

                                // 确保publish.type对象存在
                                if (!task.publish.type) {
                                    task.publish.type = { name: '未知' };
                                }

                                // 打印orderTime以便调试
                                console.log('任务ID:', task.id, '接受时间:', task.orderTime, '状态:', task.state);

                                return task;
                            });
                    } else {
                        this.tasks = [];
                    }
                    console.log('获取到的任务列表:', this.tasks);
                })
                .catch(error => {
                    console.error('获取任务列表失败:', error);
                    this.tasks = [];
                });
            },

            acceptTask(id) {
                this.$put('task/takerAccept', { id: id }) // 仅传递任务ID
                .then((res) => {
                    if (res.data.status) {
                        this.$msg(res.data.msg, "success");
                    } else {
                        console.error('错误详情:', res.data.msg);
                    }
                });
            },

            /**
             * 查看任务评价
             * @param {Object} task 任务对象
             */
            viewRemark(task) {
                // 直接显示对话框，不需要确认
                this.currentTask = task;
                this.remarkDialogVisible = true;

                // 根据任务ID查询评价信息
                this.$get('/student/remark/list', { taskId: task.id })
                .then(res => {
                    console.log('评价查询结果:', res);
                    if (res.data && res.data.rows && res.data.rows.length > 0) {
                        // 找到评价
                        this.currentRemark = res.data.rows[0];
                    } else {
                        // 没有评价
                        this.currentRemark = null;
                    }
                })
                .catch(error => {
                    console.error('查询评价失败:', error);
                    this.currentRemark = null;
                    this.$message.error('查询评价信息失败，请稍后重试');
                });
            },

            /**
             * 打开在线交流
             * @param {Object} task 任务对象
             */
            openChat(task) {
                this.currentChatTask = task;
                this.chatVisible = true;
            }
        },

        filters: {
            formatDate(time) {
                if (!time) return '无';
                try {
                    // 直接使用字符串格式化
                    const date = new Date(time);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');

                    return `${year}-${month}-${day} ${hours}:${minutes}`;
                } catch (e) {
                    console.error('日期格式化错误:', e, time);
                    return '格式错误';
                }
            }
        }
    }
</script>

<style scoped lang="less">
    .content {
        background: #FFf;
        margin: 0 15px;
        padding: 15px;

        .box_center table {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
        }

        .box_center th, td {
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #ebeef5;
        }

        .box_center th {
            background-color: #F5F7FA;
            color: #606266;
            font-weight: bold;
        }

        .status-tabs {
            margin-bottom: 20px;

            /deep/ .el-tabs__header {
                margin-bottom: 15px;
            }

            /deep/ .el-tabs__item {
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                color: #606266;

                &.is-active {
                    color: #409EFF;
                    font-weight: bold;
                }
            }
        }
    }

    /* 评价对话框样式 */
    .remark-info {
        padding: 15px;

        .task-info {
            margin-bottom: 20px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;

            .task-header {
                background-color: #f5f7fa;
                padding: 10px 15px;
                font-weight: bold;
                color: #606266;
                border-bottom: 1px solid #ebeef5;

                i {
                    color: #409EFF;
                    margin-right: 5px;
                }
            }

            .task-details {
                padding: 15px;

                p {
                    margin: 5px 0;
                    line-height: 1.6;

                    span {
                        font-weight: bold;
                        margin-right: 5px;
                        color: #606266;
                    }
                }
            }
        }

        .star-rating {
            margin-bottom: 15px;
            display: flex;
            align-items: center;

            .rating-label {
                font-weight: bold;
                margin-right: 10px;
                min-width: 70px;
                color: #606266;
            }
        }

        .remark-content {
            margin-bottom: 15px;

            .content-label {
                font-weight: bold;
                display: block;
                margin-bottom: 10px;
                color: #606266;
            }

            p {
                background-color: #f5f7fa;
                padding: 15px;
                border-radius: 4px;
                margin: 0;
                line-height: 1.6;
            }
        }

        .remark-time {
            text-align: right;
            color: #909399;
            font-size: 12px;
            margin-top: 10px;
        }
    }

    .no-remark {
        text-align: center;
        padding: 20px 0;
        color: #909399;

        i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        p {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .task-info {
            margin-top: 20px;
            text-align: left;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;

            .task-header {
                background-color: #f5f7fa;
                padding: 10px 15px;
                font-weight: bold;
                color: #606266;
                border-bottom: 1px solid #ebeef5;

                i {
                    font-size: 14px;
                    display: inline;
                    color: #409EFF;
                    margin-right: 5px;
                    margin-bottom: 0;
                }
            }

            .task-details {
                padding: 15px;

                p {
                    margin: 5px 0;
                    line-height: 1.6;
                    font-size: 14px;

                    span {
                        font-weight: bold;
                        margin-right: 5px;
                        color: #606266;
                    }
                }
            }
        }
    }
</style>