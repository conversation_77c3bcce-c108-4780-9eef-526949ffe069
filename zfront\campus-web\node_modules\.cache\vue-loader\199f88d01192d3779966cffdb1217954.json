{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue", "mtime": 1748720501299}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PostList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PostList.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"forum-container\">\n        <div class=\"forum-header\">\n            <h2>维修员论坛</h2>\n            <div class=\"forum-actions\">\n                <el-input\n                    placeholder=\"搜索帖子\"\n                    v-model=\"keyword\"\n                    class=\"search-input\"\n                    @keyup.enter.native=\"searchPosts\"\n                >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchPosts\"></el-button>\n                </el-input>\n                <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"post in posts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\" @click.native.stop=\"viewPostDetail(post.id)\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"warning\" v-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\" @click=\"viewPostDetail(post.id)\">\n                    {{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}\n                    <!-- <div class=\"view-more\">点击查看帖子全文</div> -->\n                </div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\" :class=\"{ 'liked': post.isLiked }\" @click.stop=\"toggleLike(post)\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <el-button size=\"small\" type=\"text\" @click.stop=\"viewPostDetail(post.id)\">查看详情 <i class=\"el-icon-arrow-right\"></i></el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"posts.length === 0\" description=\"暂无帖子\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'PostList',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0,\n            keyword: ''\n        };\n    },\n    created() {\n        // 检查用户角色\n        const currentUser = JSON.parse(sessionStorage.getItem('user'));\n        // 只有非用户角色可以访问论坛\n        if (!currentUser || !currentUser.role || currentUser.role.id === 14) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/home');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n\n            const params = {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: JSON.parse(sessionStorage.getItem('user')).id\n            };\n\n            if (this.keyword) {\n                params.keyword = this.keyword;\n            }\n\n            this.$get('/forum/post/list', params)\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange(page) {\n            this.currentPage = page;\n            this.fetchPosts();\n        },\n        searchPosts() {\n            this.currentPage = 1;\n            this.fetchPosts();\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            console.log('点击帖子，准备跳转到详情页，ID:', id);\n            try {\n                this.$router.push(`/home/<USER>/post/${id}`);\n            } catch (error) {\n                console.error('跳转失败:', error);\n                this.$message.error('跳转到帖子详情页失败');\n            }\n        },\n        toggleLike(post) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            this.$post(`/forum/post/like/${post.id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        post.isLiked = !post.isLiked;\n                        post.likeCount += post.isLiked ? 1 : -1;\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('点赞操作失败:', err);\n                    this.$message.error('点赞操作失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.forum-container {\n    padding: 20px;\n}\n\n.forum-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.forum-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.search-input {\n    width: 300px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n    cursor: pointer;\n    transition: all 0.3s;\n}\n\n.post-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n    cursor: pointer;\n}\n\n.view-more {\n    color: #409EFF;\n    margin-top: 8px;\n    font-size: 14px;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.liked {\n    color: #409EFF;\n}\n</style>\n"]}]}