{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue", "mtime": 1748720501299}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PostList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,OAAA,gBAAA,MAAA,mCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,gBAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,WAAA,EAAA,CAHA;AAIA,MAAA,QAAA,EAAA,EAJA;AAKA,MAAA,KAAA,EAAA,CALA;AAMA,MAAA,OAAA,EAAA;AANA,KAAA;AAQA,GAdA;AAeA,EAAA,OAfA,qBAeA;AACA;AACA,QAAA,WAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAFA,CAGA;;AACA,QAAA,CAAA,WAAA,IAAA,CAAA,WAAA,CAAA,IAAA,IAAA,WAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,OAAA;AACA;AACA;;AAEA,SAAA,UAAA;AACA,GA1BA;AA2BA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,KAAA,WADA;AAEA,QAAA,QAAA,EAAA,KAAA,QAFA;AAGA,QAAA,MAAA,EAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA;AAHA,OAAA;;AAMA,UAAA,KAAA,OAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA;;AAEA,WAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA,MAGA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAZA,EAaA,OAbA,CAaA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAfA;AAgBA,KA9BA;AA+BA,IAAA,sBA/BA,kCA+BA,IA/BA,EA+BA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,UAAA;AACA,KAlCA;AAmCA,IAAA,WAnCA,yBAmCA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,UAAA;AACA,KAtCA;AAuCA,IAAA,UAvCA,wBAuCA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA,KAzCA;AA0CA,IAAA,cA1CA,0BA0CA,EA1CA,EA0CA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,mBAAA,EAAA,EAAA;;AACA,UAAA;AACA,aAAA,OAAA,CAAA,IAAA,4BAAA,EAAA;AACA,OAFA,CAEA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA;AACA,KAlDA;AAmDA,IAAA,UAnDA,sBAmDA,IAnDA,EAmDA;AAAA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA;AACA,WAAA,KAAA,4BAAA,IAAA,CAAA,EAAA,GAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,OAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,OAZA;AAaA,KAlEA;AAmEA,IAAA,UAnEA,sBAmEA,OAnEA,EAmEA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA;AAvEA;AA3BA,CAAA", "sourcesContent": ["<template>\n    <div class=\"forum-container\">\n        <div class=\"forum-header\">\n            <h2>维修员论坛</h2>\n            <div class=\"forum-actions\">\n                <el-input\n                    placeholder=\"搜索帖子\"\n                    v-model=\"keyword\"\n                    class=\"search-input\"\n                    @keyup.enter.native=\"searchPosts\"\n                >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchPosts\"></el-button>\n                </el-input>\n                <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"post in posts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\" @click.native.stop=\"viewPostDetail(post.id)\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"warning\" v-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\" @click=\"viewPostDetail(post.id)\">\n                    {{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}\n                    <!-- <div class=\"view-more\">点击查看帖子全文</div> -->\n                </div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\" :class=\"{ 'liked': post.isLiked }\" @click.stop=\"toggleLike(post)\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <el-button size=\"small\" type=\"text\" @click.stop=\"viewPostDetail(post.id)\">查看详情 <i class=\"el-icon-arrow-right\"></i></el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"posts.length === 0\" description=\"暂无帖子\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'PostList',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0,\n            keyword: ''\n        };\n    },\n    created() {\n        // 检查用户角色\n        const currentUser = JSON.parse(sessionStorage.getItem('user'));\n        // 只有非用户角色可以访问论坛\n        if (!currentUser || !currentUser.role || currentUser.role.id === 14) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/home');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n\n            const params = {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: JSON.parse(sessionStorage.getItem('user')).id\n            };\n\n            if (this.keyword) {\n                params.keyword = this.keyword;\n            }\n\n            this.$get('/forum/post/list', params)\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange(page) {\n            this.currentPage = page;\n            this.fetchPosts();\n        },\n        searchPosts() {\n            this.currentPage = 1;\n            this.fetchPosts();\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            console.log('点击帖子，准备跳转到详情页，ID:', id);\n            try {\n                this.$router.push(`/home/<USER>/post/${id}`);\n            } catch (error) {\n                console.error('跳转失败:', error);\n                this.$message.error('跳转到帖子详情页失败');\n            }\n        },\n        toggleLike(post) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            this.$post(`/forum/post/like/${post.id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        post.isLiked = !post.isLiked;\n                        post.likeCount += post.isLiked ? 1 : -1;\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('点赞操作失败:', err);\n                    this.$message.error('点赞操作失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.forum-container {\n    padding: 20px;\n}\n\n.forum-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.forum-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.search-input {\n    width: 300px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n    cursor: pointer;\n    transition: all 0.3s;\n}\n\n.post-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n    cursor: pointer;\n}\n\n.view-more {\n    color: #409EFF;\n    margin-top: 8px;\n    font-size: 14px;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.liked {\n    color: #409EFF;\n}\n</style>\n"], "sourceRoot": "src/views/forum"}]}