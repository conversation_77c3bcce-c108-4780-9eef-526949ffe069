{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue", "mtime": 1745332328325}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AdminForumPostDetail.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"post-detail-container\">\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"15\" animated />\n        </el-card>\n\n        <div v-else>\n            <div class=\"post-actions\">\n                <el-button icon=\"el-icon-back\" @click=\"goBack\">返回</el-button>\n                <div>\n                    <el-button type=\"primary\" icon=\"el-icon-top\" @click=\"toggleTop(!post.isTop)\" v-if=\"post.status === 1\">\n                        {{ post.isTop ? '取消置顶' : '置顶' }}\n                    </el-button>\n                    <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost\" v-if=\"isAuthor\">编辑</el-button>\n                    <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost\">删除</el-button>\n                </div>\n            </div>\n\n            <el-card class=\"post-card\">\n                <div class=\"post-header\">\n                    <h2 class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </h2>\n                    <div class=\"post-status\" v-if=\"post.status === 0\">\n                        <el-tag type=\"warning\">待审核</el-tag>\n                    </div>\n                    <div class=\"post-status\" v-else-if=\"post.status === 2\">\n                        <el-tag type=\"danger\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-meta\">\n                    <span>\n                        作者: {{ post.author ? post.author.username : '未知' }}\n                        <el-tag size=\"mini\" type=\"success\" v-if=\"post.author && post.author.username === '管理员'\">管理员</el-tag>\n                    </span>\n                    <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                    <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                </div>\n                <div class=\"post-content\">{{ post.content }}</div>\n            </el-card>\n\n            <div class=\"comments-section\">\n                <h3>评论 ({{ post.commentCount || 0 }})</h3>\n\n                <div class=\"comments-list\">\n                    <el-card v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-card\">\n                        <div class=\"comment-header\">\n                            <span class=\"comment-author\">\n                                {{ comment.author ? comment.author.username : '未知' }}\n                                <el-tag size=\"mini\" type=\"success\" v-if=\"comment.author && comment.author.username === '管理员'\">管理员</el-tag>\n                            </span>\n                            <span class=\"comment-time\">{{ formatDate(comment.createTime) }}</span>\n                        </div>\n                        <div class=\"comment-content\">{{ comment.content }}</div>\n                        <div class=\"comment-actions\">\n                            <el-button\n                                size=\"mini\"\n                                type=\"danger\"\n                                icon=\"el-icon-delete\"\n                                @click=\"deleteComment(comment)\"\n                            >\n                                删除\n                            </el-button>\n                        </div>\n                    </el-card>\n\n                    <el-empty v-if=\"comments.length === 0\" description=\"暂无评论\"></el-empty>\n                </div>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostDetail',\n    data() {\n        return {\n            post: {},\n            comments: [],\n            loading: true,\n            adminId: null\n        };\n    },\n    computed: {\n        isAuthor() {\n            return this.post.authorId === this.adminId;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.post = res.data.post;\n                        this.comments = this.post.comments || [];\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        goBack() {\n            this.$router.go(-1);\n        },\n        editPost() {\n            this.$router.push(`/admin/home/<USER>/edit/${this.post.id}`);\n        },\n        toggleTop(isTop) {\n            this.$put(`/forum/post/top/${this.post.id}?isTop=${isTop}`)\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');\n                        this.post.isTop = isTop; // 更新当前帖子的置顶状态\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('置顶操作失败:', err);\n                    this.$message.error('置顶操作失败');\n                });\n        },\n        deletePost() {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                this.$del(`/forum/post/${this.post.id}?operatorId=${this.adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.$router.push('/admin/home/<USER>/posts');\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        deleteComment(comment) {\n            console.log('准备删除评论:', comment.id, '管理员ID:', this.adminId);\n\n            this.$confirm('确定要删除这条评论吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                console.log('发送删除评论请求:', `/forum/comment/${comment.id}`, { operatorId: this.adminId });\n\n                this.$del(`/forum/comment/${comment.id}`, { operatorId: this.adminId })\n                    .then(res => {\n                        console.log('删除评论响应:', res.data);\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPostDetail(); // 重新加载评论\n                        } else {\n                            console.error('删除评论失败，服务器响应:', res.data.msg);\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除评论失败:', err);\n                        this.$message.error('删除评论失败: ' + err.message);\n                    });\n            }).catch(() => {\n                // 取消删除\n                console.log('取消删除评论');\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.post-detail-container {\n    padding: 20px;\n}\n\n.post-actions {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 30px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n}\n\n.post-title {\n    margin: 0;\n    font-size: 24px;\n}\n\n.post-meta {\n    display: flex;\n    gap: 20px;\n    color: #909399;\n    margin-bottom: 20px;\n    font-size: 14px;\n}\n\n.post-content {\n    line-height: 1.6;\n    margin-bottom: 20px;\n    white-space: pre-line;\n}\n\n.comments-section {\n    margin-top: 30px;\n}\n\n.comment-card {\n    margin-bottom: 15px;\n}\n\n.comment-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 10px;\n}\n\n.comment-author {\n    font-weight: bold;\n}\n\n.comment-time {\n    color: #909399;\n    font-size: 14px;\n}\n\n.comment-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n    white-space: pre-line;\n}\n\n.comment-actions {\n    display: flex;\n    gap: 10px;\n    justify-content: flex-end;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}