{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue", "mtime": 1748720501687}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Notifications.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,OAAA,gBAAA,MAAA,mCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,gBAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,aAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,WAAA,EAAA,CAHA;AAIA,MAAA,QAAA,EAAA,EAJA;AAKA,MAAA,KAAA,EAAA;AALA,KAAA;AAOA,GAbA;AAcA,EAAA,OAdA,qBAcA;AACA,SAAA,kBAAA;AACA,GAhBA;AAiBA,EAAA,OAAA,EAAA;AACA,IAAA,kBADA,gCACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA;AAEA,WAAA,IAAA,CAAA,0BAAA,EAAA;AACA,QAAA,MAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA,KAAA,WAFA;AAGA,QAAA,QAAA,EAAA,KAAA;AAHA,OAAA,EAKA,IALA,CAKA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA,MAGA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OAZA,EAaA,KAbA,CAaA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAhBA,EAiBA,OAjBA,CAiBA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAnBA;AAoBA,KAzBA;AA0BA,IAAA,sBA1BA,oCA0BA;AACA,WAAA,kBAAA;AACA,KA5BA;AA6BA,IAAA,eA7BA,2BA6BA,YA7BA,EA6BA;AACA,UAAA,YAAA,CAAA,SAAA,IAAA,YAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,IAAA,4BAAA,YAAA,CAAA,SAAA,GAFA,CAIA;;AACA,YAAA,CAAA,YAAA,CAAA,MAAA,EAAA;AACA,eAAA,UAAA,CAAA,YAAA;AACA;AACA;AACA,KAvCA;AAwCA,IAAA,UAxCA,sBAwCA,YAxCA,EAwCA;AAAA;;AACA,WAAA,IAAA,oCAAA,YAAA,CAAA,EAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,YAAA,CAAA,MAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,0BAAA;AACA,SAJA,MAIA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,QAAA;AACA;AACA,OATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,OAbA;AAcA,KAvDA;AAwDA,IAAA,aAxDA,2BAwDA;AAAA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA;AAEA,WAAA,IAAA,CAAA,8BAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA,EADA,CAEA;;;AACA,UAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,YAAA,EAAA;AACA,YAAA,YAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAFA,EAHA,CAMA;;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,0BAAA;AACA,SARA,MAQA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,OAbA,EAcA,KAdA,CAcA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAjBA;AAkBA,KA7EA;AA8EA,IAAA,kBA9EA,8BA8EA,EA9EA,EA8EA;AAAA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,EAAA;AAEA,WAAA,IAAA,+BAAA,EAAA,GAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CAEA;;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,EAAA,KAAA,EAAA;AAAA,WAAA,CAAA,CAHA,CAIA;;AACA,cAAA,MAAA,CAAA,aAAA,CAAA,MAAA,KAAA,CAAA,IAAA,MAAA,CAAA,WAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA;;AACA,YAAA,MAAA,CAAA,kBAAA;AACA;AACA,SATA,MASA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,OAdA,EAeA,KAfA,CAeA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,OAlBA;AAmBA,KApGA;AAqGA,IAAA,UArGA,sBAqGA,OArGA,EAqGA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA;AAzGA;AAjBA,CAAA", "sourcesContent": ["<template>\n    <div class=\"notifications-container\">\n        <div class=\"page-header\">\n            <h2>消息通知</h2>\n            <div class=\"header-actions\">\n                <el-button type=\"text\" @click=\"markAllAsRead\" :disabled=\"notifications.length === 0\">全部标为已读</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"notification in notifications\" :key=\"notification.id\" class=\"notification-card\" :class=\"{ 'unread': !notification.isRead }\">\n                <div class=\"notification-header\">\n                    <div class=\"notification-type\">\n                        <el-tag type=\"primary\" v-if=\"notification.type === 1\">点赞</el-tag>\n                        <el-tag type=\"success\" v-else-if=\"notification.type === 2\">评论</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"notification.type === 3\">删除</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"notification.type === 4\">审核未通过</el-tag>\n                    </div>\n                    <div class=\"notification-time\">{{ formatDate(notification.createTime) }}</div>\n                </div>\n                <div class=\"notification-content\">{{ notification.content }}</div>\n                <div class=\"notification-actions\">\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"viewRelatedPost(notification)\"\n                        v-if=\"notification.type !== 3\"\n                    >\n                        查看详情\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"markAsRead(notification)\"\n                        v-if=\"!notification.isRead\"\n                    >\n                        标为已读\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"deleteNotification(notification.id)\"\n                    >\n                        删除\n                    </el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"notifications.length === 0\" description=\"暂无通知\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'Notifications',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            notifications: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    created() {\n        this.fetchNotifications();\n    },\n    methods: {\n        fetchNotifications() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$get('/forum/notification/list', {\n                userId: userId,\n                pageNum: this.currentPage,\n                pageSize: this.pageSize\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.notifications = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取通知列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取通知列表失败:', err);\n                    this.$message.error('获取通知列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange() {\n            this.fetchNotifications();\n        },\n        viewRelatedPost(notification) {\n            if (notification.relatedId && notification.type !== 3) {\n                // 如果是点赞或评论通知，跳转到相关帖子\n                this.$router.push(`/home/<USER>/post/${notification.relatedId}`);\n                \n                // 如果通知未读，标记为已读\n                if (!notification.isRead) {\n                    this.markAsRead(notification);\n                }\n            }\n        },\n        markAsRead(notification) {\n            this.$put(`/forum/notification/read/${notification.id}`)\n                .then(res => {\n                    if (res.data.status) {\n                        notification.isRead = true;\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '标记已读失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记已读失败:', err);\n                    this.$message.error('标记已读失败');\n                });\n        },\n        markAllAsRead() {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$put('/forum/notification/read/all', { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('全部标记为已读');\n                        // 更新通知状态\n                        this.notifications.forEach(notification => {\n                            notification.isRead = true;\n                        });\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记全部已读失败:', err);\n                    this.$message.error('标记全部已读失败');\n                });\n        },\n        deleteNotification(id) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$del(`/forum/notification/${id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('删除成功');\n                        // 从列表中移除\n                        this.notifications = this.notifications.filter(n => n.id !== id);\n                        // 如果当前页没有数据了，且不是第一页，则跳转到上一页\n                        if (this.notifications.length === 0 && this.currentPage > 1) {\n                            this.currentPage--;\n                            this.fetchNotifications();\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '删除失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('删除通知失败:', err);\n                    this.$message.error('删除通知失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.notifications-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.notification-card {\n    margin-bottom: 15px;\n    transition: background-color 0.3s;\n}\n\n.notification-card.unread {\n    background-color: #f0f9ff;\n}\n\n.notification-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.notification-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.notification-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/forum"}]}