{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\CommonPagination.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\CommonPagination.vue", "mtime": 1748720498695}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogJ0NvbW1vblBhZ2luYXRpb24nLA0KICAgIHByb3BzOiB7DQogICAgICAgIC8vIOaAu+aVsOaNruadoeaVsA0KICAgICAgICB0b3RhbDogew0KICAgICAgICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5b2T5YmN6aG156CBDQogICAgICAgIGN1cnJlbnRQYWdlOiB7DQogICAgICAgICAgICB0eXBlOiBOdW1iZXIsDQogICAgICAgICAgICBkZWZhdWx0OiAxDQogICAgICAgIH0sDQogICAgICAgIC8vIOavj+mhteaYvuekuuadoeaVsA0KICAgICAgICBwYWdlU2l6ZTogew0KICAgICAgICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgICAgICAgZGVmYXVsdDogMTANCiAgICAgICAgfQ0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnBhZ2VTaXplJywgdmFsKTsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpjdXJyZW50UGFnZScsIDEpOw0KICAgICAgICAgICAgdGhpcy4kZW1pdCgncGFnaW5hdGlvbi1jaGFuZ2UnKTsNCiAgICAgICAgfSwNCiAgICAgICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpjdXJyZW50UGFnZScsIHZhbCk7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCdwYWdpbmF0aW9uLWNoYW5nZScpOw0KICAgICAgICB9DQogICAgfQ0KfQ0K"}, {"version": 3, "sources": ["CommonPagination.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CommonPagination.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n    <div class=\"pagination-container\">\r\n        <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"currentPage\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\">\r\n        </el-pagination>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'CommonPagination',\r\n    props: {\r\n        // 总数据条数\r\n        total: {\r\n            type: Number,\r\n            required: true\r\n        },\r\n        // 当前页码\r\n        currentPage: {\r\n            type: Number,\r\n            default: 1\r\n        },\r\n        // 每页显示条数\r\n        pageSize: {\r\n            type: Number,\r\n            default: 10\r\n        }\r\n    },\r\n    methods: {\r\n        handleSizeChange(val) {\r\n            this.$emit('update:pageSize', val);\r\n            this.$emit('update:currentPage', 1);\r\n            this.$emit('pagination-change');\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.$emit('update:currentPage', val);\r\n            this.$emit('pagination-change');\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.pagination-container {\r\n    margin-top: 20px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 20px;\r\n}\r\n</style> "]}]}