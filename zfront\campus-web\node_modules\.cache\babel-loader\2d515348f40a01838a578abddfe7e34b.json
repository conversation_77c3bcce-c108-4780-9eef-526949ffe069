{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\src\\assets\\data\\region.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\assets\\data\\region.js", "mtime": 1745731548956}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgW3sKICB2YWx1ZTogJ+WMl+S6rOW4gicsCiAgbGFiZWw6ICfljJfkuqzluIInLAogIGNoaWxkcmVuOiBbewogICAgdmFsdWU6ICfljJfkuqzluIInLAogICAgbGFiZWw6ICfljJfkuqzluIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn5Lic5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfkuJzln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6KW/5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfopb/ln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5pyd6Ziz5Yy6JywKICAgICAgbGFiZWw6ICfmnJ3pmLPljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rW35reA5Yy6JywKICAgICAgbGFiZWw6ICfmtbfmt4DljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Liw5Y+w5Yy6JywKICAgICAgbGFiZWw6ICfkuLDlj7DljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn55+z5pmv5bGx5Yy6JywKICAgICAgbGFiZWw6ICfnn7Pmma/lsbHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6Zeo5aS05rKf5Yy6JywKICAgICAgbGFiZWw6ICfpl6jlpLTmsp/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5oi/5bGx5Yy6JywKICAgICAgbGFiZWw6ICfmiL/lsbHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6YCa5bee5Yy6JywKICAgICAgbGFiZWw6ICfpgJrlt57ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6aG65LmJ5Yy6JywKICAgICAgbGFiZWw6ICfpobrkuYnljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5piM5bmz5Yy6JywKICAgICAgbGFiZWw6ICfmmIzlubPljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5aSn5YW05Yy6JywKICAgICAgbGFiZWw6ICflpKflhbTljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5oCA5p+U5Yy6JywKICAgICAgbGFiZWw6ICfmgIDmn5TljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5bmz6LC35Yy6JywKICAgICAgbGFiZWw6ICflubPosLfljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5a+G5LqR5Yy6JywKICAgICAgbGFiZWw6ICflr4bkupHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5bu25bqG5Yy6JywKICAgICAgbGFiZWw6ICflu7bluobljLonCiAgICB9XQogIH1dCn0sIHsKICB2YWx1ZTogJ+Wkqea0peW4gicsCiAgbGFiZWw6ICflpKnmtKXluIInLAogIGNoaWxkcmVuOiBbewogICAgdmFsdWU6ICflpKnmtKXluIInLAogICAgbGFiZWw6ICflpKnmtKXluIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn5ZKM5bmz5Yy6JywKICAgICAgbGFiZWw6ICflkozlubPljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rKz5Lic5Yy6JywKICAgICAgbGFiZWw6ICfmsrPkuJzljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rKz6KW/5Yy6JywKICAgICAgbGFiZWw6ICfmsrPopb/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Y2X5byA5Yy6JywKICAgICAgbGFiZWw6ICfljZflvIDljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rKz5YyX5Yy6JywKICAgICAgbGFiZWw6ICfmsrPljJfljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn57qi5qGl5Yy6JywKICAgICAgbGFiZWw6ICfnuqLmoaXljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Lic5Li95Yy6JywKICAgICAgbGFiZWw6ICfkuJzkuL3ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6KW/6Z2S5Yy6JywKICAgICAgbGFiZWw6ICfopb/pnZLljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rSl5Y2X5Yy6JywKICAgICAgbGFiZWw6ICfmtKXljZfljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5YyX6L6w5Yy6JywKICAgICAgbGFiZWw6ICfljJfovrDljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5q2m5riF5Yy6JywKICAgICAgbGFiZWw6ICfmrabmuIXljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5a6d5Z275Yy6JywKICAgICAgbGFiZWw6ICflrp3lnbvljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5ruo5rW35paw5Yy6JywKICAgICAgbGFiZWw6ICfmu6jmtbfmlrDljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5a6B5rKz5Yy6JywKICAgICAgbGFiZWw6ICflroHmsrPljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6Z2Z5rW35Yy6JywKICAgICAgbGFiZWw6ICfpnZnmtbfljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6JOf5bee5Yy6JywKICAgICAgbGFiZWw6ICfok5/lt57ljLonCiAgICB9XQogIH1dCn0sIHsKICB2YWx1ZTogJ+ays+WMl+ecgScsCiAgbGFiZWw6ICfmsrPljJfnnIEnLAogIGNoaWxkcmVuOiBbewogICAgdmFsdWU6ICfnn7PlrrbluoTluIInLAogICAgbGFiZWw6ICfnn7PlrrbluoTluIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn6ZW/5a6J5Yy6JywKICAgICAgbGFiZWw6ICfplb/lronljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5qGl6KW/5Yy6JywKICAgICAgbGFiZWw6ICfmoaXopb/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5paw5Y2O5Yy6JywKICAgICAgbGFiZWw6ICfmlrDljY7ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5LqV6ZmJ55+/5Yy6JywKICAgICAgbGFiZWw6ICfkupXpmYnnn7/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6KOV5Y2O5Yy6JywKICAgICAgbGFiZWw6ICfoo5XljY7ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6JeB5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfol4Hln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6bm/5rOJ5Yy6JywKICAgICAgbGFiZWw6ICfpub/ms4nljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5qC+5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfmoL7ln47ljLonCiAgICB9XQogIH0sIHsKICAgIHZhbHVlOiAn5ZSQ5bGx5biCJywKICAgIGxhYmVsOiAn5ZSQ5bGx5biCJywKICAgIGNoaWxkcmVuOiBbewogICAgICB2YWx1ZTogJ+i3r+WNl+WMuicsCiAgICAgIGxhYmVsOiAn6Lev5Y2X5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+i3r+WMl+WMuicsCiAgICAgIGxhYmVsOiAn6Lev5YyX5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WPpOWGtuWMuicsCiAgICAgIGxhYmVsOiAn5Y+k5Ya25Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+W8gOW5s+WMuicsCiAgICAgIGxhYmVsOiAn5byA5bmz5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+S4sOWNl+WMuicsCiAgICAgIGxhYmVsOiAn5Liw5Y2X5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+S4sOa2puWMuicsCiAgICAgIGxhYmVsOiAn5Liw5ram5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+abueWmg+eUuOWMuicsCiAgICAgIGxhYmVsOiAn5pu55aaD55S45Yy6JwogICAgfV0KICB9XQp9LCB7CiAgdmFsdWU6ICflsbHkuJznnIEnLAogIGxhYmVsOiAn5bGx5Lic55yBJywKICBjaGlsZHJlbjogW3sKICAgIHZhbHVlOiAn5rWO5Y2X5biCJywKICAgIGxhYmVsOiAn5rWO5Y2X5biCJywKICAgIGNoaWxkcmVuOiBbewogICAgICB2YWx1ZTogJ+WOhuS4i+WMuicsCiAgICAgIGxhYmVsOiAn5Y6G5LiL5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+W4guS4reWMuicsCiAgICAgIGxhYmVsOiAn5biC5Lit5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+ankOiNq+WMuicsCiAgICAgIGxhYmVsOiAn5qeQ6I2r5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WkqeahpeWMuicsCiAgICAgIGxhYmVsOiAn5aSp5qGl5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WOhuWfjuWMuicsCiAgICAgIGxhYmVsOiAn5Y6G5Z+O5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mVv+a4heWMuicsCiAgICAgIGxhYmVsOiAn6ZW/5riF5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+eroOS4mOWMuicsCiAgICAgIGxhYmVsOiAn56ug5LiY5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+a1jumYs+WMuicsCiAgICAgIGxhYmVsOiAn5rWO6Ziz5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+iOseiKnOWMuicsCiAgICAgIGxhYmVsOiAn6I6x6Iqc5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mSouWfjuWMuicsCiAgICAgIGxhYmVsOiAn6ZKi5Z+O5Yy6JwogICAgfV0KICB9LCB7CiAgICB2YWx1ZTogJ+mdkuWym+W4gicsCiAgICBsYWJlbDogJ+mdkuWym+W4gicsCiAgICBjaGlsZHJlbjogW3sKICAgICAgdmFsdWU6ICfluILljZfljLonLAogICAgICBsYWJlbDogJ+W4guWNl+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfluILljJfljLonLAogICAgICBsYWJlbDogJ+W4guWMl+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpu4TlspvljLonLAogICAgICBsYWJlbDogJ+m7hOWym+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfltILlsbHljLonLAogICAgICBsYWJlbDogJ+W0guWxseWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmnY7msqfljLonLAogICAgICBsYWJlbDogJ+adjuayp+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfln47pmLPljLonLAogICAgICBsYWJlbDogJ+WfjumYs+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfljbPloqjljLonLAogICAgICBsYWJlbDogJ+WNs+WiqOWMuicKICAgIH1dCiAgfV0KfSwgewogIHZhbHVlOiAn5rGf6IuP55yBJywKICBsYWJlbDogJ+axn+iLj+ecgScsCiAgY2hpbGRyZW46IFt7CiAgICB2YWx1ZTogJ+WNl+S6rOW4gicsCiAgICBsYWJlbDogJ+WNl+S6rOW4gicsCiAgICBjaGlsZHJlbjogW3sKICAgICAgdmFsdWU6ICfnjoTmrabljLonLAogICAgICBsYWJlbDogJ+eOhOatpuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfnp6bmt67ljLonLAogICAgICBsYWJlbDogJ+enpua3ruWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICflu7rpgrrljLonLAogICAgICBsYWJlbDogJ+W7uumCuuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpvJPmpbzljLonLAogICAgICBsYWJlbDogJ+m8k+alvOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmtablj6PljLonLAogICAgICBsYWJlbDogJ+a1puWPo+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmoJbpnJ7ljLonLAogICAgICBsYWJlbDogJ+aglumcnuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpm6joirHlj7DljLonLAogICAgICBsYWJlbDogJ+mbqOiKseWPsOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmsZ/lroHljLonLAogICAgICBsYWJlbDogJ+axn+WugeWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICflha3lkIjljLonLAogICAgICBsYWJlbDogJ+WFreWQiOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmuqfmsLTljLonLAogICAgICBsYWJlbDogJ+a6p+awtOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpq5jmt7PljLonLAogICAgICBsYWJlbDogJ+mrmOa3s+WMuicKICAgIH1dCiAgfSwgewogICAgdmFsdWU6ICfoi4/lt57luIInLAogICAgbGFiZWw6ICfoi4/lt57luIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn5aeR6IuP5Yy6JywKICAgICAgbGFiZWw6ICflp5Hoi4/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6JmO5LiY5Yy6JywKICAgICAgbGFiZWw6ICfomY7kuJjljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5ZC05Lit5Yy6JywKICAgICAgbGFiZWw6ICflkLTkuK3ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn55u45Z+O5Yy6JywKICAgICAgbGFiZWw6ICfnm7jln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5ZC05rGf5Yy6JywKICAgICAgbGFiZWw6ICflkLTmsZ/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5bi454af5biCJywKICAgICAgbGFiZWw6ICfluLjnhp/luIInCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5byg5a625riv5biCJywKICAgICAgbGFiZWw6ICflvKDlrrbmuK/luIInCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5piG5bGx5biCJywKICAgICAgbGFiZWw6ICfmmIblsbHluIInCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5aSq5LuT5biCJywKICAgICAgbGFiZWw6ICflpKrku5PluIInCiAgICB9XQogIH1dCn0sIHsKICB2YWx1ZTogJ+a1meaxn+ecgScsCiAgbGFiZWw6ICfmtZnmsZ/nnIEnLAogIGNoaWxkcmVuOiBbewogICAgdmFsdWU6ICfmna3lt57luIInLAogICAgbGFiZWw6ICfmna3lt57luIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn5LiK5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfkuIrln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5LiL5Z+O5Yy6JywKICAgICAgbGFiZWw6ICfkuIvln47ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5rGf5bmy5Yy6JywKICAgICAgbGFiZWw6ICfmsZ/lubLljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5oux5aKF5Yy6JywKICAgICAgbGFiZWw6ICfmi7HlooXljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6KW/5rmW5Yy6JywKICAgICAgbGFiZWw6ICfopb/muZbljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5ruo5rGf5Yy6JywKICAgICAgbGFiZWw6ICfmu6jmsZ/ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6JCn5bGx5Yy6JywKICAgICAgbGFiZWw6ICfokKflsbHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5L2Z5p2t5Yy6JywKICAgICAgbGFiZWw6ICfkvZnmna3ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5a+M6Ziz5Yy6JywKICAgICAgbGFiZWw6ICflr4zpmLPljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Li05a6J5Yy6JywKICAgICAgbGFiZWw6ICfkuLTlronljLonCiAgICB9XQogIH0sIHsKICAgIHZhbHVlOiAn5a6B5rOi5biCJywKICAgIGxhYmVsOiAn5a6B5rOi5biCJywKICAgIGNoaWxkcmVuOiBbewogICAgICB2YWx1ZTogJ+a1t+abmeWMuicsCiAgICAgIGxhYmVsOiAn5rW35puZ5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+axn+WMl+WMuicsCiAgICAgIGxhYmVsOiAn5rGf5YyX5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WMl+S7keWMuicsCiAgICAgIGxhYmVsOiAn5YyX5LuR5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mVh+a1t+WMuicsCiAgICAgIGxhYmVsOiAn6ZWH5rW35Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mEnuW3nuWMuicsCiAgICAgIGxhYmVsOiAn6YSe5bee5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WlieWMluWMuicsCiAgICAgIGxhYmVsOiAn5aWJ5YyW5Yy6JwogICAgfV0KICB9XQp9LCB7CiAgdmFsdWU6ICflronlvr3nnIEnLAogIGxhYmVsOiAn5a6J5b6955yBJywKICBjaGlsZHJlbjogW3sKICAgIHZhbHVlOiAn5ZCI6IKl5biCJywKICAgIGxhYmVsOiAn5ZCI6IKl5biCJywKICAgIGNoaWxkcmVuOiBbewogICAgICB2YWx1ZTogJ+eRtua1t+WMuicsCiAgICAgIGxhYmVsOiAn55G25rW35Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+W6kOmYs+WMuicsCiAgICAgIGxhYmVsOiAn5bqQ6Ziz5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+icgOWxseWMuicsCiAgICAgIGxhYmVsOiAn6JyA5bGx5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WMheays+WMuicsCiAgICAgIGxhYmVsOiAn5YyF5rKz5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mVv+S4sOWOvycsCiAgICAgIGxhYmVsOiAn6ZW/5Liw5Y6/JwogICAgfSwgewogICAgICB2YWx1ZTogJ+iCpeS4nOWOvycsCiAgICAgIGxhYmVsOiAn6IKl5Lic5Y6/JwogICAgfSwgewogICAgICB2YWx1ZTogJ+iCpeilv+WOvycsCiAgICAgIGxhYmVsOiAn6IKl6KW/5Y6/JwogICAgfSwgewogICAgICB2YWx1ZTogJ+W6kOaxn+WOvycsCiAgICAgIGxhYmVsOiAn5bqQ5rGf5Y6/JwogICAgfV0KICB9XQp9LCB7CiAgdmFsdWU6ICfnpo/lu7rnnIEnLAogIGxhYmVsOiAn56aP5bu655yBJywKICBjaGlsZHJlbjogW3sKICAgIHZhbHVlOiAn56aP5bee5biCJywKICAgIGxhYmVsOiAn56aP5bee5biCJywKICAgIGNoaWxkcmVuOiBbewogICAgICB2YWx1ZTogJ+m8k+alvOWMuicsCiAgICAgIGxhYmVsOiAn6byT5qW85Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+WPsOaxn+WMuicsCiAgICAgIGxhYmVsOiAn5Y+w5rGf5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+S7k+WxseWMuicsCiAgICAgIGxhYmVsOiAn5LuT5bGx5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mprOWwvuWMuicsCiAgICAgIGxhYmVsOiAn6ams5bC+5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+aZi+WuieWMuicsCiAgICAgIGxhYmVsOiAn5pmL5a6J5Yy6JwogICAgfSwgewogICAgICB2YWx1ZTogJ+mVv+S5kOWMuicsCiAgICAgIGxhYmVsOiAn6ZW/5LmQ5Yy6JwogICAgfV0KICB9LCB7CiAgICB2YWx1ZTogJ+WOpumXqOW4gicsCiAgICBsYWJlbDogJ+WOpumXqOW4gicsCiAgICBjaGlsZHJlbjogW3sKICAgICAgdmFsdWU6ICfmgJ3mmI7ljLonLAogICAgICBsYWJlbDogJ+aAneaYjuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmtbfmsqfljLonLAogICAgICBsYWJlbDogJ+a1t+ayp+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmuZbph4zljLonLAogICAgICBsYWJlbDogJ+a5lumHjOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpm4bnvo7ljLonLAogICAgICBsYWJlbDogJ+mbhue+juWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICflkIzlronljLonLAogICAgICBsYWJlbDogJ+WQjOWuieWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfnv5TlronljLonLAogICAgICBsYWJlbDogJ+e/lOWuieWMuicKICAgIH1dCiAgfV0KfSwgewogIHZhbHVlOiAn5rGf6KW/55yBJywKICBsYWJlbDogJ+axn+ilv+ecgScsCiAgY2hpbGRyZW46IFt7CiAgICB2YWx1ZTogJ+WNl+aYjOW4gicsCiAgICBsYWJlbDogJ+WNl+aYjOW4gicsCiAgICBjaGlsZHJlbjogW3sKICAgICAgdmFsdWU6ICfkuJzmuZbljLonLAogICAgICBsYWJlbDogJ+S4nOa5luWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfopb/muZbljLonLAogICAgICBsYWJlbDogJ+ilv+a5luWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpnZLkupHosLHljLonLAogICAgICBsYWJlbDogJ+mdkuS6keiwseWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmub7ph4zljLonLAogICAgICBsYWJlbDogJ+a5vumHjOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpnZLlsbHmuZbljLonLAogICAgICBsYWJlbDogJ+mdkuWxsea5luWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmlrDlu7rljLonLAogICAgICBsYWJlbDogJ+aWsOW7uuWMuicKICAgIH1dCiAgfV0KfSwgewogIHZhbHVlOiAn5bm/5Lic55yBJywKICBsYWJlbDogJ+W5v+S4nOecgScsCiAgY2hpbGRyZW46IFt7CiAgICB2YWx1ZTogJ+W5v+W3nuW4gicsCiAgICBsYWJlbDogJ+W5v+W3nuW4gicsCiAgICBjaGlsZHJlbjogW3sKICAgICAgdmFsdWU6ICfojZTmub7ljLonLAogICAgICBsYWJlbDogJ+iNlOa5vuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfotornp4DljLonLAogICAgICBsYWJlbDogJ+i2iuengOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfmtbfnj6DljLonLAogICAgICBsYWJlbDogJ+a1t+ePoOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICflpKnmsrPljLonLAogICAgICBsYWJlbDogJ+Wkqeays+WMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfnmb3kupHljLonLAogICAgICBsYWJlbDogJ+eZveS6keWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfpu4Tln5TljLonLAogICAgICBsYWJlbDogJ+m7hOWflOWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfnlarnprrljLonLAogICAgICBsYWJlbDogJ+eVquemuuWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfoirHpg73ljLonLAogICAgICBsYWJlbDogJ+iKsemDveWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfljZfmspnljLonLAogICAgICBsYWJlbDogJ+WNl+aymeWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICfku47ljJbljLonLAogICAgICBsYWJlbDogJ+S7juWMluWMuicKICAgIH0sIHsKICAgICAgdmFsdWU6ICflop7ln47ljLonLAogICAgICBsYWJlbDogJ+WinuWfjuWMuicKICAgIH1dCiAgfSwgewogICAgdmFsdWU6ICfmt7HlnLPluIInLAogICAgbGFiZWw6ICfmt7HlnLPluIInLAogICAgY2hpbGRyZW46IFt7CiAgICAgIHZhbHVlOiAn56aP55Sw5Yy6JywKICAgICAgbGFiZWw6ICfnpo/nlLDljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn572X5rmW5Yy6JywKICAgICAgbGFiZWw6ICfnvZfmuZbljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Y2X5bGx5Yy6JywKICAgICAgbGFiZWw6ICfljZflsbHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5a6d5a6J5Yy6JywKICAgICAgbGFiZWw6ICflrp3lronljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6b6Z5bKX5Yy6JywKICAgICAgbGFiZWw6ICfpvpnlspfljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn55uQ55Sw5Yy6JywKICAgICAgbGFiZWw6ICfnm5DnlLDljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn6b6Z5Y2O5Yy6JywKICAgICAgbGFiZWw6ICfpvpnljY7ljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5Z2q5bGx5Yy6JywKICAgICAgbGFiZWw6ICflnarlsbHljLonCiAgICB9LCB7CiAgICAgIHZhbHVlOiAn5YWJ5piO5Yy6JywKICAgICAgbGFiZWw6ICflhYnmmI7ljLonCiAgICB9XQogIH1dCn1dOw=="}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/src/assets/data/region.js"], "names": ["value", "label", "children"], "mappings": "AAAA,eAAe,CACb;AACEA,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ,EAWR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAXQ,EAYR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAZQ,EAaR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAbQ,EAcR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAdQ,EAeR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAfQ,EAgBR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAhBQ;AAHZ,GADQ;AAHZ,CADa,EA6Bb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ,EAWR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAXQ,EAYR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAZQ,EAaR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAbQ,EAcR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAdQ,EAeR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAfQ,EAgBR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAhBQ;AAHZ,GADQ;AAHZ,CA7Ba,EAyDb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,MADT;AAEEC,IAAAA,KAAK,EAAE,MAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ;AAHZ,GADQ,EAeR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAPQ;AAHZ,GAfQ;AAHZ,CAzDa,EA0Fb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ;AAHZ,GADQ,EAiBR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ;AAHZ,GAjBQ;AAHZ,CA1Fa,EA6Hb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ,EAWR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAXQ;AAHZ,GADQ,EAkBR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ;AAHZ,GAlBQ;AAHZ,CA7Ha,EAmKb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ;AAHZ,GADQ,EAiBR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ;AAHZ,GAjBQ;AAHZ,CAnKa,EAqMb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ;AAHZ,GADQ;AAHZ,CArMa,EAyNb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ;AAHZ,GADQ,EAaR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ;AAHZ,GAbQ;AAHZ,CAzNa,EAuPb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,KAAK,EAAE;AAAxB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ;AAHZ,GADQ;AAHZ,CAvPa,EAyQb;AACED,EAAAA,KAAK,EAAE,KADT;AAEEC,EAAAA,KAAK,EAAE,KAFT;AAGEC,EAAAA,QAAQ,EAAE,CACR;AACEF,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ,EAUR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAVQ,EAWR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAXQ;AAHZ,GADQ,EAkBR;AACED,IAAAA,KAAK,EAAE,KADT;AAEEC,IAAAA,KAAK,EAAE,KAFT;AAGEC,IAAAA,QAAQ,EAAE,CACR;AAAEF,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KADQ,EAER;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAFQ,EAGR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAHQ,EAIR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAJQ,EAKR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KALQ,EAMR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KANQ,EAOR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KAPQ,EAQR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KARQ,EASR;AAAED,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,KAAK,EAAE;AAAvB,KATQ;AAHZ,GAlBQ;AAHZ,CAzQa,CAAf", "sourcesContent": ["export default [\n  {\n    value: '北京市',\n    label: '北京市',\n    children: [\n      {\n        value: '北京市',\n        label: '北京市',\n        children: [\n          { value: '东城区', label: '东城区' },\n          { value: '西城区', label: '西城区' },\n          { value: '朝阳区', label: '朝阳区' },\n          { value: '海淀区', label: '海淀区' },\n          { value: '丰台区', label: '丰台区' },\n          { value: '石景山区', label: '石景山区' },\n          { value: '门头沟区', label: '门头沟区' },\n          { value: '房山区', label: '房山区' },\n          { value: '通州区', label: '通州区' },\n          { value: '顺义区', label: '顺义区' },\n          { value: '昌平区', label: '昌平区' },\n          { value: '大兴区', label: '大兴区' },\n          { value: '怀柔区', label: '怀柔区' },\n          { value: '平谷区', label: '平谷区' },\n          { value: '密云区', label: '密云区' },\n          { value: '延庆区', label: '延庆区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '天津市',\n    label: '天津市',\n    children: [\n      {\n        value: '天津市',\n        label: '天津市',\n        children: [\n          { value: '和平区', label: '和平区' },\n          { value: '河东区', label: '河东区' },\n          { value: '河西区', label: '河西区' },\n          { value: '南开区', label: '南开区' },\n          { value: '河北区', label: '河北区' },\n          { value: '红桥区', label: '红桥区' },\n          { value: '东丽区', label: '东丽区' },\n          { value: '西青区', label: '西青区' },\n          { value: '津南区', label: '津南区' },\n          { value: '北辰区', label: '北辰区' },\n          { value: '武清区', label: '武清区' },\n          { value: '宝坻区', label: '宝坻区' },\n          { value: '滨海新区', label: '滨海新区' },\n          { value: '宁河区', label: '宁河区' },\n          { value: '静海区', label: '静海区' },\n          { value: '蓟州区', label: '蓟州区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '河北省',\n    label: '河北省',\n    children: [\n      {\n        value: '石家庄市',\n        label: '石家庄市',\n        children: [\n          { value: '长安区', label: '长安区' },\n          { value: '桥西区', label: '桥西区' },\n          { value: '新华区', label: '新华区' },\n          { value: '井陉矿区', label: '井陉矿区' },\n          { value: '裕华区', label: '裕华区' },\n          { value: '藁城区', label: '藁城区' },\n          { value: '鹿泉区', label: '鹿泉区' },\n          { value: '栾城区', label: '栾城区' }\n        ]\n      },\n      {\n        value: '唐山市',\n        label: '唐山市',\n        children: [\n          { value: '路南区', label: '路南区' },\n          { value: '路北区', label: '路北区' },\n          { value: '古冶区', label: '古冶区' },\n          { value: '开平区', label: '开平区' },\n          { value: '丰南区', label: '丰南区' },\n          { value: '丰润区', label: '丰润区' },\n          { value: '曹妃甸区', label: '曹妃甸区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '山东省',\n    label: '山东省',\n    children: [\n      {\n        value: '济南市',\n        label: '济南市',\n        children: [\n          { value: '历下区', label: '历下区' },\n          { value: '市中区', label: '市中区' },\n          { value: '槐荫区', label: '槐荫区' },\n          { value: '天桥区', label: '天桥区' },\n          { value: '历城区', label: '历城区' },\n          { value: '长清区', label: '长清区' },\n          { value: '章丘区', label: '章丘区' },\n          { value: '济阳区', label: '济阳区' },\n          { value: '莱芜区', label: '莱芜区' },\n          { value: '钢城区', label: '钢城区' }\n        ]\n      },\n      {\n        value: '青岛市',\n        label: '青岛市',\n        children: [\n          { value: '市南区', label: '市南区' },\n          { value: '市北区', label: '市北区' },\n          { value: '黄岛区', label: '黄岛区' },\n          { value: '崂山区', label: '崂山区' },\n          { value: '李沧区', label: '李沧区' },\n          { value: '城阳区', label: '城阳区' },\n          { value: '即墨区', label: '即墨区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '江苏省',\n    label: '江苏省',\n    children: [\n      {\n        value: '南京市',\n        label: '南京市',\n        children: [\n          { value: '玄武区', label: '玄武区' },\n          { value: '秦淮区', label: '秦淮区' },\n          { value: '建邺区', label: '建邺区' },\n          { value: '鼓楼区', label: '鼓楼区' },\n          { value: '浦口区', label: '浦口区' },\n          { value: '栖霞区', label: '栖霞区' },\n          { value: '雨花台区', label: '雨花台区' },\n          { value: '江宁区', label: '江宁区' },\n          { value: '六合区', label: '六合区' },\n          { value: '溧水区', label: '溧水区' },\n          { value: '高淳区', label: '高淳区' }\n        ]\n      },\n      {\n        value: '苏州市',\n        label: '苏州市',\n        children: [\n          { value: '姑苏区', label: '姑苏区' },\n          { value: '虎丘区', label: '虎丘区' },\n          { value: '吴中区', label: '吴中区' },\n          { value: '相城区', label: '相城区' },\n          { value: '吴江区', label: '吴江区' },\n          { value: '常熟市', label: '常熟市' },\n          { value: '张家港市', label: '张家港市' },\n          { value: '昆山市', label: '昆山市' },\n          { value: '太仓市', label: '太仓市' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '浙江省',\n    label: '浙江省',\n    children: [\n      {\n        value: '杭州市',\n        label: '杭州市',\n        children: [\n          { value: '上城区', label: '上城区' },\n          { value: '下城区', label: '下城区' },\n          { value: '江干区', label: '江干区' },\n          { value: '拱墅区', label: '拱墅区' },\n          { value: '西湖区', label: '西湖区' },\n          { value: '滨江区', label: '滨江区' },\n          { value: '萧山区', label: '萧山区' },\n          { value: '余杭区', label: '余杭区' },\n          { value: '富阳区', label: '富阳区' },\n          { value: '临安区', label: '临安区' }\n        ]\n      },\n      {\n        value: '宁波市',\n        label: '宁波市',\n        children: [\n          { value: '海曙区', label: '海曙区' },\n          { value: '江北区', label: '江北区' },\n          { value: '北仑区', label: '北仑区' },\n          { value: '镇海区', label: '镇海区' },\n          { value: '鄞州区', label: '鄞州区' },\n          { value: '奉化区', label: '奉化区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '安徽省',\n    label: '安徽省',\n    children: [\n      {\n        value: '合肥市',\n        label: '合肥市',\n        children: [\n          { value: '瑶海区', label: '瑶海区' },\n          { value: '庐阳区', label: '庐阳区' },\n          { value: '蜀山区', label: '蜀山区' },\n          { value: '包河区', label: '包河区' },\n          { value: '长丰县', label: '长丰县' },\n          { value: '肥东县', label: '肥东县' },\n          { value: '肥西县', label: '肥西县' },\n          { value: '庐江县', label: '庐江县' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '福建省',\n    label: '福建省',\n    children: [\n      {\n        value: '福州市',\n        label: '福州市',\n        children: [\n          { value: '鼓楼区', label: '鼓楼区' },\n          { value: '台江区', label: '台江区' },\n          { value: '仓山区', label: '仓山区' },\n          { value: '马尾区', label: '马尾区' },\n          { value: '晋安区', label: '晋安区' },\n          { value: '长乐区', label: '长乐区' }\n        ]\n      },\n      {\n        value: '厦门市',\n        label: '厦门市',\n        children: [\n          { value: '思明区', label: '思明区' },\n          { value: '海沧区', label: '海沧区' },\n          { value: '湖里区', label: '湖里区' },\n          { value: '集美区', label: '集美区' },\n          { value: '同安区', label: '同安区' },\n          { value: '翔安区', label: '翔安区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '江西省',\n    label: '江西省',\n    children: [\n      {\n        value: '南昌市',\n        label: '南昌市',\n        children: [\n          { value: '东湖区', label: '东湖区' },\n          { value: '西湖区', label: '西湖区' },\n          { value: '青云谱区', label: '青云谱区' },\n          { value: '湾里区', label: '湾里区' },\n          { value: '青山湖区', label: '青山湖区' },\n          { value: '新建区', label: '新建区' }\n        ]\n      }\n    ]\n  },\n  {\n    value: '广东省',\n    label: '广东省',\n    children: [\n      {\n        value: '广州市',\n        label: '广州市',\n        children: [\n          { value: '荔湾区', label: '荔湾区' },\n          { value: '越秀区', label: '越秀区' },\n          { value: '海珠区', label: '海珠区' },\n          { value: '天河区', label: '天河区' },\n          { value: '白云区', label: '白云区' },\n          { value: '黄埔区', label: '黄埔区' },\n          { value: '番禺区', label: '番禺区' },\n          { value: '花都区', label: '花都区' },\n          { value: '南沙区', label: '南沙区' },\n          { value: '从化区', label: '从化区' },\n          { value: '增城区', label: '增城区' }\n        ]\n      },\n      {\n        value: '深圳市',\n        label: '深圳市',\n        children: [\n          { value: '福田区', label: '福田区' },\n          { value: '罗湖区', label: '罗湖区' },\n          { value: '南山区', label: '南山区' },\n          { value: '宝安区', label: '宝安区' },\n          { value: '龙岗区', label: '龙岗区' },\n          { value: '盐田区', label: '盐田区' },\n          { value: '龙华区', label: '龙华区' },\n          { value: '坪山区', label: '坪山区' },\n          { value: '光明区', label: '光明区' }\n        ]\n      }\n    ]\n  }\n];\n"]}]}