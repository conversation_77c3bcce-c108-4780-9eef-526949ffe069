{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\userremark.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\userremark.vue", "mtime": 1748720501127}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovZW5kaW5nLzI1MDQyNi96ZnJvbnQvY2FtcHVzLXdlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMiI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCmltcG9ydCB7IGxpc3RSZW1hcmsgfSBmcm9tICJAL2FwaS9yZW1hcmsvcmVtYXJrIjsKaW1wb3J0IHsgbWFwU3RhdGUgfSBmcm9tICJ2dWV4IjsKaW1wb3J0IENvbW1vblBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL0NvbW1vblBhZ2luYXRpb24udnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJVc2VyUmVtYXJrIiwKICBjb21wb25lbnRzOiB7CiAgICBDb21tb25QYWdpbmF0aW9uOiBDb21tb25QYWdpbmF0aW9uCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIHJlbWFya+ihqOagvOaVsOaNrgogICAgICByZW1hcmtMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBzdGFyOiBudWxsLAogICAgICAgIHRhc2tJZDogbnVsbCwKICAgICAgICBhY2NlcHRJZDogbnVsbCwKICAgICAgICBwdWJsaXNoSWQ6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHt9LAogICAgICB0YXNrczogW10sCiAgICAgIC8vIOW9k+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["userremark.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA,SAAA,UAAA,QAAA,qBAAA;AACA,SAAA,QAAA,QAAA,MAAA;AACA,OAAA,gBAAA,MAAA,mCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,gBAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA;AACA,MAAA,OAAA,EAAA,IAFA;AAGA;AACA,MAAA,GAAA,EAAA,EAJA;AAKA;AACA,MAAA,MAAA,EAAA,IANA;AAOA;AACA,MAAA,QAAA,EAAA,IARA;AASA;AACA,MAAA,UAAA,EAAA,IAVA;AAWA;AACA,MAAA,KAAA,EAAA,CAZA;AAaA;AACA,MAAA,UAAA,EAAA,EAdA;AAeA;AACA,MAAA,KAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,IAAA,EAAA,KAlBA;AAoBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA;AANA,OArBA;AA8BA;AACA,MAAA,IAAA,EAAA,EA/BA;AAgCA;AACA,MAAA,KAAA,EAAA,EAjCA;AAmCA,MAAA,KAAA,EAAA,EAnCA;AAoCA;AACA,MAAA,WAAA,EAAA,CArCA;AAsCA;AACA,MAAA,QAAA,EAAA;AAvCA,KAAA;AAyCA,GA/CA;AAgDA,EAAA,QAAA,oBACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA,CAhDA;AAmDA,EAAA,OAnDA,qBAmDA;AACA,SAAA,OAAA;AACA,SAAA,YAAA;AACA,GAtDA;AAuDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA;AACA,QAAA,QAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,OAAA,EAAA,KAAA,WAFA;AAGA,QAAA,QAAA,EAAA,KAAA;AAHA,OAAA,CAAA,CAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OARA;AASA,KAbA;AAcA;AACA,IAAA,YAfA,0BAeA;AAAA;;AACA,WAAA,IAAA,CAAA,iBAAA,EAAA;AAAA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAHA;AAIA,KApBA;AAqBA;AACA,IAAA,MAtBA,oBAsBA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAzBA;AA0BA;AACA,IAAA,KA3BA,mBA2BA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,IAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA;AANA,OAAA,CADA,CASA;AACA,KArCA;;AAsCA;AACA,IAAA,WAvCA,yBAuCA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KA1CA;;AA2CA;AACA,IAAA,UA5CA,wBA4CA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KA/CA;AAgDA;AACA,IAAA,qBAjDA,iCAiDA,SAjDA,EAiDA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,EAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KArDA;AAsDA,IAAA,sBAtDA,oCAsDA;AACA,WAAA,OAAA;AACA;AAxDA;AAvDA,CAAA", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"星级\" prop=\"star\">\n        <el-input\n          v-model=\"queryParams.star\"\n          placeholder=\"请输入星级\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务id\" prop=\"taskId\">\n        <el-input\n          v-model=\"queryParams.taskId\"\n          placeholder=\"请输入任务id\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form> -->\n\n\n    <el-table v-loading=\"loading\" :data=\"remarkList\"  @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n       <el-table-column label=\"任务id\" align=\"center\" prop=\"taskId\" />\n      <el-table-column label=\"评价人id\" align=\"center\" prop=\"publishId\" />\n\n      <el-table-column label=\"评分\" min-width=\"100\" align=\"center\" prop=\"star\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            show-text>\n          </el-rate>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"remark\" />\n    </el-table>\n    \n    <div class=\"pagination-wrapper\">\n      <common-pagination\n        :total=\"total\"\n        :current-page.sync=\"currentPage\"\n        :page-size.sync=\"pageSize\"\n        @pagination-change=\"handlePaginationChange\">\n      </common-pagination>\n    </div>\n\n    <!-- 添加或修改remark对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n      \n        <el-form-item label=\"星级\" prop=\"star\">\n            <el-rate\n                v-model=\"form.star\"\n                show-text>\n            </el-rate>\n        </el-form-item>\n        <el-form-item label=\"评价内容\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n        </el-form-item>\n      \n      </el-form>\n\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRemark } from \"@/api/remark/remark\";\nimport {mapState} from \"vuex\"\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n  name: \"UserRemark\",\n  components: {\n    CommonPagination\n  },\n  data() {\n    return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // remark表格数据\n        remarkList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n\n        // 查询参数\n        queryParams: {\n            pageNum: 1,\n            pageSize: 10,\n            star: null,\n            taskId: null,\n            acceptId: null,\n            publishId: null\n        },\n\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n        },\n        tasks: [],\n        // 当前页码\n        currentPage: 1,\n        // 每页显示条数\n        pageSize: 10,\n    };\n  },\n  computed: {\n      ...mapState('user', ['user'])\n  },\n  created() {\n    this.getList();\n    this.retrieveData();\n  },\n  methods: {\n    /** 查询'评价我的'列表 搜索接受任务人为自己 */\n    getList() {\n      this.loading = true;\n      listRemark({\n        acceptId: this.user.id,\n        pageNum: this.currentPage,\n        pageSize: this.pageSize\n      }).then(res => {\n        this.remarkList = res.data.rows;\n        this.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    //任务列表\n    retrieveData() {\n        this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n        // console.log(res.data.task)\n        this.tasks = res.data.task\n        })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        star: null,\n        remark: null,\n        taskId: null,\n        acceptId: null,\n        publishId: null\n      };\n    //   this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handlePaginationChange() {\n      this.getList();\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.pagination-wrapper {\n  margin-top: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/remark"}]}