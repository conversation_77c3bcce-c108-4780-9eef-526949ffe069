{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue", "mtime": 1748450667594}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGUsIG1hcE11dGF0aW9uc30gZnJvbSAidnVleCIKCmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICJIb21lIiwKICAgIG1ldGhvZHM6IHsKICAgICAgICAuLi5tYXBNdXRhdGlvbnMoJ2FkbWluJyxbJ3NldEFkbWluJ10pLAogICAgICAgIGhhbmRsZVNlbGVjdChrZXksIGtleVBhdGgpIHsKICAgICAgICAgICAgLy8gY29uc29sZS5sb2coa2V5LCBrZXlQYXRoKTsKICAgICAgICB9LAogICAgICAgIGdldEJyZWFkY3J1bWIoKXsKICAgICAgICAgICAgbGV0IG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkOwogICAgICAgICAgICBpZihtYXRjaGVkWzBdLm5hbWUgIT0gJ2hvbWUnKXsKICAgICAgICAgICAgICAgIG1hdGNoZWQgPSBbe3BhdGg6Ii9hZG1pbi9ob21lIixtZXRhOnt0aXRsZTon6aaW6aG1J319XS5jb25jYXQobWF0Y2hlZCkKICAgICAgICAgICAgfQogICAgICAgICAgICAvLyBpZiAoIXRoaXMuaXNIb21lKG1hdGNoZWRbMF0pKXsKICAgICAgICAgICAgLy8gICAgIG1hdGNoZWQgPSBbe3BhdGg6Ii9ob21lIixtZXRhOnt0aXRsZTon6aaW6aG1J319XS5jb25jYXQobWF0Y2hlZCk7CiAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgdGhpcy5icmVhZExpc3QgPSBtYXRjaGVkOwogICAgICAgIH0sCiAgICAgICAgZXhpdCgpewogICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbicpOwogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL2FkbWluIikKICAgICAgICB9CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAuLi5tYXBTdGF0ZSgnYWRtaW4nLFsnYWRtaW4nXSkKICAgIH0sCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIC8v5b2T5YmN6Lev55SxCiAgICAgICAgICAgIGJyZWFkTGlzdDpbXSwKICAgICAgICAgICAgLy/lvZPliY3lsY/luZXlrr3luqYKICAgICAgICAgICAgd2luZG93V2lkdGg6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aCwKICAgICAgICAgICAgYWN0aXZlSW5kZXg6ICcxJywKICAgICAgICAgICAgLy/mjqfliLboj5zljZXmmK/lkKblsZXlvIAKICAgICAgICAgICAgaXNDb2xsYXBzZTogZmFsc2UsCiAgICAgICAgfQogICAgfSwKICAgIHdhdGNoOiB7CiAgICAgICAgJyRyb3V0ZScodG8sIGZvcm0pIHsKICAgICAgICAgICAgdGhpcy5nZXRCcmVhZGNydW1iKCk7CiAgICAgICAgfQogICAgfSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgLy8gY29uc29sZS5sb2coc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnYWRtaW4nKSkKICAgICAgICBpZiAoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgiYWRtaW4iKSl7CiAgICAgICAgICAgIHRoaXMuc2V0QWRtaW4oSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJhZG1pbiIpKSk7CiAgICAgICAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgICAgICAgICAvKmxldCBmbGFnID0gbmF2aWdhdG9yLnVzZXJBZ2VudC5tYXRjaCgvKHBob25lfHBhZHxwb2R8aVBob25lfGlQb2R8aW9zfGlQYWR8QW5kcm9pZHxNb2JpbGV8QmxhY2tCZXJyeXxJRU1vYmlsZXxNUVFCcm93c2VyfEpVQ3xGZW5uZWN8d09TQnJvd3NlcnxCcm93c2VyTkd8V2ViT1N8U3ltYmlhbnxXaW5kb3dzIFBob25lKS9pKQogICAgICAgICAgICBjb25zb2xlLmxvZyhmbGFnKQogICAgICAgICAgICBpZiAoZmxhZykgewogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goIm0vbG9naW4iKQogICAgICAgICAgICB9Ki8KICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1zZygi5bCa5pyq55m76ZmGIiwiZXJyb3IiKQogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL2FkbWluLyIpCiAgICAgICAgfQoKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICAgIHdpbmRvdy5vbnJlc2l6ZSA9ICgpID0+IHsKICAgICAgICAgICAgdGhpcy53aW5kb3dXaWR0aCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aAogICAgICAgIH0KICAgIH0sCn0KCg=="}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/admin", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"logo\">售后维修一站通</div>\n\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/admin/home\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>类别管理</span>\n                    </template>\n                    <el-menu-item index=\"/admin/home/<USER>\">\n                       <i class=\"el-icon-s-order\"></i>\n                            <span>类别信息</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"4\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>用户管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>用户信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"5\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>任务管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>任务信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"6\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>评价管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>评价信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"7\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-refresh\"></i>\n                        <span>公告管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>系统公告</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"8\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>论坛管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>/create\">\n                            <i class=\"el-icon-edit\"></i>\n                            <span>发布帖子</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/audit\">\n                            <i class=\"el-icon-s-check\"></i>\n                            <span>帖子审核</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/posts\">\n                            <i class=\"el-icon-document\"></i>\n                            <span>帖子列表</span>\n                        </el-menu-item>\n                </el-submenu>\n\n            </el-menu>\n        </div>\n        <div class=\"right\" :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"top\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\"></i>\n                </div>\n                <el-menu\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        @select=\"handleSelect\"\n                        menu-trigger=\"click\">\n                    <el-submenu index=\"2\">\n                        <template slot=\"title\">{{admin.account}}</template>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view></router-view>\n                </transition>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('admin',['setAdmin']),\n            handleSelect(key, keyPath) {\n                // console.log(key, keyPath);\n            },\n            getBreadcrumb(){\n                let matched = this.$route.matched;\n                if(matched[0].name != 'home'){\n                    matched = [{path:\"/admin/home\",meta:{title:'首页'}}].concat(matched)\n                }\n                // if (!this.isHome(matched[0])){\n                //     matched = [{path:\"/home\",meta:{title:'首页'}}].concat(matched);\n                // }\n                this.breadList = matched;\n            },\n            exit(){\n                sessionStorage.removeItem('admin');\n                this.$router.push(\"/admin\")\n            }\n        },\n        computed: {\n            ...mapState('admin',['admin'])\n        },\n        data() {\n            return {\n                //当前路由\n                breadList:[],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb();\n            }\n        },\n        created() {\n            // console.log(sessionStorage.getItem('admin'))\n            if (sessionStorage.getItem(\"admin\")){\n                this.setAdmin(JSON.parse(sessionStorage.getItem(\"admin\")));\n                this.getBreadcrumb();\n                /*let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)\n                console.log(flag)\n                if (flag) {\n                    this.$router.push(\"m/login\")\n                }*/\n            }else {\n                this.$msg(\"尚未登陆\",\"error\")\n                this.$router.push(\"/admin/\")\n            }\n\n        },\n        mounted() {\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n        },\n    }\n\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 100%;\n\n        .left {\n            background: #fff;\n            position: fixed;\n            height: 100%;\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto;\n            }\n        }\n\n        .right {\n            position: relative;\n            .top {\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                background: #fff;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                /*height: 100%;*/\n                /*background: #fff;*/\n\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n    }\n</style>"]}]}