{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue?vue&type=style&index=0&id=59823858&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue", "mtime": 1748722940092}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jb250ZW50IHsKICAgIGJhY2tncm91bmQ6ICNGRmY7CiAgICBtYXJnaW46IDAgMTVweDsKICAgIHBhZGRpbmc6IDE1cHg7Cn0KCi5zdGF0dXMtdGFicyB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgIC9kZWVwLyAuZWwtdGFic19faGVhZGVyIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgfQoKICAgIC9kZWVwLyAuZWwtdGFic19faXRlbSB7CiAgICAgICAgaGVpZ2h0OiA0MHB4OwogICAgICAgIGxpbmUtaGVpZ2h0OiA0MHB4OwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzYwNjI2NjsKCiAgICAgICAgJi5pcy1hY3RpdmUgewogICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["Published.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkXA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Published.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>已发布任务</span>\n            </div>\n\n            <!-- 状态分类标签 -->\n            <div class=\"status-tabs\">\n                <el-tabs v-model=\"activeStatus\" type=\"card\">\n                    <el-tab-pane\n                        v-for=\"group in tasksByStatus\"\n                        :key=\"group.status\"\n                        :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                        :name=\"group.status.toString()\"\n                    >\n                        <el-card\n                            class=\"box-card\"\n                            v-for=\"item in group.tasks\"\n                            :key=\"item.id\"\n                            style=\"margin-top: 20px\"\n                        >\n                <div slot=\"header\" class=\"clearfix\"\n                     style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span style=\"display: flex;align-items: center\">\n                            <el-tag :type=\"item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')\"\n                                    style=\"margin-right: 5px\">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>\n                            {{item.taskTitle}}\n                        </span>\n\n                    <!-- 评价按钮 -->\n                    <el-button v-show=\"item.state == 2\"\n                    style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"remark(item)\">订单评价</el-button>\n\n                    <!-- 在线交流按钮 -->\n                    <el-button v-show=\"item.state == 1\"\n                    style=\"float: right; padding: 3px 0; margin-right: 10px\" type=\"text\" @click=\"openChat(item)\">\n                        <i class=\"el-icon-chat-dot-round\"></i> 在线交流\n                    </el-button>\n\n                    <el-button style=\"float: right; padding: 3px 0\" type=\"text\" v-show=\"item.state != 0\"\n                               @click=\"receiver(item)\">查看维修员信息\n                    </el-button>\n                    <template>\n<!--                        <i class=\"el-icon-edit\" style=\"cursor: pointer; color: #66b1ff\" v-show=\"item.state == 0\"/>-->\n                        <el-popconfirm title=\"确定取消任务吗？\" @confirm=\"cancel(item.id)\" v-show=\"item.state == 0\">\n                            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" slot=\"reference\">取消任务\n                            </el-button>\n                        </el-popconfirm>\n                    </template>\n                </div>\n\n                <el-steps :active=\"item.state + 1\" finish-status=\"success\">\n                    <el-step title=\"发布成功\" :description=\"item.createTime | formatDate\"></el-step>\n                    <el-step title=\"服务中\" :description=\"item.orderTime ? transform(item.orderTime):'暂时没人服务'\"></el-step>\n                    <el-step title=\"完成时间\" :description=\"item.endTime ? transform(item.endTime):''\"></el-step>\n                </el-steps>\n\n                <el-collapse style=\"margin-top: 20px\" v-model=\"activeNames\">\n                    <el-collapse-item title=\"任务内容\" name=\"1\">\n                        <div>{{item.taskContext}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"任务金额\" name=\"2\">\n                        <div><i class=\"el-icon-money\" style=\"color: red;\"> {{item.reward}}元</i></div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"发布时间\" name=\"3\">\n                        <div>{{item.createTime | formatDate}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"类别\" name=\"4\">\n                        <div>{{item.dept ? item.dept.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"子类别\" name=\"5\">\n                        <div>{{item.type ? item.type.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"地址\" name=\"6\" v-if=\"item.province\">\n                        <div>{{item.province}} {{item.city}} {{item.district}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"详细地址\" name=\"7\" v-if=\"item.address\">\n                        <div>{{item.address}}</div>\n                    </el-collapse-item>\n                </el-collapse>\n\n                <el-button type=\"primary\" style=\"float: right;margin:10px 0;\" @click=\"completeTask(item.id)\"\n                           v-show=\"item.state==1\">完成任务\n                </el-button>\n\n                        </el-card>\n\n                        <!-- 当前状态下没有任务时显示 -->\n                        <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                            <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无发布任务</span>\n                        </div>\n                    </el-tab-pane>\n                </el-tabs>\n            </div>\n\n            <!-- 没有任何任务时显示 -->\n            <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                <span><i class=\"el-icon-refresh-right\"></i>暂无发布任务</span>\n            </div>\n        </el-card>\n\n        <el-drawer\n                title=\"维修员信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\">\n            <div class=\"content_drawer\">\n                <el-card class=\"box-card\" v-if=\"recipientInformation != ''\">\n                    <el-collapse v-model=\"drawerNames\">\n                        <el-collapse-item title=\"姓名\" name=\"1\">\n                            <div>{{recipientInformation.username}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"电话\" name=\"2\">\n                            <div>{{recipientInformation.phone}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"角色\" name=\"3\">\n                            <div>{{recipientInformation.role.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"类别\" name=\"4\">\n                            <div>{{recipientInformation.dept.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"子类别\" name=\"5\">\n                            <div>{{recipientInformation.type.name}}</div>\n                        </el-collapse-item>\n                    </el-collapse>\n                </el-card>\n            </div>\n        </el-drawer>\n\n        <!-- 添加或修改remark对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\"  :rules=\"rules\" label-width=\"80px\" >\n            <el-form-item label=\"星级\" prop=\"star\">\n                <el-rate\n                    v-model=\"form.star\"\n                    show-text>\n                </el-rate>\n            </el-form-item>\n            <el-form-item label=\"评价内容\" prop=\"remark\">\n            <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n            </el-form-item>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"exit\">取 消</el-button>\n        </div>\n        </el-dialog>\n\n        <!-- 聊天组件 -->\n        <Chat\n            :visible.sync=\"chatVisible\"\n            :task=\"currentChatTask\"\n            :current-user-id=\"user.id\"\n            v-if=\"currentChatTask\" />\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\"\n    import {formatDate} from '@/util/date';\n    import { addRemark, } from \"@/api/remark/remark\";\n    import Chat from '@/components/Chat.vue';\n\n    export default {\n        name: \"Published\",\n        components: {\n            Chat\n        },\n        data() {\n            return {\n                // 是否显示弹出层\n                open: false,\n                activeNames: ['1', '2', '3', '4', '5', '6', '7'],\n                drawerNames: ['1', '2', '3', '4', '5'],\n                tasks: [],\n                drawer: false,\n                recipientInformation: [],\n                // 当前选中的状态\n                activeStatus: '-1',\n                // 表单参数\n                form: {},\n                // 表单校验\n                rules: {\n                },\n                // 弹出层标题\n                title: \"\",\n                // 当前选中的任务\n                currentTask: null,\n                // 聊天相关\n                chatVisible: false,\n                currentChatTask: null,\n            };\n        },\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    '0': { name: '待接单', tasks: [] },\n                    '1': { name: '服务中', tasks: [] },\n                    '2': { name: '已完成', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.tasks;\n\n                // 按状态分组\n                this.tasks.forEach(task => {\n                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '0';\n                    if (statusMap[state]) {\n                        statusMap[state].tasks.push(task);\n                    }\n                });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.retrieveData()\n        },\n        methods: {\n            retrieveData() {\n                this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n                    console.log(res.data.task)\n                    this.tasks = res.data.task\n                })\n            },\n            receiver(val) {\n                console.log(val)\n                this.recipientInformation = val.accept;\n                // console.log(this.recipientInformation)\n                this.drawer = true\n            },\n\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n\n            cancel(id) {\n                this.$del(\"/task/\" + id)\n                    .then(res => {\n                        this.retrieveData()\n                        this.$notifyMsg('成功', res.data.msg, \"success\");\n                    })\n            },\n            completeTask(id) {\n                this.$msgbox({\n                    title: '提示',\n                    message: '确定该维修员完成此任务了吗？',\n                    showCancelButton: true,\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    beforeClose: ((action, instance, done) => {\n                        if (action == 'confirm') {\n                            // instance.confirmButtonLoading = true;\n                            instance.confirmButtonText = '执行中...';\n                            this.$put('task/' + id)\n                                .then((res) => {\n                                    done();\n                                    instance.confirmButtonLoading = false;\n                                    this.$msg(res.data.msg, \"success\");\n                                    this.retrieveData();\n\n                                    // 如果返回了更新后的用户信息，更新当前用户数据\n                                    if (res.data.data && res.data.data.id) {\n                                        // 检查返回的用户是否是当前登录用户\n                                        if (res.data.data.id === this.user.id) {\n                                            console.log('更新当前用户余额信息:', res.data.data);\n                                            this.$store.commit('user/setUser', res.data.data);\n                                        }\n                                    }\n                                })\n                        } else {\n                            done();\n                        }\n                    })\n                }).catch(() => {\n                })\n            },\n            // 评价用户\n            remark(task){\n                this.currentTask = task;\n                this.open = true;\n                this.title = \"添加评价\";\n            },\n\n            /** 提交按钮 */\n            submitForm() {\n                if(this.form.star==null){\n                    this.$message(\"请输入星级\");\n                    return;\n                }\n\n                if(this.form.remark==null || this.form.remark.trim() === ''){\n                    this.$message(\"请输入评价内容\");\n                    return;\n                }\n\n                if(!this.currentTask || !this.currentTask.accept){\n                    this.$message.error(\"任务信息不完整，无法提交评价\");\n                    return;\n                }\n\n                const aid = this.currentTask.accept.id;\n                const taskid = this.currentTask.id;\n                const pid = this.currentTask.publish.id;\n\n                console.log('提交评价:', aid, taskid, pid);\n\n                addRemark({\n                    \"star\": this.form.star,\n                    \"remark\": this.form.remark,\n                    \"acceptId\": aid,\n                    \"publishId\": pid,\n                    \"taskId\": taskid,\n                }).then(() => {\n                    this.$message.success(\"评价提交成功\");\n                    this.open = false;\n                    this.reset();\n                    this.retrieveData(); // 刷新任务列表\n                }).catch(error => {\n                    console.error('评价提交失败:', error);\n                    this.$message.error(\"评价提交失败，请稍后重试\");\n                });\n            },\n\n            // 取消按钮\n            exit() {\n                this.open = false;\n                this.reset();\n                this.currentTask = null;\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    star: null,\n                    remark: null,\n                };\n            },\n\n            /**\n             * 打开在线交流\n             * @param {Object} task 任务对象\n             */\n            openChat(task) {\n                this.currentChatTask = task;\n                this.chatVisible = true;\n            },\n        },\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n    }\n\n    .status-tabs {\n        margin-bottom: 20px;\n\n        /deep/ .el-tabs__header {\n            margin-bottom: 15px;\n        }\n\n        /deep/ .el-tabs__item {\n            height: 40px;\n            line-height: 40px;\n            font-size: 14px;\n            color: #606266;\n\n            &.is-active {\n                color: #409EFF;\n                font-weight: bold;\n            }\n        }\n    }\n</style>\n"]}]}