{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue", "mtime": 1745149170618}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumAudit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AdminForumAudit.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"audit-posts-container\">\n        <div class=\"page-header\">\n            <h2>帖子审核</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已通过\" name=\"approved\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已通过</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <template v-if=\"post.status === 0\">\n                            <el-button size=\"mini\" type=\"success\" @click=\"approvePost(post.id)\">通过</el-button>\n                            <el-button size=\"mini\" type=\"danger\" @click=\"rejectPost(post.id)\">拒绝</el-button>\n                        </template>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" :description=\"getEmptyDescription()\"></el-empty>\n        </div>\n\n        <!-- 拒绝原因对话框 -->\n        <el-dialog title=\"拒绝原因\" :visible.sync=\"rejectDialogVisible\" width=\"30%\">\n            <el-form :model=\"rejectForm\" :rules=\"rejectRules\" ref=\"rejectForm\">\n                <el-form-item label=\"原因\" prop=\"reason\">\n                    <el-input\n                        type=\"textarea\"\n                        v-model=\"rejectForm.reason\"\n                        placeholder=\"请输入拒绝原因\"\n                        :rows=\"4\"\n                    ></el-input>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"rejectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"submitting\">确定</el-button>\n            </span>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumAudit',\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'pending',\n            rejectDialogVisible: false,\n            rejectForm: {\n                postId: null,\n                reason: ''\n            },\n            rejectRules: {\n                reason: [\n                    { required: true, message: '请输入拒绝原因', trigger: 'blur' },\n                    { min: 5, max: 200, message: '原因长度在5到200个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'approved') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        if (!sessionStorage.getItem('admin')) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n            // 使用实际的管理员ID\n            const admin = JSON.parse(sessionStorage.getItem('admin'));\n            const userId = admin.id; // 管理员ID\n\n            this.$get('/forum/post/list', {\n                pageNum: 1,\n                pageSize: 100, // 设置较大的页面大小以获取所有帖子\n                userId: userId,\n                isAdminRequest: true // 标记这是管理员请求\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/admin/home/<USER>/post/${id}`);\n        },\n        approvePost(id) {\n            this.$confirm('确定通过这篇帖子吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'info'\n            }).then(() => {\n                this.$put(`/forum/post/audit/${id}?status=1`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('审核通过成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '操作失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('审核操作失败:', err);\n                        this.$message.error('审核操作失败');\n                    });\n            }).catch(() => {\n                // 取消操作\n            });\n        },\n        rejectPost(id) {\n            this.rejectForm.postId = id;\n            this.rejectForm.reason = '';\n            this.rejectDialogVisible = true;\n        },\n        confirmReject() {\n            this.$refs.rejectForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n\n                    this.$put(`/forum/post/audit/${this.rejectForm.postId}?status=2&reason=${encodeURIComponent(this.rejectForm.reason)}`)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('拒绝成功');\n                                this.rejectDialogVisible = false;\n                                this.fetchPosts(); // 重新加载帖子列表\n                            } else {\n                                this.$message.error(res.data.msg || '操作失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('拒绝操作失败:', err);\n                            this.$message.error('拒绝操作失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const adminId = JSON.parse(sessionStorage.getItem('admin')).id;\n                this.$del(`/forum/post/${id}?operatorId=${adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        getEmptyDescription() {\n            if (this.activeTab === 'pending') {\n                return '暂无待审核帖子';\n            } else if (this.activeTab === 'approved') {\n                return '暂无已通过帖子';\n            } else if (this.activeTab === 'rejected') {\n                return '暂无已拒绝帖子';\n            }\n            return '暂无帖子';\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.audit-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}