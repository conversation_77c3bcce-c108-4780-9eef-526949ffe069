{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\adviseuser.vue?vue&type=style&index=0&id=b43302c0&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\adviseuser.vue", "mtime": 1748720500260}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKL2RlZXAvIC5lbC1pbnB1dF9faW5uZXJ7CnRleHQtYWxpZ24gOiBjZW50ZXI7Cn0KCi5wYWdpbmF0aW9uLXdyYXBwZXIgewogIG1hcmdpbi10b3A6IDIwcHg7Cn0KCg=="}, {"version": 3, "sources": ["adviseuser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqNA;AACA;AACA;;AAEA;AACA;AACA", "file": "adviseuser.vue", "sourceRoot": "src/views/notice", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n        <el-input\n          v-model=\"queryParams.noticeTitle\"\n          placeholder=\"请输入公告标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n        <el-input\n          v-model=\"queryParams.noticeContent\"\n          placeholder=\"请输入公告内容\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n      </el-form-item>\n    </el-form>\n\n\n    <el-table v-loading=\"loading\" :data=\"adviseList\" >\n      <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n      <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n      <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ transform(scope.row.createTime) }}</span>\n        </template> \n      </el-table-column>\n\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n<!--          <el-button-->\n<!--            size=\"mini\"-->\n<!--            type=\"text\"-->\n<!--            icon=\"el-icon-edit\"-->\n<!--            @click=\"handleUpdate(scope.row)\"-->\n<!--          >查看公告</el-button>-->\n            <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    @click=\"handleUpdate(scope.row)\"\n            >查看公告</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <div class=\"pagination-wrapper\">\n      <common-pagination\n        :total=\"total\"\n        :current-page.sync=\"currentPage\"\n        :page-size.sync=\"pageSize\"\n        @pagination-change=\"handlePaginationChange\">\n      </common-pagination>\n    </div>\n\n    <!-- 添加或修改通知公告公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"743px\" append-to-body>\n\n        <!-- <el-table v-loading=\"loading\" :data=\"adviseList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n        <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n        <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n            <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit\"\n                @click=\"handleUpdate(scope.row)\"\n            >查看公告</el-button>\n            </template>\n        </el-table-column>\n        </el-table> -->\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n            <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n            <el-input v-model=\"form.noticeTitle\" placeholder=\"请输入公告标题\" />\n            </el-form-item>\n            <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n              <el-input\n                            resize=\"none\"\n                            type=\"textarea\"\n                            :autosize=\"{ minRows: 6, maxRows: 10}\"\n                            placeholder=\"请输入公告内容\"\n                            v-model=\"form.noticeContent\" style=\"padding: 0\">\n              </el-input>\n              <!-- <quill-editor v-model=\"form.noticeContent\" placeholder=\"请输入公告内容\" /> -->\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"remark\">\n            <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n            <el-form-item label=\"创建者\" prop=\"remark\">\n            <el-input v-model=\"form.createBy\" placeholder=\"请输入备注\" />\n            </el-form-item>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"cancel\">确 定</el-button>\n        </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listAdvise, getAdvise} from \"@/api/notice/advise\";\n  import {formatDate} from '@/util/date';\n  import CommonPagination from '@/components/CommonPagination.vue';\n\n  export default {\n    name: \"Advise\",\n    components: {\n      CommonPagination\n    },\n    data() {\n      return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 通知公告公告表格数据\n        adviseList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 当前页码\n        currentPage: 1,\n        // 每页显示条数\n        pageSize: 10,\n        // 查询参数\n        queryParams: {\n          noticeTitle: null,\n          noticeContent: null,\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n          noticeTitle: [\n            { required: true, message: \"公告标题不能为空\", trigger: \"blur\" }\n          ],\n        }\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询通知公告列表 */\n      getList() {\n        this.loading = true;\n        listAdvise({\n          pageNum: this.currentPage,\n          pageSize: this.pageSize,\n          ...this.queryParams\n        }).then(response => {\n          this.adviseList = response.data.rows;\n          this.total = response.data.total;\n          this.loading = false;\n        });\n      },\n\n      /** 修改按钮操作 */\n      handleUpdate(row) {\n        const noticeId = row.noticeId || this.ids\n        getAdvise(noticeId).then(response => {\n          this.form = response.data.data;\n          this.open = true;\n          this.title = \"查看通知公告\";\n        });\n      },\n      // 取消按钮\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.currentPage = 1;\n        this.getList();\n      },\n      transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd');\n      },\n      handlePaginationChange() {\n        this.getList();\n      },\n      reset() {\n        this.form = {};\n      }\n    }\n  };\n</script>\n\n<style scoped lang=\"less\">\n\n    /deep/ .el-input__inner{\n    text-align : center;\n    }\n\n    .pagination-wrapper {\n      margin-top: 20px;\n    }\n\n</style>\n"]}]}