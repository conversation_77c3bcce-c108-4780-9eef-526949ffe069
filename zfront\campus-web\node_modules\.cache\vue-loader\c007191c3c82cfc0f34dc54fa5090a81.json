{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue?vue&type=style&index=0&id=2973d698&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue", "mtime": 1745151314454}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNyZWF0ZS1wb3N0LWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAyMHB4Owp9CgoucGFnZS1oZWFkZXIgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnBvc3QtZm9ybSB7CiAgICBtYXgtd2lkdGg6IDgwMHB4Owp9Cg=="}, {"version": 3, "sources": ["AdminForumPostCreate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "AdminForumPostCreate.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"create-post-container\">\n        <div class=\"page-header\">\n            <h2>发布帖子</h2>\n        </div>\n\n        <el-form :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">发布</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostCreate',\n    data() {\n        return {\n            postForm: {\n                title: '',\n                content: '',\n                authorId: JSON.parse(sessionStorage.getItem('admin')).id // 使用管理员ID\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    methods: {\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    this.$post('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('发布成功'); // 管理员发布无需审核\n                                this.$router.push('/admin/home/<USER>/posts');\n                            } else {\n                                this.$message.error(res.data.msg || '发布失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('发布帖子失败:', err);\n                            this.$message.error('发布帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.create-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n</style>\n"]}]}