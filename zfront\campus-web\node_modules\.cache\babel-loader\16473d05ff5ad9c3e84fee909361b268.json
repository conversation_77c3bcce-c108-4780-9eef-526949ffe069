{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\src\\api\\notice\\advise.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\api\\notice\\advise.js", "mtime": 1737774014078}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlsL3JlcXVlc3QnOyAvLyDmn6Xor6LpgJrnn6XlhazlkYrlhazlkYrliJfooagKCmV4cG9ydCBmdW5jdGlvbiBsaXN0QWR2aXNlKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL25vdGljZS9hZHZpc2UvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9IC8vIOafpeivoumAmuefpeWFrOWRiuWFrOWRiuivpue7hgoKZXhwb3J0IGZ1bmN0aW9uIGdldEFkdmlzZShub3RpY2VJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9ub3RpY2UvYWR2aXNlLycgKyBub3RpY2VJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfSAvLyDmlrDlop7pgJrnn6XlhazlkYrlhazlkYoKCmV4cG9ydCBmdW5jdGlvbiBhZGRBZHZpc2UoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9ub3RpY2UvYWR2aXNlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9IC8vIOS/ruaUuemAmuefpeWFrOWRiuWFrOWRigoKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZUFkdmlzZShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL25vdGljZS9hZHZpc2UnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfSAvLyDliKDpmaTpgJrnn6XlhazlkYrlhazlkYoKCmV4cG9ydCBmdW5jdGlvbiBkZWxBZHZpc2Uobm90aWNlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvbm90aWNlL2FkdmlzZS8nICsgbm90aWNlSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/src/api/notice/advise.js"], "names": ["request", "listAdvise", "query", "url", "method", "params", "getAdvise", "noticeId", "addAdvise", "data", "updateAdvise", "delAdvise"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB,C,CAEA;;AACA,OAAO,SAASC,UAAT,CAAoBC,KAApB,EAA2B;AAChC,SAAOF,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,qBADQ;AAEbC,IAAAA,MAAM,EAAE,KAFK;AAGbC,IAAAA,MAAM,EAAEH;AAHK,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASI,SAAT,CAAmBC,QAAnB,EAA6B;AAClC,SAAOP,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,oBAAoBI,QADZ;AAEbH,IAAAA,MAAM,EAAE;AAFK,GAAD,CAAd;AAID,C,CAED;;AACA,OAAO,SAASI,SAAT,CAAmBC,IAAnB,EAAyB;AAC9B,SAAOT,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,gBADQ;AAEbC,IAAAA,MAAM,EAAE,MAFK;AAGbK,IAAAA,IAAI,EAAEA;AAHO,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASC,YAAT,CAAsBD,IAAtB,EAA4B;AACjC,SAAOT,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,gBADQ;AAEbC,IAAAA,MAAM,EAAE,KAFK;AAGbK,IAAAA,IAAI,EAAEA;AAHO,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASE,SAAT,CAAmBJ,QAAnB,EAA6B;AAClC,SAAOP,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,oBAAoBI,QADZ;AAEbH,IAAAA,MAAM,EAAE;AAFK,GAAD,CAAd;AAID", "sourcesContent": ["import request from '@/util/request'\n\n// 查询通知公告公告列表\nexport function listAdvise(query) {\n  return request({\n    url: '/notice/advise/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询通知公告公告详细\nexport function getAdvise(noticeId) {\n  return request({\n    url: '/notice/advise/' + noticeId,\n    method: 'get'\n  })\n}\n\n// 新增通知公告公告\nexport function addAdvise(data) {\n  return request({\n    url: '/notice/advise',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改通知公告公告\nexport function updateAdvise(data) {\n  return request({\n    url: '/notice/advise',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除通知公告公告\nexport function delAdvise(noticeId) {\n  return request({\n    url: '/notice/advise/' + noticeId,\n    method: 'delete'\n  })\n}\n"]}]}