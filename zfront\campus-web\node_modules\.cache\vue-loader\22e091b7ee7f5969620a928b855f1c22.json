{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=style&index=0&id=115c3ebc&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748718293087}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jb250ZW50IHsKICAgIHBhZGRpbmc6IDAgMSU7Cn0KCi50b3AtYWN0aW9ucyB7CiAgICB0ZXh0LWFsaWduOiBsZWZ0OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmRyYXdlci1mb290ZXIgewogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICBtYXJnaW4tdG9wOiAzMHB4OwogICAgcGFkZGluZy10b3A6IDIwcHg7CiAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2U4ZThlODsKfQoKLmRyYXdlci1mb290ZXIgLmVsLWJ1dHRvbiB7CiAgICBtYXJnaW4tbGVmdDogMTBweDsKfQoKLy8g5pON5L2c5oyJ6ZKu5qC35byP5LyY5YyWCi5lbC10YWJsZSB7CiAgICAvZGVlcC8gLmVsLWJ1dHRvbi0tdGV4dCB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICAgICY6bGFzdC1jaGlsZCB7CiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMDsKICAgICAgICB9CiAgICB9CiAgICAvZGVlcC8gLmVsLWJ1dHRvbiBbY2xhc3MqPSJlbC1pY29uLSJdICsgc3BhbiB7CiAgICAgICAgbWFyZ2luLWxlZnQ6IDNweDsKICAgIH0KfQoKLy8g6KeS6Imy5LiN5Y+v5pON5L2c5o+Q56S65qC35byPCi5lbC10YWJsZSAucm9sZS1uby1vcGVyYXRpb24gewogICAgY29sb3I6ICM5MDkzOTk7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBmb250LXN0eWxlOiBpdGFsaWM7Cn0K"}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAohBA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Role.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <!-- 顶部操作按钮 -->\n        <div class=\"top-actions\" style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAddCategory\">添加维修类别</el-button>\n        </div>\n\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%\"\n                :resizable=\"false\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"维修类别\"\n                    sortable\n                    min-width=\"120\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"维修子类别\"\n                    min-width=\"120\">\n            </el-table-column>\n\n            <el-table-column \n                    label=\"操作\" \n                    width=\"280\"\n                    align=\"center\"\n                    header-align=\"center\"\n                    fixed=\"right\">\n                <template slot-scope=\"scope\">\n                    <!-- 维修类别级别：可以增加子类别、修改、删除 -->\n                    <template v-if=\"scope.row.deptsId\">\n                        <el-button \n                            type=\"text\" \n                            size=\"small\"\n                            @click=\"handleAddSubcategory(scope.row)\">\n                            <i class=\"el-icon-plus\" style=\"color: #409EFF\"></i>\n                            添加子类别\n                        </el-button>\n                        <el-button \n                            type=\"text\" \n                            size=\"small\"\n                            @click=\"handleUpdateCategory(scope.row)\">\n                            <i class=\"el-icon-edit\" style=\"color: #409EFF\"></i>\n                            修改\n                        </el-button>\n                        <el-popconfirm\n                            confirm-button-text='确定'\n                            cancel-button-text='取消'\n                            icon=\"el-icon-info\"\n                            icon-color=\"red\"\n                            title=\"确定删除该维修类别吗？删除后其下的所有维修子类别也将被删除。\"\n                            @confirm=\"handleDeleteCategory(scope.row)\"\n                        >\n                            <el-button type=\"text\" size=\"small\" slot=\"reference\">\n                                <i class=\"el-icon-delete\" style=\"color: red\"></i>\n                                删除\n                            </el-button>\n                        </el-popconfirm>\n                    </template>\n\n                    <!-- 维修子类别级别：可以修改、删除 -->\n                    <template v-else-if=\"scope.row.classesId\">\n                        <el-button \n                            type=\"text\" \n                            size=\"small\"\n                            @click=\"handleUpdateSubcategory(scope.row)\">\n                            <i class=\"el-icon-edit\" style=\"color: #409EFF\"></i>\n                            修改\n                        </el-button>\n                        <el-popconfirm\n                            confirm-button-text='确定'\n                            cancel-button-text='取消'\n                            icon=\"el-icon-info\"\n                            icon-color=\"red\"\n                            title=\"确定删除该维修子类别吗？\"\n                            @confirm=\"handleDeleteSubcategory(scope.row)\"\n                        >\n                            <el-button type=\"text\" size=\"small\" slot=\"reference\">\n                                <i class=\"el-icon-delete\" style=\"color: red\"></i>\n                                删除\n                            </el-button>\n                        </el-popconfirm>\n                    </template>\n                </template>\n            </el-table-column>\n        </el-table>\n\n        <!-- 添加维修类别弹窗 -->\n        <el-dialog\n            :title=\"categoryDialogTitle\"\n            :visible.sync=\"categoryDialogVisible\"\n            width=\"500px\"\n            :before-close=\"handleCategoryDialogClose\">\n\n            <el-form ref=\"categoryForm\" :model=\"categoryForm\" :rules=\"categoryRules\" label-width=\"120px\">\n                <el-form-item label=\"维修员角色\" prop=\"roleId\">\n                    <el-select v-model=\"categoryForm.roleId\" placeholder=\"请选择维修员角色\" style=\"width: 100%;\" :disabled=\"isCategoryEdit\">\n                        <el-option\n                            v-for=\"role in roleOptions\"\n                            :key=\"role.id\"\n                            :label=\"role.name\"\n                            :value=\"role.id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"维修类别名称\" prop=\"name\">\n                    <el-input v-model=\"categoryForm.name\" placeholder=\"请输入维修类别名称\" />\n                </el-form-item>\n            </el-form>\n\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"handleCategoryDialogClose\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitCategoryForm\">确 定</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 抽屉式表单（仅用于子类别） -->\n        <el-drawer\n            :title=\"drawerTitle\"\n            :visible.sync=\"drawerVisible\"\n            direction=\"rtl\"\n            size=\"400px\"\n            :before-close=\"handleDrawerClose\">\n\n            <div style=\"padding: 20px;\">\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n                    <!-- 子类别表单 -->\n                    <el-form-item label=\"维修员角色\">\n                        <el-input :value=\"parentRoleName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"所属维修类别\">\n                        <el-input :value=\"parentCategoryName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"维修子类别名称\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入维修子类别名称\" />\n                    </el-form-item>\n                </el-form>\n\n                <div class=\"drawer-footer\">\n                    <el-button @click=\"handleDrawerClose\">取 消</el-button>\n                    <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                </div>\n            </div>\n        </el-drawer>\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                roles: [],\n                roleOptions: [], // 角色选项列表\n\n                // 维修类别弹窗相关\n                categoryDialogVisible: false,\n                categoryDialogTitle: '',\n                isCategoryEdit: false, // 是否为编辑模式\n\n                // 维修类别表单参数\n                categoryForm: {\n                    id: null,\n                    name: '',\n                    roleId: null\n                },\n\n                // 维修类别表单校验\n                categoryRules: {\n                    name: [\n                        { required: true, message: '请输入维修类别名称', trigger: 'blur' }\n                    ],\n                    roleId: [\n                        { required: true, message: '请选择维修员角色', trigger: 'change' }\n                    ]\n                },\n\n                // 抽屉相关（仅用于子类别）\n                drawerVisible: false,\n                drawerTitle: '',\n                isEdit: false, // 是否为编辑模式\n\n                // 子类别表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                },\n\n                // 父级信息（用于子类别表单显示）\n                parentRoleName: '',\n                parentCategoryName: '',\n                parentCategoryId: null,\n                parentRoleId: null,\n\n                // 子类别表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入维修子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新的方法---------\n\n            // 添加维修类别\n            handleAddCategory() {\n                this.resetCategoryForm();\n                this.isCategoryEdit = false;\n                this.categoryDialogTitle = '添加维修类别';\n                this.categoryDialogVisible = true;\n            },\n\n            // 添加子类别\n            handleAddSubcategory(categoryRow) {\n                this.resetForm();\n                this.formType = 'subcategory';\n                this.isEdit = false;\n                this.drawerTitle = '添加维修子类别';\n\n                // 设置父级信息\n                this.parentCategoryId = categoryRow.deptsId;\n                this.parentCategoryName = categoryRow.deptname;\n                this.parentRoleId = categoryRow.roleId;\n                this.parentRoleName = categoryRow.roleName;\n\n                this.drawerVisible = true;\n            },\n\n            // 修改维修类别\n            handleUpdateCategory(categoryRow) {\n                this.isCategoryEdit = true;\n                this.categoryDialogTitle = '修改维修类别';\n\n                this.categoryForm = {\n                    id: categoryRow.deptsId,\n                    name: categoryRow.deptname,\n                    roleId: categoryRow.roleId\n                };\n\n                this.categoryDialogVisible = true;\n            },\n\n            // 修改维修子类别\n            handleUpdateSubcategory(subcategoryRow) {\n                this.isEdit = true;\n                this.drawerTitle = '修改维修子类别';\n\n                // 从父级数据中获取信息\n                const parentCategory = this.findParentCategory(subcategoryRow.classesId);\n                if (parentCategory) {\n                    this.parentCategoryId = parentCategory.deptsId;\n                    this.parentCategoryName = parentCategory.deptname;\n                    this.parentRoleId = parentCategory.roleId;\n                    this.parentRoleName = parentCategory.roleName;\n                }\n\n                this.form = {\n                    id: subcategoryRow.classesId,\n                    name: subcategoryRow.classesname\n                };\n\n                this.drawerVisible = true;\n            },\n\n            // 删除类别\n            handleDeleteCategory(categoryRow) {\n                this.$confirm('确认删除该维修类别吗？删除后其下的所有维修子类别也将被删除。', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/dept/\" + categoryRow.deptsId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 删除子类别\n            handleDeleteSubcategory(subcategoryRow) {\n                this.$confirm('确认删除该维修子类别吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/class/\" + subcategoryRow.classesId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 提交维修类别表单\n            submitCategoryForm() {\n                this.$refs[\"categoryForm\"].validate(valid => {\n                    if (valid) {\n                        this.submitCategory();\n                    }\n                });\n            },\n\n            // 提交子类别表单\n            submitForm() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        this.submitSubcategory();\n                    }\n                });\n            },\n\n            // 提交维修类别\n            submitCategory() {\n                if (this.isCategoryEdit) {\n                    // 修改维修类别\n                    this.$put(\"/dept\", {\n                        id: this.categoryForm.id,\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"修改维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加维修类别\n                    this.$post(\"/dept\", {\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"添加维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 提交子类别\n            submitSubcategory() {\n                if (this.isEdit) {\n                    // 修改子类别\n                    this.$put(\"/class\", {\n                        id: this.form.id,\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"修改维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加子类别\n                    this.$post(\"/class\", {\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"添加维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 关闭维修类别弹窗\n            handleCategoryDialogClose() {\n                this.categoryDialogVisible = false;\n                this.resetCategoryForm();\n            },\n\n            // 关闭子类别抽屉\n            handleDrawerClose() {\n                this.drawerVisible = false;\n                this.resetForm();\n            },\n\n            // 获取数据列表\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                        // 提取角色选项，只显示维修员角色\n                        this.roleOptions = res.data.role\n                            .filter(role => role.name === '维修员')\n                            .map(role => ({\n                                id: role.id,\n                                name: role.name\n                            }));\n                    } else {\n                        this.roles = [];\n                        this.roleOptions = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.roleOptions = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 维修类别表单重置\n            resetCategoryForm() {\n                this.categoryForm = {\n                    id: null,\n                    name: '',\n                    roleId: null\n                };\n\n                if (this.$refs.categoryForm) {\n                    this.$refs.categoryForm.resetFields();\n                }\n            },\n\n            // 子类别表单重置\n            resetForm() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                };\n                this.parentRoleName = '';\n                this.parentCategoryName = '';\n                this.parentCategoryId = null;\n                this.parentRoleId = null;\n\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n\n            // 辅助方法：根据子类别ID查找父级维修类别\n            findParentCategory(classId) {\n                for (let dept of this.dataList) {\n                    if (dept.children) {\n                        for (let child of dept.children) {\n                            if (child.classesId === classId) {\n                                return dept;\n                            }\n                        }\n                    }\n                }\n                return null;\n            },\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                let allDepts = [];\n\n                // 遍历所有角色，只收集维修员角色的维修类别\n                this.roles.forEach(role => {\n                    if (!role || !role.depts || role.name !== '维修员') return;\n\n                    role.depts.forEach(dept => {\n                        if (!dept) return;\n\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        // 添加子类别\n                        deptClasses.forEach(classItem => {\n                            if (!classItem) return;\n\n                            children.push({\n                                classesId: classItem.id,\n                                classesname: classItem.name || '',\n                                uuid: this.guid2()\n                            });\n                        });\n\n                        // 添加维修类别\n                        allDepts.push({\n                            deptsId: dept.id,\n                            deptname: dept.name || '',\n                            roleId: role.id, // 保留角色ID用于操作\n                            roleName: role.name, // 保留角色名称用于表单显示\n                            children: children,\n                            uuid: this.guid2()\n                        });\n                    });\n                });\n\n                return allDepts;\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n\n    .top-actions {\n        text-align: left;\n        margin-bottom: 20px;\n    }\n\n    .drawer-footer {\n        text-align: right;\n        margin-top: 30px;\n        padding-top: 20px;\n        border-top: 1px solid #e8e8e8;\n    }\n\n    .drawer-footer .el-button {\n        margin-left: 10px;\n    }\n\n    // 操作按钮样式优化\n    .el-table {\n        /deep/ .el-button--text {\n            margin-right: 10px;\n            &:last-child {\n                margin-right: 0;\n            }\n        }\n        /deep/ .el-button [class*=\"el-icon-\"] + span {\n            margin-left: 3px;\n        }\n    }\n\n    // 角色不可操作提示样式\n    .el-table .role-no-operation {\n        color: #909399;\n        font-size: 12px;\n        font-style: italic;\n    }\n</style>\n"]}]}