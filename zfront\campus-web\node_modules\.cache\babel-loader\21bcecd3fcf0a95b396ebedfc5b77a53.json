{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithHoles.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\@babel\\runtime\\helpers\\esm\\arrayWithHoles.js", "mtime": 1737774013961}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX2FycmF5V2l0aEhvbGVzKGFycikgewogIGlmIChBcnJheS5pc0FycmF5KGFycikpIHJldHVybiBhcnI7Cn0="}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js"], "names": ["_arrayWithHoles", "arr", "Array", "isArray"], "mappings": "AAAA,eAAe,SAASA,eAAT,CAAyBC,GAAzB,EAA8B;AAC3C,MAAIC,KAAK,CAACC,OAAN,CAAcF,GAAd,CAAJ,EAAwB,OAAOA,GAAP;AACzB", "sourcesContent": ["export default function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}"]}]}