{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\src\\api\\remark\\remark.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\api\\remark\\remark.js", "mtime": 1737774014078}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlsL3JlcXVlc3QnOyAvLyDmn6Xor6JyZW1hcmvliJfooagKCmV4cG9ydCBmdW5jdGlvbiBsaXN0UmVtYXJrKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N0dWRlbnQvcmVtYXJrL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfSAvLyDmn6Xor6JyZW1hcmvor6bnu4YKCmV4cG9ydCBmdW5jdGlvbiBnZXRSZW1hcmsoaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvc3R1ZGVudC9yZW1hcmsvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9IC8vIOaWsOWinnJlbWFyawoKZXhwb3J0IGZ1bmN0aW9uIGFkZFJlbWFyayhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N0dWRlbnQvcmVtYXJrJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9IC8vIOS/ruaUuXJlbWFyawoKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZVJlbWFyayhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N0dWRlbnQvcmVtYXJrJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0gLy8g5Yig6ZmkcmVtYXJrCgpleHBvcnQgZnVuY3Rpb24gZGVsUmVtYXJrKGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3N0dWRlbnQvcmVtYXJrLycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/src/api/remark/remark.js"], "names": ["request", "listRemark", "query", "url", "method", "params", "getRemark", "id", "addRemark", "data", "updateRemark", "delRemark"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB,C,CAEA;;AACA,OAAO,SAASC,UAAT,CAAoBC,KAApB,EAA2B;AAChC,SAAOF,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,sBADQ;AAEbC,IAAAA,MAAM,EAAE,KAFK;AAGbC,IAAAA,MAAM,EAAEH;AAHK,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASI,SAAT,CAAmBC,EAAnB,EAAuB;AAC5B,SAAOP,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,qBAAqBI,EADb;AAEbH,IAAAA,MAAM,EAAE;AAFK,GAAD,CAAd;AAID,C,CAED;;AACA,OAAO,SAASI,SAAT,CAAmBC,IAAnB,EAAyB;AAC9B,SAAOT,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,iBADQ;AAEbC,IAAAA,MAAM,EAAE,MAFK;AAGbK,IAAAA,IAAI,EAAEA;AAHO,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASC,YAAT,CAAsBD,IAAtB,EAA4B;AACjC,SAAOT,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,iBADQ;AAEbC,IAAAA,MAAM,EAAE,KAFK;AAGbK,IAAAA,IAAI,EAAEA;AAHO,GAAD,CAAd;AAKD,C,CAED;;AACA,OAAO,SAASE,SAAT,CAAmBJ,EAAnB,EAAuB;AAC5B,SAAOP,OAAO,CAAC;AACbG,IAAAA,GAAG,EAAE,qBAAqBI,EADb;AAEbH,IAAAA,MAAM,EAAE;AAFK,GAAD,CAAd;AAID", "sourcesContent": ["import request from '@/util/request'\n\n// 查询remark列表\nexport function listRemark(query) {\n  return request({\n    url: '/student/remark/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询remark详细\nexport function getRemark(id) {\n  return request({\n    url: '/student/remark/' + id,\n    method: 'get'\n  })\n}\n\n// 新增remark\nexport function addRemark(data) {\n  return request({\n    url: '/student/remark',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改remark\nexport function updateRemark(data) {\n  return request({\n    url: '/student/remark',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除remark\nexport function delRemark(id) {\n  return request({\n    url: '/student/remark/' + id,\n    method: 'delete'\n  })\n}\n"]}]}