{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue?vue&type=style&index=0&id=7b7ab946&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\PostList.vue", "mtime": 1748720501299}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZvcnVtLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAyMHB4Owp9CgouZm9ydW0taGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmZvcnVtLWFjdGlvbnMgewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMTBweDsKfQoKLnNlYXJjaC1pbnB1dCB7CiAgICB3aWR0aDogMzAwcHg7Cn0KCi5wb3N0LWNhcmQgewogICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIHRyYW5zaXRpb246IGFsbCAwLjNzOwp9CgoucG9zdC1jYXJkOmhvdmVyIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTsKICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgoucG9zdC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgoucG9zdC10aXRsZSB7CiAgICBmb250LXNpemU6IDE4cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKICAgIGNvbG9yOiAjMzAzMTMzOwp9CgoucG9zdC1jb250ZW50IHsKICAgIGNvbG9yOiAjNjA2MjY2OwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgIGxpbmUtaGVpZ2h0OiAxLjU7CiAgICBjdXJzb3I6IHBvaW50ZXI7Cn0KCi52aWV3LW1vcmUgewogICAgY29sb3I6ICM0MDlFRkY7CiAgICBtYXJnaW4tdG9wOiA4cHg7CiAgICBmb250LXNpemU6IDE0cHg7Cn0KCi5wb3N0LWZvb3RlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgY29sb3I6ICM5MDkzOTk7CiAgICBmb250LXNpemU6IDE0cHg7Cn0KCi5wb3N0LWluZm8gewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMTVweDsKfQoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIHBhZGRpbmctcmlnaHQ6IDIwcHg7Cn0KCi5sb2FkaW5nLWNhcmQgewogICAgcGFkZGluZzogMjBweDsKfQoKLmxpa2VkIHsKICAgIGNvbG9yOiAjNDA5RUZGOwp9Cg=="}, {"version": 3, "sources": ["PostList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2KA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "PostList.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"forum-container\">\n        <div class=\"forum-header\">\n            <h2>维修员论坛</h2>\n            <div class=\"forum-actions\">\n                <el-input\n                    placeholder=\"搜索帖子\"\n                    v-model=\"keyword\"\n                    class=\"search-input\"\n                    @keyup.enter.native=\"searchPosts\"\n                >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchPosts\"></el-button>\n                </el-input>\n                <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"post in posts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\" @click.native.stop=\"viewPostDetail(post.id)\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"warning\" v-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\" @click=\"viewPostDetail(post.id)\">\n                    {{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}\n                    <!-- <div class=\"view-more\">点击查看帖子全文</div> -->\n                </div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\" :class=\"{ 'liked': post.isLiked }\" @click.stop=\"toggleLike(post)\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <el-button size=\"small\" type=\"text\" @click.stop=\"viewPostDetail(post.id)\">查看详情 <i class=\"el-icon-arrow-right\"></i></el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"posts.length === 0\" description=\"暂无帖子\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'PostList',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0,\n            keyword: ''\n        };\n    },\n    created() {\n        // 检查用户角色\n        const currentUser = JSON.parse(sessionStorage.getItem('user'));\n        // 只有非用户角色可以访问论坛\n        if (!currentUser || !currentUser.role || currentUser.role.id === 14) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/home');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n\n            const params = {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: JSON.parse(sessionStorage.getItem('user')).id\n            };\n\n            if (this.keyword) {\n                params.keyword = this.keyword;\n            }\n\n            this.$get('/forum/post/list', params)\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange(page) {\n            this.currentPage = page;\n            this.fetchPosts();\n        },\n        searchPosts() {\n            this.currentPage = 1;\n            this.fetchPosts();\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            console.log('点击帖子，准备跳转到详情页，ID:', id);\n            try {\n                this.$router.push(`/home/<USER>/post/${id}`);\n            } catch (error) {\n                console.error('跳转失败:', error);\n                this.$message.error('跳转到帖子详情页失败');\n            }\n        },\n        toggleLike(post) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            this.$post(`/forum/post/like/${post.id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        post.isLiked = !post.isLiked;\n                        post.likeCount += post.isLiked ? 1 : -1;\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('点赞操作失败:', err);\n                    this.$message.error('点赞操作失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.forum-container {\n    padding: 20px;\n}\n\n.forum-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.forum-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.search-input {\n    width: 300px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n    cursor: pointer;\n    transition: all 0.3s;\n}\n\n.post-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n    cursor: pointer;\n}\n\n.view-more {\n    color: #409EFF;\n    margin-top: 8px;\n    font-size: 14px;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.liked {\n    color: #409EFF;\n}\n</style>\n"]}]}