{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue?vue&type=style&index=0&id=c0f81a7c&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue", "mtime": 1745332269335}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWRpdC1wb3N0LWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAyMHB4Owp9CgoucGFnZS1oZWFkZXIgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnBvc3QtZm9ybSB7CiAgICBtYXgtd2lkdGg6IDgwMHB4Owp9CgoubG9hZGluZy1jYXJkIHsKICAgIHBhZGRpbmc6IDIwcHg7Cn0K"}, {"version": 3, "sources": ["AdminForumPostEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "AdminForumPostEdit.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"edit-post-container\">\n        <div class=\"page-header\">\n            <h2>编辑帖子</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <el-form v-else :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">保存</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostEdit',\n    data() {\n        return {\n            postForm: {\n                id: null,\n                title: '',\n                content: '',\n                authorId: null\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            loading: true,\n            submitting: false,\n            adminId: null\n        };\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        const post = res.data.post;\n                        this.postForm = {\n                            id: post.id,\n                            title: post.title,\n                            content: post.content,\n                            authorId: post.authorId\n                        };\n                        \n                        // 检查是否是自己的帖子\n                        if (post.authorId !== this.adminId) {\n                            this.$message.warning('您只能编辑自己发布的帖子');\n                            this.$router.push(`/admin/home/<USER>/post/${postId}`);\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                        this.$router.push('/admin/home/<USER>/posts');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                    this.$router.push('/admin/home/<USER>/posts');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    // 确保使用正确的作者ID\n                    this.postForm.authorId = this.adminId;\n                    \n                    this.$put('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('更新成功');\n                                this.$router.push(`/admin/home/<USER>/post/${this.postForm.id}`);\n                            } else {\n                                this.$message.error(res.data.msg || '更新失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('更新帖子失败:', err);\n                            this.$message.error('更新帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.edit-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}