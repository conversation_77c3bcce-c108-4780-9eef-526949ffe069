package com.yqn.controller;

import com.yqn.pojo.ChatMessage;
import com.yqn.service.ChatService;
import com.yqn.common.tools.MessageTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器
 * 处理WebSocket消息和HTTP请求
 * 
 * <AUTHOR>
 */
@Controller
@Slf4j
public class ChatController {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ChatService chatService;
    
    @Autowired
    private MessageTools messageTools;

    /**
     * 处理发送的聊天消息
     * @param chatMessage 聊天消息
     * @param headerAccessor 消息头访问器
     */
    @MessageMapping("/chat.sendMessage")
    public void sendMessage(@Payload ChatMessage chatMessage, SimpMessageHeaderAccessor headerAccessor) {
        try {
            // 设置发送时间
            chatMessage.setSendTime(new Date());
            chatMessage.setIsRead(0); // 设置为未读
            
            // 保存消息到数据库
            boolean saved = chatService.saveMessage(chatMessage);
            
            if (saved) {
                log.info("聊天消息已保存: 发送者={}, 接收者={}, 任务ID={}", 
                        chatMessage.getSenderId(), chatMessage.getReceiverId(), chatMessage.getTaskId());
                
                // 发送消息给接收者
                messagingTemplate.convertAndSendToUser(
                    chatMessage.getReceiverId().toString(),
                    "/queue/messages",
                    chatMessage
                );
                
                // 同时发送给发送者（确认消息已发送）
                messagingTemplate.convertAndSendToUser(
                    chatMessage.getSenderId().toString(),
                    "/queue/messages",
                    chatMessage
                );
            } else {
                log.error("保存聊天消息失败");
            }
        } catch (Exception e) {
            log.error("处理聊天消息时发生错误", e);
        }
    }

    /**
     * 用户加入聊天
     * @param chatMessage 聊天消息
     * @param headerAccessor 消息头访问器
     */
    @MessageMapping("/chat.addUser")
    public void addUser(@Payload ChatMessage chatMessage, SimpMessageHeaderAccessor headerAccessor) {
        // 在WebSocket会话中添加用户信息
        headerAccessor.getSessionAttributes().put("userId", chatMessage.getSenderId());
        headerAccessor.getSessionAttributes().put("taskId", chatMessage.getTaskId());
        
        log.info("用户 {} 加入任务 {} 的聊天", chatMessage.getSenderId(), chatMessage.getTaskId());
    }

    /**
     * 获取聊天历史记录
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 聊天历史记录
     */
    @GetMapping("/api/chat/history")
    @ResponseBody
    public Map<String, Object> getChatHistory(@RequestParam Long taskId, @RequestParam Long userId) {
        try {
            List<ChatMessage> messages = chatService.getChatHistory(taskId, userId);
            return messageTools.message(true, "获取聊天记录成功", "messages", messages);
        } catch (Exception e) {
            log.error("获取聊天记录失败", e);
            return messageTools.message(false, "获取聊天记录失败", "messages", null);
        }
    }

    /**
     * 标记消息为已读
     * @param messageIds 消息ID列表
     * @return 操作结果
     */
    @PutMapping("/api/chat/markRead")
    @ResponseBody
    public Map<String, Object> markMessagesAsRead(@RequestBody List<Long> messageIds) {
        try {
            boolean success = chatService.markMessagesAsRead(messageIds);
            if (success) {
                return messageTools.message(true, "标记已读成功", "", null);
            } else {
                return messageTools.message(false, "标记已读失败", "", null);
            }
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
            return messageTools.message(false, "标记已读失败", "", null);
        }
    }

    /**
     * 获取未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    @GetMapping("/api/chat/unreadCount")
    @ResponseBody
    public Map<String, Object> getUnreadCount(@RequestParam Long userId) {
        try {
            int count = chatService.getUnreadCount(userId);
            return messageTools.message(true, "获取未读消息数量成功", "count", count);
        } catch (Exception e) {
            log.error("获取未读消息数量失败", e);
            return messageTools.message(false, "获取未读消息数量失败", "count", 0);
        }
    }
}
