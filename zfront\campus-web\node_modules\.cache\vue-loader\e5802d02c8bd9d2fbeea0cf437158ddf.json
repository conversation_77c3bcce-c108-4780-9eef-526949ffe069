{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue?vue&type=style&index=0&id=5be4d989&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue", "mtime": 1745332328325}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wb3N0LWRldGFpbC1jb250YWluZXIgewogICAgcGFkZGluZzogMjBweDsKfQoKLnBvc3QtYWN0aW9ucyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnBvc3QtY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAzMHB4Owp9CgoucG9zdC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgoucG9zdC10aXRsZSB7CiAgICBtYXJnaW46IDA7CiAgICBmb250LXNpemU6IDI0cHg7Cn0KCi5wb3N0LW1ldGEgewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMjBweDsKICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIGZvbnQtc2l6ZTogMTRweDsKfQoKLnBvc3QtY29udGVudCB7CiAgICBsaW5lLWhlaWdodDogMS42OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIHdoaXRlLXNwYWNlOiBwcmUtbGluZTsKfQoKLmNvbW1lbnRzLXNlY3Rpb24gewogICAgbWFyZ2luLXRvcDogMzBweDsKfQoKLmNvbW1lbnQtY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouY29tbWVudC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5jb21tZW50LWF1dGhvciB7CiAgICBmb250LXdlaWdodDogYm9sZDsKfQoKLmNvbW1lbnQtdGltZSB7CiAgICBjb2xvcjogIzkwOTM5OTsKICAgIGZvbnQtc2l6ZTogMTRweDsKfQoKLmNvbW1lbnQtY29udGVudCB7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgbGluZS1oZWlnaHQ6IDEuNTsKICAgIHdoaXRlLXNwYWNlOiBwcmUtbGluZTsKfQoKLmNvbW1lbnQtYWN0aW9ucyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZ2FwOiAxMHB4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKfQoKLmxvYWRpbmctY2FyZCB7CiAgICBwYWRkaW5nOiAyMHB4Owp9Cg=="}, {"version": 3, "sources": ["AdminForumPostDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgNA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "AdminForumPostDetail.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"post-detail-container\">\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"15\" animated />\n        </el-card>\n\n        <div v-else>\n            <div class=\"post-actions\">\n                <el-button icon=\"el-icon-back\" @click=\"goBack\">返回</el-button>\n                <div>\n                    <el-button type=\"primary\" icon=\"el-icon-top\" @click=\"toggleTop(!post.isTop)\" v-if=\"post.status === 1\">\n                        {{ post.isTop ? '取消置顶' : '置顶' }}\n                    </el-button>\n                    <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost\" v-if=\"isAuthor\">编辑</el-button>\n                    <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost\">删除</el-button>\n                </div>\n            </div>\n\n            <el-card class=\"post-card\">\n                <div class=\"post-header\">\n                    <h2 class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </h2>\n                    <div class=\"post-status\" v-if=\"post.status === 0\">\n                        <el-tag type=\"warning\">待审核</el-tag>\n                    </div>\n                    <div class=\"post-status\" v-else-if=\"post.status === 2\">\n                        <el-tag type=\"danger\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-meta\">\n                    <span>\n                        作者: {{ post.author ? post.author.username : '未知' }}\n                        <el-tag size=\"mini\" type=\"success\" v-if=\"post.author && post.author.username === '管理员'\">管理员</el-tag>\n                    </span>\n                    <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                    <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                </div>\n                <div class=\"post-content\">{{ post.content }}</div>\n            </el-card>\n\n            <div class=\"comments-section\">\n                <h3>评论 ({{ post.commentCount || 0 }})</h3>\n\n                <div class=\"comments-list\">\n                    <el-card v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-card\">\n                        <div class=\"comment-header\">\n                            <span class=\"comment-author\">\n                                {{ comment.author ? comment.author.username : '未知' }}\n                                <el-tag size=\"mini\" type=\"success\" v-if=\"comment.author && comment.author.username === '管理员'\">管理员</el-tag>\n                            </span>\n                            <span class=\"comment-time\">{{ formatDate(comment.createTime) }}</span>\n                        </div>\n                        <div class=\"comment-content\">{{ comment.content }}</div>\n                        <div class=\"comment-actions\">\n                            <el-button\n                                size=\"mini\"\n                                type=\"danger\"\n                                icon=\"el-icon-delete\"\n                                @click=\"deleteComment(comment)\"\n                            >\n                                删除\n                            </el-button>\n                        </div>\n                    </el-card>\n\n                    <el-empty v-if=\"comments.length === 0\" description=\"暂无评论\"></el-empty>\n                </div>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostDetail',\n    data() {\n        return {\n            post: {},\n            comments: [],\n            loading: true,\n            adminId: null\n        };\n    },\n    computed: {\n        isAuthor() {\n            return this.post.authorId === this.adminId;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.post = res.data.post;\n                        this.comments = this.post.comments || [];\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        goBack() {\n            this.$router.go(-1);\n        },\n        editPost() {\n            this.$router.push(`/admin/home/<USER>/edit/${this.post.id}`);\n        },\n        toggleTop(isTop) {\n            this.$put(`/forum/post/top/${this.post.id}?isTop=${isTop}`)\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');\n                        this.post.isTop = isTop; // 更新当前帖子的置顶状态\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('置顶操作失败:', err);\n                    this.$message.error('置顶操作失败');\n                });\n        },\n        deletePost() {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                this.$del(`/forum/post/${this.post.id}?operatorId=${this.adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.$router.push('/admin/home/<USER>/posts');\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        deleteComment(comment) {\n            console.log('准备删除评论:', comment.id, '管理员ID:', this.adminId);\n\n            this.$confirm('确定要删除这条评论吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                console.log('发送删除评论请求:', `/forum/comment/${comment.id}`, { operatorId: this.adminId });\n\n                this.$del(`/forum/comment/${comment.id}`, { operatorId: this.adminId })\n                    .then(res => {\n                        console.log('删除评论响应:', res.data);\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPostDetail(); // 重新加载评论\n                        } else {\n                            console.error('删除评论失败，服务器响应:', res.data.msg);\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除评论失败:', err);\n                        this.$message.error('删除评论失败: ' + err.message);\n                    });\n            }).catch(() => {\n                // 取消删除\n                console.log('取消删除评论');\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.post-detail-container {\n    padding: 20px;\n}\n\n.post-actions {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 30px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n}\n\n.post-title {\n    margin: 0;\n    font-size: 24px;\n}\n\n.post-meta {\n    display: flex;\n    gap: 20px;\n    color: #909399;\n    margin-bottom: 20px;\n    font-size: 14px;\n}\n\n.post-content {\n    line-height: 1.6;\n    margin-bottom: 20px;\n    white-space: pre-line;\n}\n\n.comments-section {\n    margin-top: 30px;\n}\n\n.comment-card {\n    margin-bottom: 15px;\n}\n\n.comment-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 10px;\n}\n\n.comment-author {\n    font-weight: bold;\n}\n\n.comment-time {\n    color: #909399;\n    font-size: 14px;\n}\n\n.comment-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n    white-space: pre-line;\n}\n\n.comment-actions {\n    display: flex;\n    gap: 10px;\n    justify-content: flex-end;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}