{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accept.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accept.vue", "mtime": 1746181287829}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Accept.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA,SAAA,QAAA,QAAA,MAAA;AACA,SAAA,UAAA,IAAA,WAAA,QAAA,aAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,eAAA,EAAA,IAFA;AAGA;AACA,MAAA,YAAA,EAAA;AAJA,KAAA;AAMA,GATA;AAWA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,sBACA,EADA,EACA;AAAA;;AACA,WAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,WAFA;AAGA,QAAA,gBAAA,EAAA,IAHA;AAIA,QAAA,iBAAA,EAAA,IAJA;AAKA,QAAA,gBAAA,EAAA,IALA;AAMA,QAAA,WAAA,EAAA,qBAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA;AACA,cAAA,MAAA,IAAA,SAAA,EAAA;AACA,YAAA,QAAA,CAAA,iBAAA,GAAA,QAAA;AACA,gBAAA,IAAA,GAAA;AACA,cAAA,EAAA,EAAA,EADA;AAEA,cAAA,QAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AAFA,aAAA;;AAKA,YAAA,KAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,IAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,gBAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,gBAAA;AACA,eAHA,MAGA;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,OAAA;AACA;;AACA,cAAA,IAAA;AACA,cAAA,QAAA,CAAA,iBAAA,GAAA,IAAA;AACA,aAVA,EAWA,KAXA,CAWA,YAAA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,cAAA,EAAA,OAAA;;AACA,cAAA,IAAA;AACA,cAAA,QAAA,CAAA,iBAAA,GAAA,IAAA;AACA,aAfA;AAgBA,WAvBA,MAuBA;AACA,YAAA,IAAA;AACA;AACA;AAjCA,OAAA,EAkCA,KAlCA,CAkCA,YAAA,CAAA,CAlCA;AAmCA,KArCA;AAsCA,IAAA,SAtCA,uBAsCA;AAAA;;AACA,WAAA,IAAA,CAAA,OAAA,EAAA;AAAA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,OAAA;AACA;AACA,OAPA;AAQA;AA/CA,GAXA;AA6DA,EAAA,QAAA,kCACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA;AAGA;AACA,IAAA,cAJA,4BAIA;AACA,aAAA,KAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA,KAAA,CAAA;AAAA,OAAA,CAAA;AACA,KANA;AAQA;AACA,IAAA,aATA,2BASA;AAAA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA,eAAA,KAAA,cAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,cAAA,SAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,MAAA,KAAA,MAAA,CAAA,IAAA,CAAA,MAAA;AACA,cAAA,SAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,MAAA,KAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA,iBAAA,SAAA,IAAA,SAAA;AACA,SALA,CAAA;AAMA;;AACA,aAAA,KAAA,cAAA;AACA,KAnBA;AAqBA;AACA,IAAA,aAtBA,2BAsBA;AACA;AACA,UAAA,SAAA,GAAA;AACA,cAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,CAEA;;AAFA,OAAA,CAFA,CAOA;;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,CAAA,KAAA,GAAA,KAAA,cAAA,CARA,CAUA;AACA;AACA;AACA;AAEA;;AACA,aAAA,MAAA,CAAA,OAAA,CAAA,SAAA,EAAA,GAAA,CAAA;AAAA;AAAA,YAAA,MAAA;AAAA,YAAA,IAAA;;AAAA,eAAA;AACA,UAAA,MAAA,EAAA,QAAA,CAAA,MAAA,CADA;AAEA,UAAA,UAAA,EAAA,IAAA,CAAA,IAFA;AAGA,UAAA,KAAA,EAAA,IAAA,CAAA;AAHA,SAAA;AAAA,OAAA,CAAA;AAKA;AA3CA,IA7DA;AA0GA,EAAA,OA1GA,qBA0GA;AACA,SAAA,SAAA;AACA,GA5GA;AA6GA,EAAA,OA7GA,qBA6GA;AAAA;;AACA;AACA,SAAA,eAAA,GAAA,WAAA,CAAA,YAAA;AACA,MAAA,MAAA,CAAA,SAAA;AACA,KAFA,EAEA,KAFA,CAAA,CAFA,CAIA;AACA,GAlHA;AAmHA,EAAA,aAnHA,2BAmHA;AACA;AACA,QAAA,KAAA,eAAA,EAAA;AACA,MAAA,aAAA,CAAA,KAAA,eAAA,CAAA;AACA;AACA,GAxHA;AA0HA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,sBACA,IADA,EACA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAAA,WAAA,CAAA,IAAA,EAAA,kBAAA,CAAA;AACA;AAJA;AA1HA,CAAA", "sourcesContent": ["<template>\n   <div class=\"content\">\n        <el-alert\n                :title=\"user.role.name + '- - -任务平台共' + tasks.length + '个任务'\"\n                :closable=\"false\"\n                type=\"info\">\n        </el-alert>\n\n        <div>\n            <el-card class=\"box-card\">\n                <div slot=\"header\" class=\"clearfix\">\n                    <span>可接受任务</span>\n                </div>\n\n                <!-- 状态分类标签 -->\n                <div class=\"status-tabs\">\n                    <el-tabs v-model=\"activeStatus\" type=\"card\">\n                        <el-tab-pane\n                            v-for=\"group in tasksByStatus\"\n                            :key=\"group.status\"\n                            :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                            :name=\"group.status.toString()\"\n                        >\n                            <el-card\n                                class=\"box-card\"\n                                v-for=\"item in group.tasks\"\n                                :key=\"item.id\"\n                                style=\"margin-top: 20px\"\n                            >\n                    <div slot=\"header\" class=\"clearfix\"\n                         style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span>\n                            <el-tag type=\"success\" style=\"margin-right: 5px\">待解决</el-tag>\n                            {{item.taskTitle}}\n                            <i class=\"el-icon-money\" style=\"margin-left: 20px;color: red;\"> {{item.reward}}元</i>\n                        </span>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\"\n                                   v-show=\"user.id != item.publish.id\" @click=\"acceptTask(item.id)\">接受任务\n                        </el-button>\n                        <el-button style=\"float: right; padding: 3px 0\" type=\"text\"\n                                   v-show=\"user.id == item.publish.id\">本人任务\n                        </el-button>\n                    </div>\n                    <div class=\"text item\">\n                        <!-- 发布人和任务内容 -->\n                        <p class=\"el-icon-s-custom\">{{item.publish.username}}<span style=\"margin-left: 10px;\">{{item.taskContext}}</span>\n                        </p>\n\n                        <!-- 任务详细信息表格 -->\n                        <div class=\"task-details\">\n                            <table class=\"info-table\">\n                                <tr>\n                                    <th>类别</th>\n                                    <th>子类别</th>\n                                    <th>地址</th>\n                                    <th>详细地址</th>\n                                </tr>\n                                <tr>\n                                    <td>{{item.dept ? item.dept.name : '未知'}}</td>\n                                    <td>{{item.type ? item.type.name : '未知'}}</td>\n                                    <td>{{item.province ? item.province + ' ' + item.city + ' ' + item.district : '未知'}}</td>\n                                    <td>{{item.address || '未知'}}</td>\n                                </tr>\n                            </table>\n                        </div>\n\n                        <span style=\"float: right\">{{item.createTime | formatDate}}</span>\n                    </div>\n                            </el-card>\n\n                            <!-- 当前状态下没有任务时显示 -->\n                            <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                                <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无可接受任务</span>\n                            </div>\n                        </el-tab-pane>\n                    </el-tabs>\n                </div>\n\n                <!-- 没有任何任务时显示 -->\n                <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                    <span><i class=\"el-icon-refresh-right\"></i>暂无可接受任务</span>\n                </div>\n            </el-card>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\";\n    import {formatDate} from '@/util/date';\n\n    export default {\n        name: \"Index\",\n        data() {\n            return {\n                tasks: [],\n                refreshInterval: null,\n                // 当前选中的状态\n                activeStatus: '-1'\n            }\n        },\n\n        methods: {\n            acceptTask(id) {\n                this.$msgbox({\n                    title: '提示',\n                    message: '确定接受此任务吗？',\n                    showCancelButton: true,\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    beforeClose: ((action, instance, done) => {\n                        if (action == 'confirm') {\n                            instance.confirmButtonText = '执行中...';\n                            const data = {\n                                id: id,\n                                acceptId: this.user.id\n                            };\n\n                            this.$put('task/takerAccept', data)\n                                .then((res) => {\n                                    if (res.data.status) {\n                                        this.$msg(res.data.msg, \"success\");\n                                        this.$router.push('/home/<USER>');\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\");\n                                    }\n                                    done();\n                                    instance.confirmButtonText = '确定';\n                                })\n                                .catch(() => {\n                                    this.$msg(\"接受任务失败，请稍后重试\", \"error\");\n                                    done();\n                                    instance.confirmButtonText = '确定';\n                                });\n                        } else {\n                            done();\n                        }\n                    })\n                }).catch(() => {});\n            },\n            loadTasks() {\n                this.$get(\"/task\", { id: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.tasks = res.data.task\n                        } else {\n                            this.$msg(res.data.msg, \"error\")\n                        }\n                    })\n            }\n        },\n\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 过滤出可接受的任务（状态为0的待接单任务）\n            availableTasks() {\n                return this.tasks.filter(task => task.state === 0);\n            },\n\n            // 过滤出当前维修员可接受的任务（匹配类别和子类别）\n            matchingTasks() {\n                if (this.user.role && this.user.role.id === 13) {\n                    return this.availableTasks.filter(task => {\n                        // 检查类别和子类别是否匹配\n                        const deptMatch = !task.deptId || task.deptId === this.user.deptId;\n                        const typeMatch = !task.typeId || task.typeId === this.user.classId;\n                        return deptMatch && typeMatch;\n                    });\n                }\n                return this.availableTasks;\n            },\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    // '0': { name: '待接单', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.availableTasks;\n\n                // // 按状态分组 - 对于Accept页面，我们只关心状态为0的待接单任务\n                // this.availableTasks.forEach(task => {\n                //     statusMap['0'].tasks.push(task);\n                // });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.loadTasks()\n        },\n        mounted() {\n            // 定期刷新任务列表\n            this.refreshInterval = setInterval(() => {\n                this.loadTasks()\n            }, 30000) // 每30秒刷新一次\n        },\n        beforeDestroy() {\n            // 清除定时器\n            if (this.refreshInterval) {\n                clearInterval(this.refreshInterval);\n            }\n        },\n\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n\n        .card h2 {\n            font-weight: normal;\n            font-size: 18px;\n\n            span {\n                font-size: 12px;\n                display: inline-block;\n                border: 1px solid red;\n                padding: 1px 3px;\n            }\n        }\n    }\n\n\n    /deep/ .el-alert--info.is-light {\n        height: 50px;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 130px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n    /deep/ .el-card {\n        margin-bottom: 20px;\n    }\n\n    .task-details {\n        margin: 15px 0;\n\n        .info-table {\n            width: 100%;\n            border-collapse: collapse;\n            border: 1px solid #EBEEF5;\n\n            th, td {\n                padding: 10px;\n                text-align: center;\n                border: 1px solid #EBEEF5;\n            }\n\n            th {\n                background-color: #F5F7FA;\n                color: #606266;\n                font-weight: bold;\n            }\n\n            td {\n                color: #606266;\n            }\n        }\n    }\n\n    .status-tabs {\n        margin-bottom: 20px;\n\n        /deep/ .el-tabs__header {\n            margin-bottom: 15px;\n        }\n\n        /deep/ .el-tabs__item {\n            height: 40px;\n            line-height: 40px;\n            font-size: 14px;\n            color: #606266;\n\n            &.is-active {\n                color: #409EFF;\n                font-weight: bold;\n            }\n        }\n    }\n</style>"], "sourceRoot": "src/views/user/children"}]}