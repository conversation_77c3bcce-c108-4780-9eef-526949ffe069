package com.yqn.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 聊天消息实体类
 * 
 * <AUTHOR>
 */
@Data
@ToString
@TableName("chat_message")
public class ChatMessage {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;
    
    /**
     * 发送者ID
     */
    @TableField("sender_id")
    private Long senderId;
    
    /**
     * 接收者ID
     */
    @TableField("receiver_id")
    private Long receiverId;
    
    /**
     * 消息内容
     */
    @TableField("content")
    private String content;
    
    /**
     * 消息类型：text-文本，image-图片，file-文件
     */
    @TableField(value = "message_type", exist = true)
    private String messageType;
    
    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;
    
    /**
     * 是否已读：0-未读，1-已读
     */
    @TableField("is_read")
    private Integer isRead;
    
    // 非数据库字段，用于前端显示
    @TableField(exist = false)
    private User sender;
    
    @TableField(exist = false)
    private User receiver;
    
    @TableField(exist = false)
    private Task task;
}
