{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\advise.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\advise.vue", "mtime": 1737774014078}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["advise.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA,SAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,YAAA,QAAA,qBAAA;AACA,SAAA,UAAA,QAAA,aAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,OAAA,EAAA,IAFA;AAGA;AACA,MAAA,GAAA,EAAA,EAJA;AAKA;AACA,MAAA,MAAA,EAAA,IANA;AAOA;AACA,MAAA,QAAA,EAAA,IARA;AASA;AACA,MAAA,UAAA,EAAA,IAVA;AAWA;AACA,MAAA,KAAA,EAAA,CAZA;AAaA;AACA,MAAA,UAAA,EAAA,EAdA;AAeA;AACA,MAAA,KAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,IAAA,EAAA,KAlBA;AAmBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,WAAA,EAAA,IAHA;AAIA,QAAA,aAAA,EAAA;AAJA,OApBA;AA0BA;AACA,MAAA,IAAA,EAAA,EA3BA;AA4BA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA;AA7BA,KAAA;AAmCA,GAtCA;AAuCA,EAAA,OAvCA,qBAuCA;AACA,SAAA,OAAA;AACA,GAzCA;AA0CA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,KAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,UAAA,GAAA,QAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAJA;AAKA,KATA;;AAUA;AACA,IAAA,SAXA,uBAWA;AACA,WAAA,KAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,KAfA;;AAiBA;AACA,IAAA,YAlBA,wBAkBA,GAlBA,EAkBA;AAAA;;AACA,WAAA,KAAA;AACA,UAAA,QAAA,GAAA,GAAA,CAAA,QAAA,IAAA,KAAA,GAAA;AACA,MAAA,SAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,IAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,UAAA;AACA,OAJA;AAKA,KA1BA;;AA4BA;AACA,IAAA,UA7BA,wBA6BA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,IAAA,IAAA,EAAA;AACA,YAAA,YAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA,aAJA;AAKA,WANA,MAMA;AACA,YAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA,aAJA;AAKA;AACA;AACA,OAhBA;AAiBA,KA/CA;;AAgDA;AACA,IAAA,YAjDA,wBAiDA,GAjDA,EAiDA;AAAA;;AACA,UAAA,SAAA,GAAA,GAAA,CAAA,QAAA,IAAA,KAAA,GAAA;AACA,WAAA,QAAA,CAAA,WAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,SAAA,CAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA;;AAIA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAKA,OAdA,EAcA,KAdA,CAcA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAnBA;AAoBA,KAvEA;AAwEA;AACA,IAAA,MAzEA,oBAyEA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KA5EA;AA6EA;AACA,IAAA,KA9EA,mBA8EA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,WAAA,EAAA,IAFA;AAGA,QAAA,aAAA,EAAA,IAHA;AAIA,QAAA,QAAA,EAAA,IAJA;AAKA,QAAA,UAAA,EAAA,IALA;AAMA,QAAA,MAAA,EAAA;AANA,OAAA,CADA,CASA;AACA,KAxFA;;AAyFA;AACA,IAAA,WA1FA,yBA0FA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KA7FA;AA8FA;AACA,IAAA,qBA/FA,iCA+FA,SA/FA,EA+FA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,QAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KAnGA;AAoGA,IAAA,SApGA,qBAoGA,IApGA,EAoGA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAAA,UAAA,CAAA,IAAA,EAAA,kBAAA,CAAA;AACA;AAvGA;AA1CA,CAAA", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n        <el-input\n          v-model=\"queryParams.noticeTitle\"\n          placeholder=\"请输入公告标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n        <el-input\n          v-model=\"queryParams.noticeContent\"\n          placeholder=\"请输入公告内容\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n        >发布公告</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n        >删除</el-button>\n      </el-col>\n      <!-- <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar> -->\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"adviseList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n      <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n      <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ transform(scope.row.createTime) }}</span>\n        </template> \n      </el-table-column>\n\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    /> -->\n\n    <!-- 添加或修改通知公告公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"743px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n          <el-input v-model=\"form.noticeTitle\" placeholder=\"请输入公告标题\" />\n        </el-form-item>\n        <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n          <el-input\n                        resize=\"none\"\n                        type=\"textarea\"\n                        :autosize=\"{ minRows: 6, maxRows: 10}\"\n                        placeholder=\"请输入公告内容\"\n                        v-model=\"form.noticeContent\" style=\"padding: 0\">\n          </el-input>\n          <!-- <quill-editor v-model=\"form.noticeContent\" placeholder=\"请输入公告内容\" /> -->\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n        <el-form-item label=\"创建者\" prop=\"remark\">\n          <el-input v-model=\"form.createBy\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listAdvise, getAdvise, delAdvise, addAdvise, updateAdvise } from \"@/api/notice/advise\";\n  import {formatDate} from '@/util/date';\n\n  export default {\n    name: \"Advise\",\n    data() {\n      return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 通知公告公告表格数据\n        adviseList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          noticeTitle: null,\n          noticeContent: null,\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n          noticeTitle: [\n            { required: true, message: \"公告标题不能为空\", trigger: \"blur\" }\n          ],\n        }\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询通知公告公告列表 */\n      getList() {\n        this.loading = true;\n        listAdvise(this.queryParams).then(response => {\n          this.adviseList = response.data.rows;\n          this.total = response.total;\n          this.loading = false;\n        });\n      },\n      /** 新增按钮操作 */\n      handleAdd() {\n        this.reset();\n        this.open = true;\n        this.title = \"添加通知公告\";\n      },\n\n      /** 修改按钮操作 */\n      handleUpdate(row) {\n        this.reset();\n        const noticeId = row.noticeId || this.ids\n        getAdvise(noticeId).then(response => {\n          this.form = response.data.data;\n          this.open = true;\n          this.title = \"修改通知公告公告\";\n        });\n      },\n\n      /** 提交按钮 */\n      submitForm() {\n        this.$refs[\"form\"].validate(valid => {\n          if (valid) {\n            if (this.form.noticeId != null) {\n              updateAdvise(this.form).then(response => {\n                this.$message(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n            } else {\n              addAdvise(this.form).then(response => {\n                this.$message(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n            }\n          }\n        });\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const noticeIds = row.noticeId || this.ids;\n        this.$confirm('是否确认删除此学生', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning',\n            }).then(() => {\n            delAdvise(noticeIds).then(response => {\n                this.open = false;\n                this.getList();\n            });\n            this.$message({\n                type: 'success',\n                message: '删除成功!'\n                });\n\n            }).catch(() => {\n            this.$message({\n                type: 'info',\n                message: '已取消删除'\n            });          \n            });\n      },\n      // 取消按钮\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      // 表单重置\n      reset() {\n        this.form = {\n          noticeId: null,\n          noticeTitle: null,\n          noticeContent: null,\n          createBy: null,\n          createTime: null,\n          remark: null\n        };\n        // this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      // 多选框选中数据\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.noticeId)\n        this.single = selection.length!==1\n        this.multiple = !selection.length\n      },\n      transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n      }\n    },\n\n  };\n</script>\n"], "sourceRoot": "src/views/notice"}]}