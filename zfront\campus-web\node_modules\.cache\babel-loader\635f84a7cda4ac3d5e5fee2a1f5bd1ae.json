{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostDetail.vue", "mtime": 1745332328325}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA,eAAA;AACA,EAAA,IAAA,EAAA,sBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,EADA;AAEA,MAAA,QAAA,EAAA,EAFA;AAGA,MAAA,OAAA,EAAA,IAHA;AAIA,MAAA,OAAA,EAAA;AAJA,KAAA;AAMA,GATA;AAUA,EAAA,QAAA,EAAA;AACA,IAAA,QADA,sBACA;AACA,aAAA,KAAA,IAAA,CAAA,QAAA,KAAA,KAAA,OAAA;AACA;AAHA,GAVA;AAeA,EAAA,OAfA,qBAeA;AACA;AACA,QAAA,KAAA,GAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA;;AACA,QAAA,CAAA,KAAA,EAAA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;;AAEA,SAAA,OAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA;AACA,SAAA,eAAA;AACA,GA1BA;AA2BA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,6BACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,KAAA,MAAA,CAAA,MAAA,CAAA,EAAA;AAEA,WAAA,IAAA,uBAAA,MAAA,GAAA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,IAAA,CAAA,QAAA,IAAA,EAAA;AACA,SAHA,MAGA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAZA,EAaA,OAbA,CAaA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAfA;AAgBA,KArBA;AAsBA,IAAA,MAtBA,oBAsBA;AACA,WAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAxBA;AAyBA,IAAA,QAzBA,sBAyBA;AACA,WAAA,OAAA,CAAA,IAAA,kCAAA,KAAA,IAAA,CAAA,EAAA;AACA,KA3BA;AA4BA,IAAA,SA5BA,qBA4BA,KA5BA,EA4BA;AAAA;;AACA,WAAA,IAAA,2BAAA,KAAA,IAAA,CAAA,EAAA,oBAAA,KAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,KAAA,GAAA,MAAA,GAAA,QAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAFA,CAEA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,OAZA;AAaA,KA1CA;AA2CA,IAAA,UA3CA,wBA2CA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,uBAAA,MAAA,CAAA,IAAA,CAAA,EAAA,yBAAA,MAAA,CAAA,OAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,yBAAA;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,SAZA;AAaA,OAlBA,EAkBA,KAlBA,CAkBA,YAAA,CACA;AACA,OApBA;AAqBA,KAjEA;AAkEA,IAAA,aAlEA,yBAkEA,OAlEA,EAkEA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,EAAA,EAAA,QAAA,EAAA,KAAA,OAAA;AAEA,WAAA,QAAA,CAAA,aAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,WAAA,2BAAA,OAAA,CAAA,EAAA,GAAA;AAAA,UAAA,UAAA,EAAA,MAAA,CAAA;AAAA,SAAA;;AAEA,QAAA,MAAA,CAAA,IAAA,0BAAA,OAAA,CAAA,EAAA,GAAA;AAAA,UAAA,UAAA,EAAA,MAAA,CAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,IAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,eAAA,GAFA,CAEA;;AACA,WAHA,MAGA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,eAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SAVA,EAWA,KAXA,CAWA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,GAAA,CAAA,OAAA;AACA,SAdA;AAeA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,OAzBA;AA0BA,KA/FA;AAgGA,IAAA,UAhGA,sBAgGA,OAhGA,EAgGA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA;AApGA;AA3BA,CAAA", "sourcesContent": ["<template>\n    <div class=\"post-detail-container\">\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"15\" animated />\n        </el-card>\n\n        <div v-else>\n            <div class=\"post-actions\">\n                <el-button icon=\"el-icon-back\" @click=\"goBack\">返回</el-button>\n                <div>\n                    <el-button type=\"primary\" icon=\"el-icon-top\" @click=\"toggleTop(!post.isTop)\" v-if=\"post.status === 1\">\n                        {{ post.isTop ? '取消置顶' : '置顶' }}\n                    </el-button>\n                    <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost\" v-if=\"isAuthor\">编辑</el-button>\n                    <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost\">删除</el-button>\n                </div>\n            </div>\n\n            <el-card class=\"post-card\">\n                <div class=\"post-header\">\n                    <h2 class=\"post-title\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </h2>\n                    <div class=\"post-status\" v-if=\"post.status === 0\">\n                        <el-tag type=\"warning\">待审核</el-tag>\n                    </div>\n                    <div class=\"post-status\" v-else-if=\"post.status === 2\">\n                        <el-tag type=\"danger\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-meta\">\n                    <span>\n                        作者: {{ post.author ? post.author.username : '未知' }}\n                        <el-tag size=\"mini\" type=\"success\" v-if=\"post.author && post.author.username === '管理员'\">管理员</el-tag>\n                    </span>\n                    <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                    <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                </div>\n                <div class=\"post-content\">{{ post.content }}</div>\n            </el-card>\n\n            <div class=\"comments-section\">\n                <h3>评论 ({{ post.commentCount || 0 }})</h3>\n\n                <div class=\"comments-list\">\n                    <el-card v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-card\">\n                        <div class=\"comment-header\">\n                            <span class=\"comment-author\">\n                                {{ comment.author ? comment.author.username : '未知' }}\n                                <el-tag size=\"mini\" type=\"success\" v-if=\"comment.author && comment.author.username === '管理员'\">管理员</el-tag>\n                            </span>\n                            <span class=\"comment-time\">{{ formatDate(comment.createTime) }}</span>\n                        </div>\n                        <div class=\"comment-content\">{{ comment.content }}</div>\n                        <div class=\"comment-actions\">\n                            <el-button\n                                size=\"mini\"\n                                type=\"danger\"\n                                icon=\"el-icon-delete\"\n                                @click=\"deleteComment(comment)\"\n                            >\n                                删除\n                            </el-button>\n                        </div>\n                    </el-card>\n\n                    <el-empty v-if=\"comments.length === 0\" description=\"暂无评论\"></el-empty>\n                </div>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostDetail',\n    data() {\n        return {\n            post: {},\n            comments: [],\n            loading: true,\n            adminId: null\n        };\n    },\n    computed: {\n        isAuthor() {\n            return this.post.authorId === this.adminId;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.post = res.data.post;\n                        this.comments = this.post.comments || [];\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        goBack() {\n            this.$router.go(-1);\n        },\n        editPost() {\n            this.$router.push(`/admin/home/<USER>/edit/${this.post.id}`);\n        },\n        toggleTop(isTop) {\n            this.$put(`/forum/post/top/${this.post.id}?isTop=${isTop}`)\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');\n                        this.post.isTop = isTop; // 更新当前帖子的置顶状态\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('置顶操作失败:', err);\n                    this.$message.error('置顶操作失败');\n                });\n        },\n        deletePost() {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                this.$del(`/forum/post/${this.post.id}?operatorId=${this.adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.$router.push('/admin/home/<USER>/posts');\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        deleteComment(comment) {\n            console.log('准备删除评论:', comment.id, '管理员ID:', this.adminId);\n\n            this.$confirm('确定要删除这条评论吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                console.log('发送删除评论请求:', `/forum/comment/${comment.id}`, { operatorId: this.adminId });\n\n                this.$del(`/forum/comment/${comment.id}`, { operatorId: this.adminId })\n                    .then(res => {\n                        console.log('删除评论响应:', res.data);\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPostDetail(); // 重新加载评论\n                        } else {\n                            console.error('删除评论失败，服务器响应:', res.data.msg);\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除评论失败:', err);\n                        this.$message.error('删除评论失败: ' + err.message);\n                    });\n            }).catch(() => {\n                // 取消删除\n                console.log('取消删除评论');\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.post-detail-container {\n    padding: 20px;\n}\n\n.post-actions {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 30px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n}\n\n.post-title {\n    margin: 0;\n    font-size: 24px;\n}\n\n.post-meta {\n    display: flex;\n    gap: 20px;\n    color: #909399;\n    margin-bottom: 20px;\n    font-size: 14px;\n}\n\n.post-content {\n    line-height: 1.6;\n    margin-bottom: 20px;\n    white-space: pre-line;\n}\n\n.comments-section {\n    margin-top: 30px;\n}\n\n.comment-card {\n    margin-bottom: 15px;\n}\n\n.comment-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 10px;\n}\n\n.comment-author {\n    font-weight: bold;\n}\n\n.comment-time {\n    color: #909399;\n    font-size: 14px;\n}\n\n.comment-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n    white-space: pre-line;\n}\n\n.comment-actions {\n    display: flex;\n    gap: 10px;\n    justify-content: flex-end;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}