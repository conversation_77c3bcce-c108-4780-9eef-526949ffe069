{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue", "mtime": 1748450667594}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HA,SAAA,QAAA,EAAA,YAAA,QAAA,MAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,OAAA,kCACA,YAAA,CAAA,OAAA,EAAA,CAAA,UAAA,CAAA,CADA;AAEA,IAAA,YAFA,wBAEA,GAFA,EAEA,OAFA,EAEA,CACA;AACA,KAJA;AAKA,IAAA,aALA,2BAKA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,OAAA;;AACA,UAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,QAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,IAAA,EAAA;AAAA,YAAA,KAAA,EAAA;AAAA;AAAA,SAAA,EAAA,MAAA,CAAA,OAAA,CAAA;AACA,OAJA,CAKA;AACA;AACA;;;AACA,WAAA,SAAA,GAAA,OAAA;AACA,KAdA;AAeA,IAAA,IAfA,kBAeA;AACA,MAAA,cAAA,CAAA,UAAA,CAAA,OAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA;AAlBA,IAFA;AAsBA,EAAA,QAAA,oBACA,QAAA,CAAA,OAAA,EAAA,CAAA,OAAA,CAAA,CADA,CAtBA;AAyBA,EAAA,IAzBA,kBAyBA;AACA,WAAA;AACA;AACA,MAAA,SAAA,EAAA,EAFA;AAGA;AACA,MAAA,WAAA,EAAA,QAAA,CAAA,eAAA,CAAA,WAJA;AAKA,MAAA,WAAA,EAAA,GALA;AAMA;AACA,MAAA,UAAA,EAAA;AAPA,KAAA;AASA,GAnCA;AAoCA,EAAA,KAAA,EAAA;AACA,YADA,kBACA,EADA,EACA,IADA,EACA;AACA,WAAA,aAAA;AACA;AAHA,GApCA;AAyCA,EAAA,OAzCA,qBAyCA;AACA;AACA,QAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACA,WAAA,aAAA;AACA;AACA;AACA;AACA;AACA;AACA,KARA,MAQA;AACA,WAAA,IAAA,CAAA,MAAA,EAAA,OAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,SAAA;AACA;AAEA,GAxDA;AAyDA,EAAA,OAzDA,qBAyDA;AAAA;;AACA,IAAA,MAAA,CAAA,QAAA,GAAA,YAAA;AACA,MAAA,KAAA,CAAA,WAAA,GAAA,QAAA,CAAA,eAAA,CAAA,WAAA;AACA,KAFA;AAGA;AA7DA,CAAA", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"logo\">售后维修一站通</div>\n\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/admin/home\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>类别管理</span>\n                    </template>\n                    <el-menu-item index=\"/admin/home/<USER>\">\n                       <i class=\"el-icon-s-order\"></i>\n                            <span>类别信息</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"4\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>用户管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>用户信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"5\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>任务管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>任务信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"6\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>评价管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>评价信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"7\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-refresh\"></i>\n                        <span>公告管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>系统公告</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"8\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>论坛管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>/create\">\n                            <i class=\"el-icon-edit\"></i>\n                            <span>发布帖子</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/audit\">\n                            <i class=\"el-icon-s-check\"></i>\n                            <span>帖子审核</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/posts\">\n                            <i class=\"el-icon-document\"></i>\n                            <span>帖子列表</span>\n                        </el-menu-item>\n                </el-submenu>\n\n            </el-menu>\n        </div>\n        <div class=\"right\" :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"top\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\"></i>\n                </div>\n                <el-menu\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        @select=\"handleSelect\"\n                        menu-trigger=\"click\">\n                    <el-submenu index=\"2\">\n                        <template slot=\"title\">{{admin.account}}</template>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view></router-view>\n                </transition>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('admin',['setAdmin']),\n            handleSelect(key, keyPath) {\n                // console.log(key, keyPath);\n            },\n            getBreadcrumb(){\n                let matched = this.$route.matched;\n                if(matched[0].name != 'home'){\n                    matched = [{path:\"/admin/home\",meta:{title:'首页'}}].concat(matched)\n                }\n                // if (!this.isHome(matched[0])){\n                //     matched = [{path:\"/home\",meta:{title:'首页'}}].concat(matched);\n                // }\n                this.breadList = matched;\n            },\n            exit(){\n                sessionStorage.removeItem('admin');\n                this.$router.push(\"/admin\")\n            }\n        },\n        computed: {\n            ...mapState('admin',['admin'])\n        },\n        data() {\n            return {\n                //当前路由\n                breadList:[],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb();\n            }\n        },\n        created() {\n            // console.log(sessionStorage.getItem('admin'))\n            if (sessionStorage.getItem(\"admin\")){\n                this.setAdmin(JSON.parse(sessionStorage.getItem(\"admin\")));\n                this.getBreadcrumb();\n                /*let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)\n                console.log(flag)\n                if (flag) {\n                    this.$router.push(\"m/login\")\n                }*/\n            }else {\n                this.$msg(\"尚未登陆\",\"error\")\n                this.$router.push(\"/admin/\")\n            }\n\n        },\n        mounted() {\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n        },\n    }\n\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 100%;\n\n        .left {\n            background: #fff;\n            position: fixed;\n            height: 100%;\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto;\n            }\n        }\n\n        .right {\n            position: relative;\n            .top {\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                background: #fff;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                /*height: 100%;*/\n                /*background: #fff;*/\n\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n    }\n</style>"], "sourceRoot": "src/views/admin"}]}