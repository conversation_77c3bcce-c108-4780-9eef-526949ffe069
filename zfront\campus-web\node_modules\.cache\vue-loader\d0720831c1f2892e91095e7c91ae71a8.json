{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Index.vue", "mtime": 1748510377388}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Zm9ybWF0RGF0ZX0gZnJvbSAnQC91dGlsL2RhdGUnOwppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnOwoKZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogIkluZGV4IiwKICAgIGRhdGEoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgYWN0aXZlTmFtZTogJ2ZpcnN0JywKICAgICAgICAgICAgcG9wVXBzOidmaXJzdCcsCiAgICAgICAgICAgIHRhc2tzOiBbXSwKICAgICAgICAgICAgdXNlcnM6IFtdLAogICAgICAgICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgICAgICAgY2hhcnRzOiB7CiAgICAgICAgICAgICAgICB0YXNrUGllOiBudWxsLAogICAgICAgICAgICAgICAgdXNlckxpbmU6IG51bGwsCiAgICAgICAgICAgICAgICBtb25leUJhcjogbnVsbAogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgICB0cmFuc2Zvcm0odGltZSkgewogICAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKHRpbWUpOwogICAgICAgICAgICByZXR1cm4gZm9ybWF0RGF0ZShkYXRlLCAneXl5eS1NTS1kZCBoaDptbScpOwogICAgICAgIH0sCiAgICAgICAgc2hvdygpIHsKICAgICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICAgIH0sCiAgICAgICAgLy/nlKjmiLfov4fmu6QKICAgICAgICB1c2VyRmlsdGVyaW5nKGNvbmRpdGlvbil7CiAgICAgICAgICAgIGxldCBhcnIgPSB0aGlzLnVzZXJzLmZpbHRlcigoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICAgICAgICAgIC8v6I635Y+W5b2T5aSp5pe26Ze0CiAgICAgICAgICAgICAgICBsZXQgdG9kYXkgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTsKICAgICAgICAgICAgICAgIHRvZGF5ID0gdGhpcy50cmFuc2Zvcm0odG9kYXkpLnN1YnN0cmluZygwLCAxMCk7CiAgICAgICAgICAgICAgICAvL+iOt+WPluiAgeaXtumXtAogICAgICAgICAgICAgICAgbGV0IGNyZWF0ZVRpbWUgPSB0aGlzLnRyYW5zZm9ybShpdGVtLmNyZWF0ZVRpbWUpLnN1YnN0cmluZygwLCAxMCkKICAgICAgICAgICAgICAgIGlmIChjb25kaXRpb24pewogICAgICAgICAgICAgICAgICAgIHJldHVybiB0b2RheSA9PSBjcmVhdGVUaW1lCiAgICAgICAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gdG9kYXkgIT0gY3JlYXRlVGltZQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICByZXR1cm4gYXJyCiAgICAgICAgfSwKICAgICAgICAvL+S7iuWkqeS7u+WKoei/h+a7pAogICAgICAgIHRhc2tGaWx0ZXJpbmcoY29uZGl0aW9uKXsKICAgICAgICAgICAgbGV0IGFyciA9IHRoaXMudGFza3MuZmlsdGVyKChpdGVtLGluZGV4KT0+ewogICAgICAgICAgICAgICAgbGV0IHRvZGF5ID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICAgICAgICAgICAgICB0b2RheSA9IHRoaXMudHJhbnNmb3JtKHRvZGF5KS5zdWJzdHJpbmcoMCwgMTApOwogICAgICAgICAgICAgICAgLy/ojrflj5bogIHml7bpl7QKICAgICAgICAgICAgICAgIGxldCBjcmVhdGVUaW1lID0gdGhpcy50cmFuc2Zvcm0oaXRlbS5jcmVhdGVUaW1lKS5zdWJzdHJpbmcoMCwgMTApCiAgICAgICAgICAgICAgICBpZiAoY29uZGl0aW9uKXsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gdG9kYXkgPT0gY3JlYXRlVGltZQogICAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICAgICAgaWYgKHRvZGF5ID09IGNyZWF0ZVRpbWUgJiYgaXRlbS5zdGF0ZSA9PSAyKXsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHJldHVybiBhcnI7CiAgICAgICAgfSwKICAgICAgICAvL+iuoeeul+S7u+WKoeaUtuebigogICAgICAgIHRhc2tSZXZlbnVlKHBhcmFtZXRlcil7CiAgICAgICAgICAgIGxldCBudW0gPSAwOwogICAgICAgICAgICBmb3IgKGxldCBpID0gMDtpPHBhcmFtZXRlci5sZW5ndGg7aSsrKXsKICAgICAgICAgICAgICAgIG51bSArPSBwYXJhbWV0ZXJbaV0ucmV3YXJkCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIG51bQogICAgICAgIH0sCiAgICAgICAgaW5pdENoYXJ0cygpIHsKICAgICAgICAgICAgLy8g5Yid5aeL5YyW5Lu75Yqh5a6M5oiQ5oOF5Ya16aW85Zu+CiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnRhc2tQaWVDaGFydCAmJiB0aGlzLnRhc2tzLmxlbmd0aCkgewogICAgICAgICAgICAgICAgdGhpcy5jaGFydHMudGFza1BpZSA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnRhc2tQaWVDaGFydCkKICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRlZENvdW50ID0gdGhpcy5jb21wbGV0ZWQubGVuZ3RoCiAgICAgICAgICAgICAgICBjb25zdCB0b3RhbENvdW50ID0gdGhpcy50YXNrcy5sZW5ndGgKICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgdGhpcy5jaGFydHMudGFza1BpZS5zZXRPcHRpb24oewogICAgICAgICAgICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLAogICAgICAgICAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7Yn06IHtjfSAoe2R9JSknCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgICAgICAgICAgICAgICAgb3JpZW50OiAndmVydGljYWwnLAogICAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiAnbGVmdCcKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHNlcmllczogW3sKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3BpZScsCiAgICAgICAgICAgICAgICAgICAgICAgIHJhZGl1czogJzcwJScsCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgdmFsdWU6IGNvbXBsZXRlZENvdW50LCBuYW1lOiAn5bey5a6M5oiQJyB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogdG90YWxDb3VudCAtIGNvbXBsZXRlZENvdW50LCBuYW1lOiAn5pyq5a6M5oiQJyB9CiAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgIGVtcGhhc2lzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjUpJwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWIneWni+WMlueUqOaIt+azqOWGjOi2i+WKv+aKmOe6v+WbvgogICAgICAgICAgICBpZiAodGhpcy4kcmVmcy51c2VyTGluZUNoYXJ0ICYmIHRoaXMudXNlcnMubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0cy51c2VyTGluZSA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnVzZXJMaW5lQ2hhcnQpCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIC8vIOiOt+WPluacgOi/kTflpKnnmoTmlbDmja4KICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVzID0gW10KICAgICAgICAgICAgICAgIGNvbnN0IHRvdGFsQ291bnRzID0gW10KICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJDb3VudHMgPSBbXQogICAgICAgICAgICAgICAgY29uc3QgbWFpbnRlbmFuY2VDb3VudHMgPSBbXQogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gNjsgaSA+PSAwOyBpLS0pIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKQogICAgICAgICAgICAgICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpCiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZVN0ciA9IHRoaXMudHJhbnNmb3JtKGRhdGUpLnN1YnN0cmluZygwLCAxMCkKICAgICAgICAgICAgICAgICAgICBkYXRlcy5wdXNoKGRhdGVTdHIpCiAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgLy8g6K6h566X5b2T5aSp5oC75rOo5YaM5Lq65pWwCiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxDb3VudCA9IHRoaXMudXNlcnMuZmlsdGVyKHVzZXIgPT4gCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudHJhbnNmb3JtKHVzZXIuY3JlYXRlVGltZSkuc3Vic3RyaW5nKDAsIDEwKSA9PT0gZGF0ZVN0cgogICAgICAgICAgICAgICAgICAgICkubGVuZ3RoCiAgICAgICAgICAgICAgICAgICAgdG90YWxDb3VudHMucHVzaCh0b3RhbENvdW50KQogICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgIC8vIOiuoeeul+W9k+WkqeeUqOaIt+azqOWGjOS6uuaVsO+8iOaZrumAmueUqOaIt++8iQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJDb3VudCA9IHRoaXMudXNlcnMuZmlsdGVyKHVzZXIgPT4gCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudHJhbnNmb3JtKHVzZXIuY3JlYXRlVGltZSkuc3Vic3RyaW5nKDAsIDEwKSA9PT0gZGF0ZVN0ciAmJgogICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUubmFtZSA9PT0gJ+eUqOaItycKICAgICAgICAgICAgICAgICAgICApLmxlbmd0aAogICAgICAgICAgICAgICAgICAgIHVzZXJDb3VudHMucHVzaCh1c2VyQ291bnQpCiAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgLy8g6K6h566X5b2T5aSp57u05L+u5ZGY5rOo5YaM5Lq65pWwCiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWFpbnRlbmFuY2VDb3VudCA9IHRoaXMudXNlcnMuZmlsdGVyKHVzZXIgPT4gCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudHJhbnNmb3JtKHVzZXIuY3JlYXRlVGltZSkuc3Vic3RyaW5nKDAsIDEwKSA9PT0gZGF0ZVN0ciAmJgogICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUubmFtZSA9PT0gJ+e7tOS/ruWRmCcKICAgICAgICAgICAgICAgICAgICApLmxlbmd0aAogICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ291bnRzLnB1c2gobWFpbnRlbmFuY2VDb3VudCkKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0cy51c2VyTGluZS5zZXRPcHRpb24oewogICAgICAgICAgICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLAogICAgICAgICAgICAgICAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3NoYWRvdycKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IFsn5oC75rOo5YaM6YePJywgJ+eUqOaIt+azqOWGjCcsICfnu7Tkv67lkZjms6jlhownXSwKICAgICAgICAgICAgICAgICAgICAgICAgdG9wOiAxMAogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgeEF4aXM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogZGF0ZXMsCiAgICAgICAgICAgICAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgcm90YXRlOiA0NQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB5QXhpczogewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5rOo5YaM5Lq65pWwJwogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmgLvms6jlhozph48nLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogdG90YWxDb3VudHMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyM0MDlFRkYnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN5bWJvbFNpemU6IDgKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+eUqOaIt+azqOWGjCcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiB1c2VyQ291bnRzLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNjdDMjNBJwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeW1ib2xTaXplOiA4CiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICfnu7Tkv67lkZjms6jlhownLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogbWFpbnRlbmFuY2VDb3VudHMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyNFNkEyM0MnCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN5bWJvbFNpemU6IDgKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWIneWni+WMluS7u+WKoemHkeminee7n+iuoeafseeKtuWbvgogICAgICAgICAgICBpZiAodGhpcy4kcmVmcy5tb25leUJhckNoYXJ0ICYmIHRoaXMudGFza3MubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0cy5tb25leUJhciA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLm1vbmV5QmFyQ2hhcnQpCiAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgIC8vIOiOt+WPluacgOi/kTflpKnnmoTmlbDmja4KICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVzID0gW10KICAgICAgICAgICAgICAgIGNvbnN0IGFtb3VudHMgPSBbXQogICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDY7IGkgPj0gMDsgaS0tKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCkKICAgICAgICAgICAgICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgLSBpKQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVTdHIgPSB0aGlzLnRyYW5zZm9ybShkYXRlKS5zdWJzdHJpbmcoMCwgMTApCiAgICAgICAgICAgICAgICAgICAgZGF0ZXMucHVzaChkYXRlU3RyKQogICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgIGNvbnN0IGFtb3VudCA9IHRoaXMudGFza3MKICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcih0YXNrID0+IAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50cmFuc2Zvcm0odGFzay5jcmVhdGVUaW1lKS5zdWJzdHJpbmcoMCwgMTApID09PSBkYXRlU3RyCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmJiB0YXNrLnN0YXRlID09PSAyCiAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgLnJlZHVjZSgoc3VtLCB0YXNrKSA9PiBzdW0gKyB0YXNrLnJld2FyZCwgMCkKICAgICAgICAgICAgICAgICAgICBhbW91bnRzLnB1c2goYW1vdW50KQogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIHRoaXMuY2hhcnRzLm1vbmV5QmFyLnNldE9wdGlvbih7CiAgICAgICAgICAgICAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHhBeGlzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGRhdGVzLAogICAgICAgICAgICAgICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdGF0ZTogNDUKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgeUF4aXM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJwogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBhbW91bnRzLAogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYmFyJwogICAgICAgICAgICAgICAgICAgIH1dCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgdGhpcy4kZ2V0KCIvdGFzayIpCiAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdGhpcy50YXNrcyA9IHJlcy5kYXRhLnRhc2sKICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy5pbml0Q2hhcnRzKCkKICAgICAgICAgICAgfSkKICAgICAgICB9KQoKICAgICAgICB0aGlzLiRnZXQoIi91c2VyIikKICAgICAgICAudGhlbigocnMpID0+IHsKICAgICAgICAgICAgdGhpcy51c2VycyA9IHJzLmRhdGEudXNlcgogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLmluaXRDaGFydHMoKQogICAgICAgICAgICB9KQogICAgICAgIH0pCiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICBjb21wbGV0ZWQoKSB7CiAgICAgICAgICAgIGxldCBhcnIgPSB0aGlzLnRhc2tzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLnN0YXRlID09IDIKICAgICAgICAgICAgfSkKICAgICAgICAgICAgcmV0dXJuIGFycgogICAgICAgIH0sCiAgICAgICAgLy/lvZPlpKkKICAgICAgICB0aGF0RGF5KCkgewogICAgICAgICAgICByZXR1cm4gdGhpcy51c2VyRmlsdGVyaW5nKHRydWUpCiAgICAgICAgfSwKICAgICAgICAvL+iAgeeUqOaItwogICAgICAgIG9sZEN1c3RvbWVycygpIHsKICAgICAgICAgICAgcmV0dXJuIHRoaXMudXNlckZpbHRlcmluZyhmYWxzZSkKICAgICAgICB9LAogICAgICAgIG5ld1Rhc2soKXsKICAgICAgICAgICAgcmV0dXJuIHRoaXMudGFza0ZpbHRlcmluZyh0cnVlKQogICAgICAgIH0sCiAgICAgICAgLy/lt7LlrozmiJDku7vliqEKICAgICAgICB0YXNrQ29tcGxldGVkKCl7CiAgICAgICAgICAgIHJldHVybiB0aGlzLnRhc2tGaWx0ZXJpbmcoZmFsc2UpCiAgICAgICAgfSwKICAgICAgICAvL+mHkemineiuoeeulwogICAgICAgIG1vbmV5KCl7CiAgICAgICAgICAgIHJldHVybiB0aGlzLnRhc2tSZXZlbnVlKHRoaXMudGFza0NvbXBsZXRlZCkKICAgICAgICB9LAogICAgICAgIC8v5omA5pyJ5a6M5oiQ5Lu75YqhCiAgICAgICAgbWlzc2lvbkFjY29tcGxpc2hlZCgpewogICAgICAgICAgICBsZXQgYXJyID0gdGhpcy50YXNrcy5maWx0ZXIoKGl0ZW0saW5kZXgpID0+IHsKICAgICAgICAgICAgICAgIGlmIChpdGVtLnN0YXRlID09IDIpewogICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHJldHVybiBhcnI7CiAgICAgICAgfSwKICAgICAgICAvL+aJgOaciemHkeminQogICAgICAgIGFsbEFtb3VudHMoKXsKICAgICAgICAgICAgcmV0dXJuIHRoaXMudGFza1JldmVudWUodGhpcy5taXNzaW9uQWNjb21wbGlzaGVkKQogICAgICAgIH0KICAgIH0sCiAgICBmaWx0ZXJzOiB7CiAgICAgICAgZm9ybWF0RGF0ZSh0aW1lKSB7CiAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUodGltZSk7CiAgICAgICAgICAgIHJldHVybiBmb3JtYXREYXRlKGRhdGUsICd5eXl5LU1NLWRkIGhoOm1tJyk7CiAgICAgICAgfQogICAgfSwKICAgIG1vdW50ZWQoKSB7CiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsICgpID0+IHsKICAgICAgICAgICAgdGhpcy5jaGFydHMudGFza1BpZSAmJiB0aGlzLmNoYXJ0cy50YXNrUGllLnJlc2l6ZSgpCiAgICAgICAgICAgIHRoaXMuY2hhcnRzLnVzZXJMaW5lICYmIHRoaXMuY2hhcnRzLnVzZXJMaW5lLnJlc2l6ZSgpCiAgICAgICAgICAgIHRoaXMuY2hhcnRzLm1vbmV5QmFyICYmIHRoaXMuY2hhcnRzLm1vbmV5QmFyLnJlc2l6ZSgpCiAgICAgICAgfSkKICAgIH0sCiAgICBiZWZvcmVEZXN0cm95KCkgewogICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCAoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuY2hhcnRzLnRhc2tQaWUgJiYgdGhpcy5jaGFydHMudGFza1BpZS5yZXNpemUoKQogICAgICAgICAgICB0aGlzLmNoYXJ0cy51c2VyTGluZSAmJiB0aGlzLmNoYXJ0cy51c2VyTGluZS5yZXNpemUoKQogICAgICAgICAgICB0aGlzLmNoYXJ0cy5tb25leUJhciAmJiB0aGlzLmNoYXJ0cy5tb25leUJhci5yZXNpemUoKQogICAgICAgIH0pCiAgICB9Cn0K"}, {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Index.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <div class=\"title\">\n            <div class=\"title_left\">\n                <h2>{{completed.length}}</h2>\n                <p>已完成任务</p>\n            </div>\n            <div class=\"title_center\">\n                <h2>{{tasks.length}}</h2>\n                <p>任务数量</p>\n            </div>\n            <div class=\"title_right\">\n                <h2>{{users.length}}</h2>\n                <p>总用户</p>\n            </div>\n        </div>\n        <div class=\"bottom\">\n            <el-tabs v-model=\"activeName\" type=\"card\">\n                <el-tab-pane label=\"用户管理\" name=\"first\">\n                    <div class=\"card\">\n                        <el-card class=\"box-card\">\n                            <div slot=\"header\" class=\"clearfix\">\n                                <span>用户</span>\n                                <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"show\">查看</el-button>\n                            </div>\n                            <div class=\"bottom_txt\">\n                                <p>新增用户<span>{{thatDay.length}}</span></p>\n                                <p>老用户<span>{{oldCustomers.length}}</span></p>\n                            </div>\n                        </el-card>\n                        <el-card class=\"box-card\">\n                            <div slot=\"header\" class=\"clearfix\">\n                                <span>任务</span>\n                            </div>\n                            <div class=\"bottom_txt\">\n                                <p>今天新增任务<span>{{newTask.length}}</span></p>\n                                <p>今天已完成/金额<span>{{taskCompleted.length}}/{{money}}</span></p>\n                            </div>\n                        </el-card>\n                        <el-card class=\"box-card\">\n                            <div slot=\"header\" class=\"clearfix\">\n                                <span>所有任务</span>\n                            </div>\n                            <div class=\"bottom_txt\">\n                                <p>完成任务总数<span>{{missionAccomplished.length}}</span></p>\n                                <p>总数金额<span>{{allAmounts}}</span></p>\n                            </div>\n                        </el-card>\n                    </div>\n                </el-tab-pane>\n            </el-tabs>\n        </div>\n        \n        <div class=\"charts-container\">\n            <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                    <el-card class=\"chart-card\">\n                        <div slot=\"header\">\n                            <span>任务完成情况</span>\n                        </div>\n                        <div ref=\"taskPieChart\" class=\"chart\"></div>\n                    </el-card>\n                </el-col>\n                <el-col :span=\"8\">\n                    <el-card class=\"chart-card\">\n                        <div slot=\"header\">\n                            <span>用户注册趋势</span>\n                        </div>\n                        <div ref=\"userLineChart\" class=\"chart\"></div>\n                    </el-card>\n                </el-col>\n                <el-col :span=\"8\">\n                    <el-card class=\"chart-card\">\n                        <div slot=\"header\">\n                            <span>任务金额统计</span>\n                        </div>\n                        <div ref=\"moneyBarChart\" class=\"chart\"></div>\n                    </el-card>\n                </el-col>\n            </el-row>\n        </div>\n\n        <el-dialog\n                title=\"提示\"\n                :visible.sync=\"dialogVisible\"\n                width=\"60%\">\n            <el-tabs v-model=\"popUps\" type=\"card\">\n                <el-tab-pane label=\"新用户\" name=\"first\">\n                    <el-table\n                            :data=\"thatDay\"\n                            border\n                            style=\"width: 100%\">\n                        <el-table-column\n                                fixed\n                                prop=\"studentId\"\n                                label=\"账号\"\n                                min-width=\"120\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"username\"\n                                label=\"姓名\"\n                                min-width=\"100\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"role.name\"\n                                label=\"类别\"\n                                min-width=\"160\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"createTime\"\n                                label=\"注册时间\"\n                                min-width=\"140\">\n                            <template slot-scope=\"scope\">\n                                {{scope.row.createTime | formatDate}}\n                            </template>\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"sex\"\n                                label=\"性别\"\n                                min-width=\"100\">\n                            <template slot-scope=\"scope\">\n                                {{scope.row.sex==0 ? '男' : '女'}}\n                            </template>\n                        </el-table-column>\n                    </el-table>\n                </el-tab-pane>\n                <el-tab-pane label=\"老用户\" name=\"second\">\n                    <el-table\n                            :data=\"oldCustomers\"\n                            border\n                            style=\"width: 100%\">\n                        <el-table-column\n                                fixed\n                                prop=\"studentId\"\n                                label=\"账号\"\n                                min-width=\"120\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"username\"\n                                label=\"姓名\"\n                                min-width=\"100\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"role.name\"\n                                label=\"类别\"\n                                min-width=\"160\">\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"createTime\"\n                                label=\"注册时间\"\n                                min-width=\"140\">\n                            <template slot-scope=\"scope\">\n                                {{scope.row.createTime | formatDate}}\n                            </template>\n                        </el-table-column>\n                        <el-table-column\n                                prop=\"sex\"\n                                label=\"性别\"\n                                min-width=\"100\">\n                            <template slot-scope=\"scope\">\n                                {{scope.row.sex==0 ? '男' : '女'}}\n                            </template>\n                        </el-table-column>\n                    </el-table>\n                </el-tab-pane>\n            </el-tabs>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {formatDate} from '@/util/date';\n    import * as echarts from 'echarts';\n\n    export default {\n        name: \"Index\",\n        data() {\n            return {\n                activeName: 'first',\n                popUps:'first',\n                tasks: [],\n                users: [],\n                dialogVisible: false,\n                charts: {\n                    taskPie: null,\n                    userLine: null,\n                    moneyBar: null\n                }\n            }\n        },\n        methods: {\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n            show() {\n                this.dialogVisible = true\n            },\n            //用户过滤\n            userFiltering(condition){\n                let arr = this.users.filter((item, index) => {\n                    //获取当天时间\n                    let today = new Date().getTime();\n                    today = this.transform(today).substring(0, 10);\n                    //获取老时间\n                    let createTime = this.transform(item.createTime).substring(0, 10)\n                    if (condition){\n                        return today == createTime\n                    }else{\n                        return today != createTime\n                    }\n                })\n                return arr\n            },\n            //今天任务过滤\n            taskFiltering(condition){\n                let arr = this.tasks.filter((item,index)=>{\n                    let today = new Date().getTime();\n                    today = this.transform(today).substring(0, 10);\n                    //获取老时间\n                    let createTime = this.transform(item.createTime).substring(0, 10)\n                    if (condition){\n                        return today == createTime\n                    }else{\n                        if (today == createTime && item.state == 2){\n                            return true\n                        }\n                    }\n                })\n                return arr;\n            },\n            //计算任务收益\n            taskRevenue(parameter){\n                let num = 0;\n                for (let i = 0;i<parameter.length;i++){\n                    num += parameter[i].reward\n                }\n                return num\n            },\n            initCharts() {\n                // 初始化任务完成情况饼图\n                if (this.$refs.taskPieChart && this.tasks.length) {\n                    this.charts.taskPie = echarts.init(this.$refs.taskPieChart)\n                    const completedCount = this.completed.length\n                    const totalCount = this.tasks.length\n                    \n                    this.charts.taskPie.setOption({\n                        tooltip: {\n                            trigger: 'item',\n                            formatter: '{b}: {c} ({d}%)'\n                        },\n                        legend: {\n                            orient: 'vertical',\n                            left: 'left'\n                        },\n                        series: [{\n                            type: 'pie',\n                            radius: '70%',\n                            data: [\n                                { value: completedCount, name: '已完成' },\n                                { value: totalCount - completedCount, name: '未完成' }\n                            ],\n                            emphasis: {\n                                itemStyle: {\n                                    shadowBlur: 10,\n                                    shadowOffsetX: 0,\n                                    shadowColor: 'rgba(0, 0, 0, 0.5)'\n                                }\n                            }\n                        }]\n                    })\n                }\n\n                // 初始化用户注册趋势折线图\n                if (this.$refs.userLineChart && this.users.length) {\n                    this.charts.userLine = echarts.init(this.$refs.userLineChart)\n                    \n                    // 获取最近7天的数据\n                    const dates = []\n                    const totalCounts = []\n                    const userCounts = []\n                    const maintenanceCounts = []\n                    \n                    for (let i = 6; i >= 0; i--) {\n                        const date = new Date()\n                        date.setDate(date.getDate() - i)\n                        const dateStr = this.transform(date).substring(0, 10)\n                        dates.push(dateStr)\n                        \n                        // 计算当天总注册人数\n                        const totalCount = this.users.filter(user => \n                            this.transform(user.createTime).substring(0, 10) === dateStr\n                        ).length\n                        totalCounts.push(totalCount)\n                        \n                        // 计算当天用户注册人数（普通用户）\n                        const userCount = this.users.filter(user => \n                            this.transform(user.createTime).substring(0, 10) === dateStr &&\n                            user.role.name === '用户'\n                        ).length\n                        userCounts.push(userCount)\n                        \n                        // 计算当天维修员注册人数\n                        const maintenanceCount = this.users.filter(user => \n                            this.transform(user.createTime).substring(0, 10) === dateStr &&\n                            user.role.name === '维修员'\n                        ).length\n                        maintenanceCounts.push(maintenanceCount)\n                    }\n\n                    this.charts.userLine.setOption({\n                        tooltip: {\n                            trigger: 'axis',\n                            axisPointer: {\n                                type: 'shadow'\n                            }\n                        },\n                        legend: {\n                            data: ['总注册量', '用户注册', '维修员注册'],\n                            top: 10\n                        },\n                        xAxis: {\n                            type: 'category',\n                            data: dates,\n                            axisLabel: {\n                                rotate: 45\n                            }\n                        },\n                        yAxis: {\n                            type: 'value',\n                            name: '注册人数'\n                        },\n                        series: [\n                            {\n                                name: '总注册量',\n                                data: totalCounts,\n                                type: 'line',\n                                smooth: true,\n                                lineStyle: {\n                                    width: 2,\n                                    color: '#409EFF'\n                                },\n                                symbol: 'circle',\n                                symbolSize: 8\n                            },\n                            {\n                                name: '用户注册',\n                                data: userCounts,\n                                type: 'line',\n                                smooth: true,\n                                lineStyle: {\n                                    width: 2,\n                                    color: '#67C23A'\n                                },\n                                symbol: 'circle',\n                                symbolSize: 8\n                            },\n                            {\n                                name: '维修员注册',\n                                data: maintenanceCounts,\n                                type: 'line',\n                                smooth: true,\n                                lineStyle: {\n                                    width: 2,\n                                    color: '#E6A23C'\n                                },\n                                symbol: 'circle',\n                                symbolSize: 8\n                            }\n                        ]\n                    })\n                }\n\n                // 初始化任务金额统计柱状图\n                if (this.$refs.moneyBarChart && this.tasks.length) {\n                    this.charts.moneyBar = echarts.init(this.$refs.moneyBarChart)\n                    \n                    // 获取最近7天的数据\n                    const dates = []\n                    const amounts = []\n                    for (let i = 6; i >= 0; i--) {\n                        const date = new Date()\n                        date.setDate(date.getDate() - i)\n                        const dateStr = this.transform(date).substring(0, 10)\n                        dates.push(dateStr)\n                        \n                        const amount = this.tasks\n                            .filter(task => \n                                this.transform(task.createTime).substring(0, 10) === dateStr\n                                && task.state === 2\n                            )\n                            .reduce((sum, task) => sum + task.reward, 0)\n                        amounts.push(amount)\n                    }\n\n                    this.charts.moneyBar.setOption({\n                        tooltip: {\n                            trigger: 'axis'\n                        },\n                        xAxis: {\n                            type: 'category',\n                            data: dates,\n                            axisLabel: {\n                                rotate: 45\n                            }\n                        },\n                        yAxis: {\n                            type: 'value'\n                        },\n                        series: [{\n                            data: amounts,\n                            type: 'bar'\n                        }]\n                    })\n                }\n            }\n        },\n        created() {\n            this.$get(\"/task\")\n            .then(res => {\n                this.tasks = res.data.task\n                this.$nextTick(() => {\n                    this.initCharts()\n                })\n            })\n\n            this.$get(\"/user\")\n            .then((rs) => {\n                this.users = rs.data.user\n                this.$nextTick(() => {\n                    this.initCharts()\n                })\n            })\n        },\n        computed: {\n            completed() {\n                let arr = this.tasks.filter(function (item, index) {\n                    return item.state == 2\n                })\n                return arr\n            },\n            //当天\n            thatDay() {\n                return this.userFiltering(true)\n            },\n            //老用户\n            oldCustomers() {\n                return this.userFiltering(false)\n            },\n            newTask(){\n                return this.taskFiltering(true)\n            },\n            //已完成任务\n            taskCompleted(){\n                return this.taskFiltering(false)\n            },\n            //金额计算\n            money(){\n                return this.taskRevenue(this.taskCompleted)\n            },\n            //所有完成任务\n            missionAccomplished(){\n                let arr = this.tasks.filter((item,index) => {\n                    if (item.state == 2){\n                        return true\n                    }\n                })\n                return arr;\n            },\n            //所有金额\n            allAmounts(){\n                return this.taskRevenue(this.missionAccomplished)\n            }\n        },\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        },\n        mounted() {\n            window.addEventListener('resize', () => {\n                this.charts.taskPie && this.charts.taskPie.resize()\n                this.charts.userLine && this.charts.userLine.resize()\n                this.charts.moneyBar && this.charts.moneyBar.resize()\n            })\n        },\n        beforeDestroy() {\n            window.removeEventListener('resize', () => {\n                this.charts.taskPie && this.charts.taskPie.resize()\n                this.charts.userLine && this.charts.userLine.resize()\n                this.charts.moneyBar && this.charts.moneyBar.resize()\n            })\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0px 15px;\n        height: 100%;\n        padding: 15px;\n\n        .title {\n            margin: 15px 0;\n            width: 100%;\n            display: flex;\n            height: 100px;\n            justify-content: space-between;\n\n            div {\n                border-radius: 5px;\n                padding: 15px;\n                color: #fff;\n            }\n\n            div p {\n                font-size: 12px;\n                margin-top: 10px;\n            }\n\n            .title_left {\n                width: 32.5%;\n                background: #e64242;\n            }\n\n            .title_center {\n                width: 32.5%;\n                background: #11b95c;\n            }\n\n            .title_right {\n                width: 32.5%;\n                background: #1f2d3d;\n            }\n        }\n\n        .card {\n            display: flex;\n            margin-bottom: 25px;\n            justify-content: space-between;\n\n            /deep/ .el-card.is-always-shadow {\n                width: 32.4%;\n            }\n        }\n\n        .bottom_txt p:nth-child(1) {\n            margin-top: 10px;\n        }\n\n        .bottom_txt p:nth-child(2) {\n            margin: 15px 0;\n        }\n\n        .bottom_txt span {\n            float: right;\n        }\n\n        /deep/ .el-card__body {\n            padding: 10px 20px !important;\n        }\n    }\n\n    /deep/ .el-alert--info.is-light {\n        height: 50px;\n    }\n\n    .charts-container {\n        margin-top: 20px;\n        \n        .chart-card {\n            margin-bottom: 20px;\n            \n            .chart {\n                height: 300px;\n                width: 100%;\n            }\n        }\n    }\n</style>"]}]}