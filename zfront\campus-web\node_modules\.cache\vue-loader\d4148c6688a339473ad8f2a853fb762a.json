{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostCreate.vue", "mtime": 1745151314454}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostCreate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AdminForumPostCreate.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"create-post-container\">\n        <div class=\"page-header\">\n            <h2>发布帖子</h2>\n        </div>\n\n        <el-form :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">发布</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostCreate',\n    data() {\n        return {\n            postForm: {\n                title: '',\n                content: '',\n                authorId: JSON.parse(sessionStorage.getItem('admin')).id // 使用管理员ID\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    methods: {\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    this.$post('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('发布成功'); // 管理员发布无需审核\n                                this.$router.push('/admin/home/<USER>/posts');\n                            } else {\n                                this.$message.error(res.data.msg || '发布失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('发布帖子失败:', err);\n                            this.$message.error('发布帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.create-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n</style>\n"]}]}