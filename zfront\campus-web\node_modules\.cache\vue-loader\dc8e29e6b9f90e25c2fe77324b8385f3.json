{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue?vue&type=style&index=0&id=ba370ada&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumAudit.vue", "mtime": 1745149170618}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hdWRpdC1wb3N0cy1jb250YWluZXIgewogICAgcGFkZGluZzogMjBweDsKfQoKLnBhZ2UtaGVhZGVyIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5wb3N0LWNhcmQgewogICAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLnBvc3QtaGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLnBvc3QtdGl0bGUgewogICAgZm9udC1zaXplOiAxOHB4OwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBjb2xvcjogIzMwMzEzMzsKICAgIGN1cnNvcjogcG9pbnRlcjsKfQoKLnBvc3QtY29udGVudCB7CiAgICBjb2xvcjogIzYwNjI2NjsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICBsaW5lLWhlaWdodDogMS41Owp9CgoucG9zdC1mb290ZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5wb3N0LWluZm8gewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMTVweDsKICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgZm9udC1zaXplOiAxNHB4Owp9CgoucG9zdC1hY3Rpb25zIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBnYXA6IDEwcHg7Cn0KCi5sb2FkaW5nLWNhcmQgewogICAgcGFkZGluZzogMjBweDsKfQo="}, {"version": 3, "sources": ["AdminForumAudit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "AdminForumAudit.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"audit-posts-container\">\n        <div class=\"page-header\">\n            <h2>帖子审核</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已通过\" name=\"approved\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已通过</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <template v-if=\"post.status === 0\">\n                            <el-button size=\"mini\" type=\"success\" @click=\"approvePost(post.id)\">通过</el-button>\n                            <el-button size=\"mini\" type=\"danger\" @click=\"rejectPost(post.id)\">拒绝</el-button>\n                        </template>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" :description=\"getEmptyDescription()\"></el-empty>\n        </div>\n\n        <!-- 拒绝原因对话框 -->\n        <el-dialog title=\"拒绝原因\" :visible.sync=\"rejectDialogVisible\" width=\"30%\">\n            <el-form :model=\"rejectForm\" :rules=\"rejectRules\" ref=\"rejectForm\">\n                <el-form-item label=\"原因\" prop=\"reason\">\n                    <el-input\n                        type=\"textarea\"\n                        v-model=\"rejectForm.reason\"\n                        placeholder=\"请输入拒绝原因\"\n                        :rows=\"4\"\n                    ></el-input>\n                </el-form-item>\n            </el-form>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"rejectDialogVisible = false\">取消</el-button>\n                <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"submitting\">确定</el-button>\n            </span>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumAudit',\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'pending',\n            rejectDialogVisible: false,\n            rejectForm: {\n                postId: null,\n                reason: ''\n            },\n            rejectRules: {\n                reason: [\n                    { required: true, message: '请输入拒绝原因', trigger: 'blur' },\n                    { min: 5, max: 200, message: '原因长度在5到200个字符之间', trigger: 'blur' }\n                ]\n            },\n            submitting: false\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'approved') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        // 检查管理员登录状态\n        if (!sessionStorage.getItem('admin')) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n            // 使用实际的管理员ID\n            const admin = JSON.parse(sessionStorage.getItem('admin'));\n            const userId = admin.id; // 管理员ID\n\n            this.$get('/forum/post/list', {\n                pageNum: 1,\n                pageSize: 100, // 设置较大的页面大小以获取所有帖子\n                userId: userId,\n                isAdminRequest: true // 标记这是管理员请求\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/admin/home/<USER>/post/${id}`);\n        },\n        approvePost(id) {\n            this.$confirm('确定通过这篇帖子吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'info'\n            }).then(() => {\n                this.$put(`/forum/post/audit/${id}?status=1`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('审核通过成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '操作失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('审核操作失败:', err);\n                        this.$message.error('审核操作失败');\n                    });\n            }).catch(() => {\n                // 取消操作\n            });\n        },\n        rejectPost(id) {\n            this.rejectForm.postId = id;\n            this.rejectForm.reason = '';\n            this.rejectDialogVisible = true;\n        },\n        confirmReject() {\n            this.$refs.rejectForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n\n                    this.$put(`/forum/post/audit/${this.rejectForm.postId}?status=2&reason=${encodeURIComponent(this.rejectForm.reason)}`)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('拒绝成功');\n                                this.rejectDialogVisible = false;\n                                this.fetchPosts(); // 重新加载帖子列表\n                            } else {\n                                this.$message.error(res.data.msg || '操作失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('拒绝操作失败:', err);\n                            this.$message.error('拒绝操作失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const adminId = JSON.parse(sessionStorage.getItem('admin')).id;\n                this.$del(`/forum/post/${id}?operatorId=${adminId}`)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        getEmptyDescription() {\n            if (this.activeTab === 'pending') {\n                return '暂无待审核帖子';\n            } else if (this.activeTab === 'approved') {\n                return '暂无已通过帖子';\n            } else if (this.activeTab === 'rejected') {\n                return '暂无已拒绝帖子';\n            }\n            return '暂无帖子';\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.audit-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}