{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue", "mtime": 1748720502030}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Zm9ybWF0RGF0ZX0gZnJvbSAnQC91dGlsL2RhdGUnOwppbXBvcnQgQ29tbW9uUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvQ29tbW9uUGFnaW5hdGlvbi52dWUnOwoKZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogIlRhc2siLAogICAgY29tcG9uZW50czogewogICAgICAgIENvbW1vblBhZ2luYXRpb24KICAgIH0sCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvbGVzOltdLAogICAgICAgICAgICB0YXNrczogW10sCiAgICAgICAgICAgIGFsbFRhc2tzOiBbXSwKICAgICAgICAgICAgcnVsZUZvcm06IHsKICAgICAgICAgICAgICAgIGlkOiAwLAogICAgICAgICAgICAgICAgc3RhdGU6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgICAge2xhYmVsOiAi5q2j5bi4IiwgdmFsdWU6ICIwIn0sCiAgICAgICAgICAgICAgICB7bGFiZWw6ICLnpoHnlKgiLCB2YWx1ZTogIjEifQogICAgICAgICAgICBdLAogICAgICAgICAgICBpbnB1dDoiIiwKICAgICAgICAgICAgc2VsZWN0OiAiMSIsCiAgICAgICAgICAgIC8vIOWIhumhteebuOWFs+aVsOaNrgogICAgICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgICAgfQogICAgfSwKICAgIGNvbXB1dGVkOiB7CiAgICAgICAgcGFnaW5hdGVkVGFza3MoKSB7CiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gKHRoaXMuY3VycmVudFBhZ2UgLSAxKSAqIHRoaXMucGFnZVNpemU7CiAgICAgICAgICAgIGNvbnN0IGVuZCA9IHN0YXJ0ICsgdGhpcy5wYWdlU2l6ZTsKICAgICAgICAgICAgcmV0dXJuIHRoaXMudGFza3Muc2xpY2Uoc3RhcnQsIGVuZCk7CiAgICAgICAgfQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgICAvLyDlpITnkIbliIbpobXlj5jljJYKICAgICAgICBoYW5kbGVQYWdpbmF0aW9uQ2hhbmdlKCkgewogICAgICAgICAgICAvLyDlpoLmnpzpnIDopoHlnKjliIbpobXlj5jljJbml7bmiafooYzlhbbku5bmk43kvZzvvIzlj6/ku6XlnKjov5nph4zmt7vliqAKICAgICAgICB9LAoKICAgICAgICAvLyDkv67mlLnmkJzntKLmlrnms5UKICAgICAgICBjbGlja1NlYXJjaCgpIHsKICAgICAgICAgICAgaWYgKCF0aGlzLmlucHV0LnRyaW0oKSkgewogICAgICAgICAgICAgICAgdGhpcy50YXNrcyA9IHRoaXMuYWxsVGFza3M7CiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgY29uc3Qgc2VhcmNoVGV4dCA9IHRoaXMuaW5wdXQudHJpbSgpLnRvTG93ZXJDYXNlKCk7CiAgICAgICAgICAgIGxldCBmaWx0ZXJlZFRhc2tzID0gW107CgogICAgICAgICAgICBzd2l0Y2ggKHRoaXMuc2VsZWN0KSB7CiAgICAgICAgICAgICAgICBjYXNlICIxIjogLy8g5Lu75Yqh5qCH6aKYCiAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWRUYXNrcyA9IHRoaXMuYWxsVGFza3MuZmlsdGVyKHRhc2sgPT4KICAgICAgICAgICAgICAgICAgICAgICAgdGFzay50YXNrVGl0bGUgJiYgdGFzay50YXNrVGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXh0KQogICAgICAgICAgICAgICAgICAgICk7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICBjYXNlICIyIjogLy8g5Y+R5biD5Lq6CiAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWRUYXNrcyA9IHRoaXMuYWxsVGFza3MuZmlsdGVyKHRhc2sgPT4KICAgICAgICAgICAgICAgICAgICAgICAgKHRhc2sucHVibGlzaCAmJiB0YXNrLnB1Ymxpc2gudXNlcm5hbWUgJiYgdGFzay5wdWJsaXNoLnVzZXJuYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGV4dCkpIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICh0YXNrLnB1Ymxpc2ggJiYgdGFzay5wdWJsaXNoLnN0dWRlbnRJZCAmJiB0YXNrLnB1Ymxpc2guc3R1ZGVudElkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGV4dCkpCiAgICAgICAgICAgICAgICAgICAgKTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIGNhc2UgIjMiOiAvLyDnu7Tkv67lkZgKICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFRhc2tzID0gdGhpcy5hbGxUYXNrcy5maWx0ZXIodGFzayA9PgogICAgICAgICAgICAgICAgICAgICAgICAodGFzay5hY2NlcHQgJiYgdGFzay5hY2NlcHQudXNlcm5hbWUgJiYgdGFzay5hY2NlcHQudXNlcm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXh0KSkgfHwKICAgICAgICAgICAgICAgICAgICAgICAgKHRhc2suYWNjZXB0ICYmIHRhc2suYWNjZXB0LnN0dWRlbnRJZCAmJiB0YXNrLmFjY2VwdC5zdHVkZW50SWQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXh0KSkKICAgICAgICAgICAgICAgICAgICApOwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgY2FzZSAiNCI6IC8vIOS7u+WKoeeKtuaAgQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsKICAgICAgICAgICAgICAgICAgICAgICAgJ+W+heaOpeWNlSc6IDAsCiAgICAgICAgICAgICAgICAgICAgICAgICfov5vooYzkuK0nOiAxLAogICAgICAgICAgICAgICAgICAgICAgICAn5bey5a6M5oiQJzogMgogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhdHVzVmFsdWUgPSBzdGF0dXNNYXBbc2VhcmNoVGV4dF0gIT09IHVuZGVmaW5lZCA/IHN0YXR1c01hcFtzZWFyY2hUZXh0XSA6IG51bGw7CiAgICAgICAgICAgICAgICAgICAgaWYgKHN0YXR1c1ZhbHVlICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkVGFza3MgPSB0aGlzLmFsbFRhc2tzLmZpbHRlcih0YXNrID0+IHRhc2suc3RhdGUgPT09IHN0YXR1c1ZhbHVlKTsKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFRhc2tzID0gdGhpcy5hbGxUYXNrcy5maWx0ZXIodGFzayA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGF0ZVRleHQgPSB0aGlzLmdldFN0YXRlVGV4dCh0YXNrLnN0YXRlKS50b0xvd2VyQ2FzZSgpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHN0YXRlVGV4dC5pbmNsdWRlcyhzZWFyY2hUZXh0KTsKICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgY2FzZSAiNSI6IC8vIOe7tOS/ruexu+WIqwogICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkVGFza3MgPSB0aGlzLmFsbFRhc2tzLmZpbHRlcih0YXNrID0+CiAgICAgICAgICAgICAgICAgICAgICAgICh0YXNrLmRlcHQgJiYgdGFzay5kZXB0Lm5hbWUgJiYgdGFzay5kZXB0Lm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXh0KSkgfHwKICAgICAgICAgICAgICAgICAgICAgICAgKHRhc2sudHlwZSAmJiB0YXNrLnR5cGUubmFtZSAmJiB0YXNrLnR5cGUubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRleHQpKQogICAgICAgICAgICAgICAgICAgICk7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICBkZWZhdWx0OgogICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkVGFza3MgPSB0aGlzLmFsbFRhc2tzOwogICAgICAgICAgICB9CgogICAgICAgICAgICB0aGlzLnRhc2tzID0gZmlsdGVyZWRUYXNrczsKICAgICAgICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CgogICAgICAgICAgICBpZiAoZmlsdGVyZWRUYXNrcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5pyq5om+5Yiw5Yy56YWN55qE5Lu75YqhJyk7CiAgICAgICAgICAgIH0KICAgICAgICB9LAoKICAgICAgICAvLyDkv67mlLnmuIXnqbrmkJzntKLmlrnms5XvvIzkv53mjIHliIbpobXnirbmgIEKICAgICAgICBjbGVhclNlYXJjaCgpIHsKICAgICAgICAgICAgdGhpcy5pbnB1dCA9ICcnOwogICAgICAgICAgICB0aGlzLnRhc2tzID0gdGhpcy5hbGxUYXNrczsKICAgICAgICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7IC8vIOmHjee9ruWIsOesrOS4gOmhtQogICAgICAgIH0sCgogICAgICAgIGdldFN0YXRlVHlwZShzdGF0ZSkgewogICAgICAgICAgICBjb25zdCB0eXBlcyA9IHsKICAgICAgICAgICAgICAgIDA6ICdpbmZvJywgICAgLy8g5b6F5o6l5Y2VCiAgICAgICAgICAgICAgICAxOiAnd2FybmluZycsIC8vIOi/m+ihjOS4rQogICAgICAgICAgICAgICAgMjogJ3N1Y2Nlc3MnICAvLyDlt7LlrozmiJAKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gdHlwZXNbc3RhdGVdIHx8ICdpbmZvJwogICAgICAgIH0sCiAgICAgICAgZ2V0U3RhdGVUZXh0KHN0YXRlKSB7CiAgICAgICAgICAgIGNvbnN0IHRleHRzID0gewogICAgICAgICAgICAgICAgMDogJ+W+heaOpeWNlScsCiAgICAgICAgICAgICAgICAxOiAn6L+b6KGM5LitJywKICAgICAgICAgICAgICAgIDI6ICflt7LlrozmiJAnCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHRleHRzW3N0YXRlXSB8fCAn5pyq55+lJwogICAgICAgIH0sCiAgICAgICAgdHJhbnNmb3JtKHRpbWUpIHsKICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsKICAgICAgICAgICAgcmV0dXJuIGZvcm1hdERhdGUoZGF0ZSwgJ3l5eXktTU0tZGQgaGg6bW0nKTsKICAgICAgICB9LAogICAgICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5Yig6Zmk6K+l5Lu75YqhPycsICfmj5DnpLonLCB7CiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJGRlbChgL3Rhc2svJHtyb3cuaWR9YCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KCk7CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuZGF0YS5tc2cgfHwgJ+WIoOmZpOWksei0pScpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlVG9wVG9nZ2xlKHJvdykgewogICAgICAgICAgICBjb25zdCBhY3Rpb24gPSByb3cuaXNUb3AgPyAnY2FuY2VsVG9wJyA6ICd0b3AnOwogICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gcm93LmlzVG9wID8gJ+WPlua2iOe9rumhticgOiAn572u6aG2JzsKCiAgICAgICAgICAgIHRoaXMuJHB1dChgL3Rhc2svJHthY3Rpb259LyR7cm93LmlkfWApLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7bWVzc2FnZX3miJDlip9gKTsKICAgICAgICAgICAgICAgICAgICByb3cuaXNUb3AgPSAhcm93LmlzVG9wOwogICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCgpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5kYXRhLm1zZyB8fCBgJHttZXNzYWdlfeWksei0pWApOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KS5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgJHttZXNzYWdlfeWksei0pTpgLCBlcnIpOwogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHttZXNzYWdlfeWksei0pe+8jOivt+eojeWQjumHjeivlWApOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIG5ld0xpc3QoKSB7CiAgICAgICAgICAgIHRoaXMuJGdldCgiL3Rhc2siKQogICAgICAgICAgICAudGhlbigocnMpID0+IHsKICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgIGNvbnN0IHNvcnRlZFRhc2tzID0gcnMuZGF0YS50YXNrLnNvcnQoKGEsIGIpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGEuaXNUb3AgJiYgIWIuaXNUb3ApIHJldHVybiAtMTsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFhLmlzVG9wICYmIGIuaXNUb3ApIHJldHVybiAxOwogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3IERhdGUoYi5jcmVhdGVUaW1lKSAtIG5ldyBEYXRlKGEuY3JlYXRlVGltZSk7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5hbGxUYXNrcyA9IHNvcnRlZFRhc2tzOyAvLyDkv53lrZjmiYDmnInku7vliqHmlbDmja4KICAgICAgICAgICAgICAgICAgICB0aGlzLnRhc2tzID0gc29ydGVkVGFza3M7IC8vIOaYvuekuueahOS7u+WKoeaVsOaNrgogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJzLmRhdGEubXNnIHx8ICfojrflj5bku7vliqHliJfooajlpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bku7vliqHliJfooajlpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS7u+WKoeWIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlScpOwogICAgICAgICAgICB9KTsKICAgICAgICB9CiAgICB9LAogICAgY3JlYXRlZCgpIHsKICAgICAgICB0aGlzLm5ld0xpc3QoKTsKICAgICAgICB0aGlzLiRnZXQoInJvbGUiKQogICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMucm9sZXMgPSByZXMuZGF0YS5yb2xlCiAgICAgICAgfSkKICAgIH0sCgogICAgZmlsdGVyczogewogICAgICAgIGZvcm1hdERhdGUodGltZSkgewogICAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKHRpbWUpOwogICAgICAgICAgICByZXR1cm4gZm9ybWF0RGF0ZShkYXRlLCAneXl5eS1NTS1kZCBoaDptbScpOwogICAgICAgIH0KICAgIH0KCn0K"}, {"version": 3, "sources": ["Task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "Task.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <!-- 搜索栏 -->\n        <div class=\"center\">\n            <el-input placeholder=\"请输入搜索内容\"\n                      v-model=\"input\"\n                      class=\"input-with-select\"\n                      clearable\n                      @clear=\"clearSearch\"\n                      @keydown.enter.native=\"clickSearch\">\n                <el-select v-model=\"select\" slot=\"prepend\" placeholder=\"请选择搜索类型\" value=\"1\">\n                    <el-option label=\"任务标题\" value=\"1\"></el-option>\n                    <el-option label=\"发布人\" value=\"2\"></el-option>\n                    <el-option label=\"维修员\" value=\"3\"></el-option>\n                    <el-option label=\"任务状态\" value=\"4\"></el-option>\n                    <el-option label=\"维修类别\" value=\"5\"></el-option>\n                </el-select>\n                <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"clickSearch\"></el-button>\n            </el-input>\n        </div>\n        <div class=\"bottom\">\n            <el-table\n                    :data=\"paginatedTasks\"\n                    :resizable=\"false\"\n                    style=\"width: 100%\">\n                <el-table-column\n                        prop=\"publish.username\"\n                        label=\"发布人\"\n                        min-width=\"140\">\n                </el-table-column>\n                <el-table-column\n                        label=\"维修员\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.accept ? scope.row.accept.username : '暂无服务'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"reward\"\n                        label=\"任务金额\"\n                        min-width=\"110\">\n                </el-table-column>\n                <el-table-column\n                        label=\"任务所在类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.dept ? scope.row.dept.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"任务子类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.type ? scope.row.type.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <span v-if=\"scope.row.province\">\n                            {{scope.row.province}} {{scope.row.city}} {{scope.row.district}}\n                        </span>\n                        <span v-else>未设置</span>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"详细地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.address || '未设置'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"taskTitle\"\n                        label=\"标题\"\n                        min-width=\"150\">\n                </el-table-column>\n                <el-table-column\n                        label=\"发布时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.createTime ? transform(scope.row.createTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"服务时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.orderTime ? transform(scope.row.orderTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"balance\"\n                        label=\"完成时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.endTime ? transform(scope.row.endTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"state\"\n                        label=\"任务状态\"\n                        min-width=\"90\">\n                    <template slot-scope=\"scope\">\n                        <el-tag :type=\"getStateType(scope.row.state)\">\n                            {{ getStateText(scope.row.state) }}\n                        </el-tag>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"操作\"\n                        width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <el-button-group>\n                            <!-- <el-button\n                                    size=\"mini\"\n                                    :type=\"scope.row.isTop ? 'warning' : 'success'\"\n                                    @click=\"handleTopToggle(scope.row)\">\n                                {{ scope.row.isTop ? '取消置顶' : '置顶' }}\n                            </el-button> -->\n                            <el-button\n                                    size=\"mini\"\n                                    type=\"danger\"\n                                    @click=\"handleDelete(scope.row)\">\n                                删除\n                            </el-button>\n                        </el-button-group>\n                    </template>\n                </el-table-column>\n            </el-table>\n\n            <!-- 使用通用分页组件 -->\n            <common-pagination\n                :total=\"tasks.length\"\n                :current-page.sync=\"currentPage\"\n                :page-size.sync=\"pageSize\"\n                @pagination-change=\"handlePaginationChange\">\n            </common-pagination>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {formatDate} from '@/util/date';\n    import CommonPagination from '@/components/CommonPagination.vue';\n\n    export default {\n        name: \"Task\",\n        components: {\n            CommonPagination\n        },\n        data() {\n            return {\n                roles:[],\n                tasks: [],\n                allTasks: [],\n                ruleForm: {\n                    id: 0,\n                    state: true\n                },\n                options: [\n                    {label: \"正常\", value: \"0\"},\n                    {label: \"禁用\", value: \"1\"}\n                ],\n                input:\"\",\n                select: \"1\",\n                // 分页相关数据\n                currentPage: 1,\n                pageSize: 10\n            }\n        },\n        computed: {\n            paginatedTasks() {\n                const start = (this.currentPage - 1) * this.pageSize;\n                const end = start + this.pageSize;\n                return this.tasks.slice(start, end);\n            }\n        },\n        methods: {\n            // 处理分页变化\n            handlePaginationChange() {\n                // 如果需要在分页变化时执行其他操作，可以在这里添加\n            },\n\n            // 修改搜索方法\n            clickSearch() {\n                if (!this.input.trim()) {\n                    this.tasks = this.allTasks;\n                    this.currentPage = 1;\n                    return;\n                }\n\n                const searchText = this.input.trim().toLowerCase();\n                let filteredTasks = [];\n\n                switch (this.select) {\n                    case \"1\": // 任务标题\n                        filteredTasks = this.allTasks.filter(task =>\n                            task.taskTitle && task.taskTitle.toLowerCase().includes(searchText)\n                        );\n                        break;\n                    case \"2\": // 发布人\n                        filteredTasks = this.allTasks.filter(task =>\n                            (task.publish && task.publish.username && task.publish.username.toLowerCase().includes(searchText)) ||\n                            (task.publish && task.publish.studentId && task.publish.studentId.toLowerCase().includes(searchText))\n                        );\n                        break;\n                    case \"3\": // 维修员\n                        filteredTasks = this.allTasks.filter(task =>\n                            (task.accept && task.accept.username && task.accept.username.toLowerCase().includes(searchText)) ||\n                            (task.accept && task.accept.studentId && task.accept.studentId.toLowerCase().includes(searchText))\n                        );\n                        break;\n                    case \"4\": // 任务状态\n                        const statusMap = {\n                            '待接单': 0,\n                            '进行中': 1,\n                            '已完成': 2\n                        };\n                        const statusValue = statusMap[searchText] !== undefined ? statusMap[searchText] : null;\n                        if (statusValue !== null) {\n                            filteredTasks = this.allTasks.filter(task => task.state === statusValue);\n                        } else {\n                            filteredTasks = this.allTasks.filter(task => {\n                                const stateText = this.getStateText(task.state).toLowerCase();\n                                return stateText.includes(searchText);\n                            });\n                        }\n                        break;\n                    case \"5\": // 维修类别\n                        filteredTasks = this.allTasks.filter(task =>\n                            (task.dept && task.dept.name && task.dept.name.toLowerCase().includes(searchText)) ||\n                            (task.type && task.type.name && task.type.name.toLowerCase().includes(searchText))\n                        );\n                        break;\n                    default:\n                        filteredTasks = this.allTasks;\n                }\n\n                this.tasks = filteredTasks;\n                this.currentPage = 1;\n\n                if (filteredTasks.length === 0) {\n                    this.$message.info('未找到匹配的任务');\n                }\n            },\n\n            // 修改清空搜索方法，保持分页状态\n            clearSearch() {\n                this.input = '';\n                this.tasks = this.allTasks;\n                this.currentPage = 1; // 重置到第一页\n            },\n\n            getStateType(state) {\n                const types = {\n                    0: 'info',    // 待接单\n                    1: 'warning', // 进行中\n                    2: 'success'  // 已完成\n                }\n                return types[state] || 'info'\n            },\n            getStateText(state) {\n                const texts = {\n                    0: '待接单',\n                    1: '进行中',\n                    2: '已完成'\n                }\n                return texts[state] || '未知'\n            },\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n            handleDelete(row) {\n                this.$confirm('确认删除该任务?', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(`/task/${row.id}`).then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.newList();\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                }).catch(() => {})\n            },\n            handleTopToggle(row) {\n                const action = row.isTop ? 'cancelTop' : 'top';\n                const message = row.isTop ? '取消置顶' : '置顶';\n\n                this.$put(`/task/${action}/${row.id}`).then(res => {\n                    if (res.data.status) {\n                        this.$message.success(`${message}成功`);\n                        row.isTop = !row.isTop;\n                        this.newList();\n                    } else {\n                        this.$message.error(res.data.msg || `${message}失败`);\n                    }\n                }).catch(err => {\n                    console.error(`${message}失败:`, err);\n                    this.$message.error(`${message}失败，请稍后重试`);\n                });\n            },\n            newList() {\n                this.$get(\"/task\")\n                .then((rs) => {\n                    if (rs.data.status) {\n                        const sortedTasks = rs.data.task.sort((a, b) => {\n                            if (a.isTop && !b.isTop) return -1;\n                            if (!a.isTop && b.isTop) return 1;\n                            return new Date(b.createTime) - new Date(a.createTime);\n                        });\n                        this.allTasks = sortedTasks; // 保存所有任务数据\n                        this.tasks = sortedTasks; // 显示的任务数据\n                    } else {\n                        this.$message.error(rs.data.msg || '获取任务列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取任务列表失败:', err);\n                    this.$message.error('获取任务列表失败，请稍后重试');\n                });\n            }\n        },\n        created() {\n            this.newList();\n            this.$get(\"role\")\n            .then(res => {\n                this.roles = res.data.role\n            })\n        },\n\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n\n    }\n\n    .center {\n        width: 80%;\n        margin-bottom: 30px;\n        display: flex;\n        justify-content: center;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 200px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n\n\n    .form {\n        margin: 0 22px;\n    }\n\n    .el-button-group {\n        .el-button {\n            margin-left: 0;\n            margin-right: 0;\n\n            &:first-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.5);\n            }\n        }\n    }\n\n    // 底部容器样式\n    .bottom {\n        position: relative;\n        min-height: 500px;\n        display: flex;\n        flex-direction: column;\n        justify-content: space-between;\n    }\n</style>"]}]}