{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostEdit.vue", "mtime": 1745332269335}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AdminForumPostEdit.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"edit-post-container\">\n        <div class=\"page-header\">\n            <h2>编辑帖子</h2>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <el-form v-else :model=\"postForm\" :rules=\"rules\" ref=\"postForm\" label-width=\"80px\" class=\"post-form\">\n            <el-form-item label=\"标题\" prop=\"title\">\n                <el-input v-model=\"postForm.title\" placeholder=\"请输入帖子标题\"></el-input>\n            </el-form-item>\n            \n            <el-form-item label=\"内容\" prop=\"content\">\n                <el-input \n                    type=\"textarea\" \n                    v-model=\"postForm.content\" \n                    placeholder=\"请输入帖子内容\"\n                    :rows=\"10\"\n                ></el-input>\n            </el-form-item>\n            \n            <el-form-item>\n                <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">保存</el-button>\n                <el-button @click=\"cancel\">取消</el-button>\n            </el-form-item>\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostEdit',\n    data() {\n        return {\n            postForm: {\n                id: null,\n                title: '',\n                content: '',\n                authorId: null\n            },\n            rules: {\n                title: [\n                    { required: true, message: '请输入帖子标题', trigger: 'blur' },\n                    { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }\n                ],\n                content: [\n                    { required: true, message: '请输入帖子内容', trigger: 'blur' },\n                    { min: 10, max: 5000, message: '内容长度在10到5000个字符之间', trigger: 'blur' }\n                ]\n            },\n            loading: true,\n            submitting: false,\n            adminId: null\n        };\n    },\n    created() {\n        // 检查管理员登录状态\n        const admin = sessionStorage.getItem('admin');\n        if (!admin) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.adminId = JSON.parse(admin).id;\n        this.fetchPostDetail();\n    },\n    methods: {\n        fetchPostDetail() {\n            this.loading = true;\n            const postId = this.$route.params.id;\n\n            this.$get(`/forum/post/${postId}`, { userId: this.adminId })\n                .then(res => {\n                    if (res.data.status) {\n                        const post = res.data.post;\n                        this.postForm = {\n                            id: post.id,\n                            title: post.title,\n                            content: post.content,\n                            authorId: post.authorId\n                        };\n                        \n                        // 检查是否是自己的帖子\n                        if (post.authorId !== this.adminId) {\n                            this.$message.warning('您只能编辑自己发布的帖子');\n                            this.$router.push(`/admin/home/<USER>/post/${postId}`);\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子详情失败');\n                        this.$router.push('/admin/home/<USER>/posts');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子详情失败:', err);\n                    this.$message.error('获取帖子详情失败');\n                    this.$router.push('/admin/home/<USER>/posts');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        submitForm() {\n            this.$refs.postForm.validate(valid => {\n                if (valid) {\n                    this.submitting = true;\n                    \n                    // 确保使用正确的作者ID\n                    this.postForm.authorId = this.adminId;\n                    \n                    this.$put('/forum/post', this.postForm)\n                        .then(res => {\n                            if (res.data.status) {\n                                this.$message.success('更新成功');\n                                this.$router.push(`/admin/home/<USER>/post/${this.postForm.id}`);\n                            } else {\n                                this.$message.error(res.data.msg || '更新失败');\n                            }\n                        })\n                        .catch(err => {\n                            console.error('更新帖子失败:', err);\n                            this.$message.error('更新帖子失败');\n                        })\n                        .finally(() => {\n                            this.submitting = false;\n                        });\n                }\n            });\n        },\n        cancel() {\n            this.$router.go(-1);\n        }\n    }\n};\n</script>\n\n<style scoped>\n.edit-post-container {\n    padding: 20px;\n}\n\n.page-header {\n    margin-bottom: 20px;\n}\n\n.post-form {\n    max-width: 800px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}