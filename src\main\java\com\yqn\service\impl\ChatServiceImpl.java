package com.yqn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yqn.mapper.ChatMessageMapper;
import com.yqn.mapper.TaskMapper;
import com.yqn.mapper.UserMapper;
import com.yqn.pojo.ChatMessage;
import com.yqn.pojo.Task;
import com.yqn.pojo.User;
import com.yqn.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 聊天服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChatServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessage> implements ChatService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private TaskMapper taskMapper;

    @Override
    public boolean saveMessage(ChatMessage chatMessage) {
        try {
            // 设置默认值
            if (chatMessage.getSendTime() == null) {
                chatMessage.setSendTime(new Date());
            }
            if (chatMessage.getIsRead() == null) {
                chatMessage.setIsRead(0);
            }
            if (chatMessage.getMessageType() == null) {
                chatMessage.setMessageType("text");
            }
            
            return save(chatMessage);
        } catch (Exception e) {
            log.error("保存聊天消息失败", e);
            return false;
        }
    }

    @Override
    public List<ChatMessage> getChatHistory(Long taskId, Long userId) {
        try {
            // 首先获取任务信息，确定聊天的参与者
            Task task = taskMapper.selectById(taskId);
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return new ArrayList<>();
            }
            
            // 查询条件：任务ID匹配，且用户是发送者或接收者
            QueryWrapper<ChatMessage> wrapper = new QueryWrapper<>();
            wrapper.eq("task_id", taskId)
                   .and(w -> w.eq("sender_id", userId).or().eq("receiver_id", userId))
                   .orderByAsc("send_time");

            List<ChatMessage> messages = list(wrapper);
            
            // 填充用户信息
            for (ChatMessage message : messages) {
                if (message.getSenderId() != null) {
                    User sender = userMapper.selectById(message.getSenderId());
                    message.setSender(sender);
                }
                if (message.getReceiverId() != null) {
                    User receiver = userMapper.selectById(message.getReceiverId());
                    message.setReceiver(receiver);
                }
                message.setTask(task);
            }
            
            return messages;
        } catch (Exception e) {
            log.error("获取聊天历史记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean markMessagesAsRead(List<Long> messageIds) {
        try {
            if (messageIds == null || messageIds.isEmpty()) {
                return true;
            }
            
            UpdateWrapper<ChatMessage> wrapper = new UpdateWrapper<>();
            wrapper.in("id", messageIds)
                   .set("is_read", 1);
            
            return update(wrapper);
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
            return false;
        }
    }

    @Override
    public int getUnreadCount(Long userId) {
        try {
            QueryWrapper<ChatMessage> wrapper = new QueryWrapper<>();
            wrapper.eq("receiver_id", userId)
                   .eq("is_read", 0);
            
            return Math.toIntExact(count(wrapper));
        } catch (Exception e) {
            log.error("获取未读消息数量失败", e);
            return 0;
        }
    }

    @Override
    public List<Long> getChatParticipants(Long taskId) {
        try {
            Task task = taskMapper.selectById(taskId);
            if (task == null) {
                return new ArrayList<>();
            }
            
            List<Long> participants = new ArrayList<>();
            if (task.getPublishId() != null) {
                participants.add(task.getPublishId());
            }
            if (task.getAcceptId() != null) {
                participants.add(task.getAcceptId());
            }
            
            return participants.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取聊天参与者失败", e);
            return new ArrayList<>();
        }
    }
}
