{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accepted.vue?vue&type=style&index=0&id=2de4f9ad&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Accepted.vue", "mtime": 1748722900827}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNvbnRlbnQgewogICAgYmFja2dyb3VuZDogI0ZGZjsKICAgIG1hcmdpbjogMCAxNXB4OwogICAgcGFkZGluZzogMTVweDsKCiAgICAuYm94X2NlbnRlciB0YWJsZSB7CiAgICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1OwogICAgfQoKICAgIC5ib3hfY2VudGVyIHRoLCB0ZCB7CiAgICAgICAgcGFkZGluZzogMTJweCAxNXB4OwogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1OwogICAgfQoKICAgIC5ib3hfY2VudGVyIHRoIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOwogICAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgfQoKICAgIC5zdGF0dXMtdGFicyB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAgICAgL2RlZXAvIC5lbC10YWJzX19oZWFkZXIgewogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgICAgIH0KCiAgICAgICAgL2RlZXAvIC5lbC10YWJzX19pdGVtIHsKICAgICAgICAgICAgaGVpZ2h0OiA0MHB4OwogICAgICAgICAgICBsaW5lLWhlaWdodDogNDBweDsKICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgICBjb2xvcjogIzYwNjI2NjsKCiAgICAgICAgICAgICYuaXMtYWN0aXZlIHsKICAgICAgICAgICAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgIH0KICAgICAgICB9CiAgICB9Cn0KCi8qIOivhOS7t+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["Accepted.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuXA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Accepted.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>已接受任务</span>\n            </div>\n\n            <!-- 状态分类标签 -->\n            <div class=\"status-tabs\">\n                <el-tabs v-model=\"activeStatus\" type=\"card\">\n                    <el-tab-pane\n                        v-for=\"group in tasksByStatus\"\n                        :key=\"group.status\"\n                        :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                        :name=\"group.status.toString()\"\n                    >\n                        <el-card\n                            class=\"box-card\"\n                            v-for=\"item in group.tasks\"\n                            :key=\"item.id\"\n                            style=\"margin-top: 20px\"\n                        >\n                <div slot=\"header\" class=\"clearfix\"\n                     style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span style=\"display: flex;align-items: center\">\n                            <el-tag v-if=\"item\" :type=\"item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')\"\n                                    style=\"margin-right: 5px\">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>\n                            任务名称：{{item ? item.taskTitle : ''}}\n                        </span>\n                    <!--                    <el-button style=\"float: right; padding: 3px 0\" type=\"text\">查看接受人信息</el-button>-->\n                    <div>\n                        <!-- 已完成任务显示查看评价按钮 -->\n                        <el-button\n                            style=\"float: right; padding: 3px 0\"\n                            type=\"text\"\n                            v-show=\"item && item.state == 2\"\n                            @click=\"viewRemark(item)\">查看评价\n                        </el-button>\n\n                        <!-- 在线交流按钮 - 服务中的任务显示 -->\n                        <el-button v-show=\"item && item.state == 1\"\n                        style=\"float: right; padding: 3px 0; margin-right: 10px\" type=\"text\" @click=\"openChat(item)\">\n                            <i class=\"el-icon-chat-dot-round\"></i> 在线交流\n                        </el-button>\n\n                        <!-- 未完成任务显示取消任务按钮 -->\n                        <el-popconfirm title=\"确定取消任务吗\" @confirm=\"del(item.id)\">\n                            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" slot=\"reference\"\n                                       v-show=\"item && item.state != 2\">取消任务\n                            </el-button>\n                        </el-popconfirm>\n                    </div>\n                </div>\n                <div>\n                    任务内容：{{item ? item.taskContext : ''}}\n                </div>\n                <el-collapse style=\"margin-top: 20px\" v-model=\"activeNames\">\n                    <el-collapse-item title=\"用户信息\" name=\"1\">\n                        <el-card class=\"box-card\" v-if=\"item && item.publish\">\n                            <div slot=\"header\" class=\"clearfix\">\n                                <span>用户姓名：{{item.publish.username || '未知用户'}}</span>\n                                <!-- <el-button style=\"float: right; padding: 3px 0\" type=\"text\">投诉</el-button> -->\n                            </div>\n\n                            <div class=\"box_center\">\n                                <table cellspacing=\"0\">\n                                    <tr>\n                                        <th>电话</th>\n                                        <th>角色</th>\n                                        <th>类别</th>\n                                        <th>子类别</th>\n                                        <th>任务金额</th>\n                                        <th>接受任务时间</th>\n                                    </tr>\n                                    <tr align=\"center\">\n                                        <td>{{item.publish.phone || '无'}}</td>\n                                        <td>{{item.publish.role ? item.publish.role.name : '无'}}</td>\n                                        <td>{{item.dept ? item.dept.name : '无'}}</td>\n                                        <td>{{item.type ? item.type.name : '无'}}</td>\n                                        <td><i class=\"el-icon-money\" style=\"color: red;\">{{item.reward || 0}}元</i></td>\n                                        <td>\n                                            <!-- 添加调试信息 -->\n                                            <span v-if=\"item.orderTime\">{{item.orderTime | formatDate}}</span>\n                                            <span v-else>无时间数据</span>\n                                        </td>\n                                    </tr>\n                                </table>\n                            </div>\n                        </el-card>\n                        <el-card class=\"box-card\" v-else>\n                            <div class=\"box_center\">\n                                <p>暂无发布人信息</p>\n                            </div>\n                        </el-card>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"查看钱款\" name=\"2\" v-if=\"item && item.state == 2\">\n                        <el-card class=\"box-card\">\n                            <div>\n                                <p>实收款：<i class=\"el-icon-money\" style=\"color: red;\">{{item.reward || 0}}元</i></p>\n                                <p>到款时间：\n                                    <span v-if=\"item.endTime\">{{item.endTime | formatDate}}</span>\n                                    <span v-else>无</span>\n                                </p>\n                            </div>\n                        </el-card>\n                    </el-collapse-item>\n                </el-collapse>\n                        </el-card>\n\n                        <!-- 当前状态下没有任务时显示 -->\n                        <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                            <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无已接受任务</span>\n                        </div>\n                    </el-tab-pane>\n                </el-tabs>\n            </div>\n\n            <!-- 没有任何任务时显示 -->\n            <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                <span><i class=\"el-icon-refresh-right\"></i>暂无已接受任务</span>\n            </div>\n        </el-card>\n\n        <!-- 评价对话框 -->\n        <el-dialog\n            :title=\"currentTask ? '任务「' + currentTask.taskTitle + '」的评价' : '任务评价'\"\n            :visible.sync=\"remarkDialogVisible\"\n            width=\"550px\"\n            center>\n            <div v-if=\"currentRemark\">\n                <div class=\"remark-info\">\n                    <div class=\"task-info\" v-if=\"currentTask\">\n                        <div class=\"task-header\">\n                            <i class=\"el-icon-info-circle\"></i> 任务信息\n                        </div>\n                        <div class=\"task-details\">\n                            <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>\n                            <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>\n                            <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>\n                        </div>\n                    </div>\n\n                    <div class=\"star-rating\">\n                        <span class=\"rating-label\">评分：</span>\n                        <el-rate\n                            v-model=\"currentRemark.star\"\n                            disabled\n                            show-score\n                            text-color=\"#ff9900\">\n                        </el-rate>\n                    </div>\n                    <div class=\"remark-content\">\n                        <span class=\"content-label\">评价内容：</span>\n                        <p>{{currentRemark.remark}}</p>\n                    </div>\n                    <div class=\"remark-time\" v-if=\"currentRemark.createTime\">\n                        <span>评价时间：{{currentRemark.createTime | formatDate}}</span>\n                    </div>\n                </div>\n            </div>\n            <div v-else class=\"no-remark\">\n                <i class=\"el-icon-warning-outline\"></i>\n                <p>用户尚未评价此任务</p>\n                <div class=\"task-info\" v-if=\"currentTask\">\n                    <div class=\"task-header\">\n                        <i class=\"el-icon-info-circle\"></i> 任务信息\n                    </div>\n                    <div class=\"task-details\">\n                        <p><span>发布者：</span>{{currentTask.publish ? currentTask.publish.username : '未知'}}</p>\n                        <p><span>任务类别：</span>{{currentTask.dept ? currentTask.dept.name : '未知'}} - {{currentTask.type ? currentTask.type.name : '未知'}}</p>\n                        <p><span>完成时间：</span>{{currentTask.endTime | formatDate}}</p>\n                    </div>\n                </div>\n            </div>\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"remarkDialogVisible = false\">关 闭</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 聊天组件 -->\n        <Chat\n            :visible.sync=\"chatVisible\"\n            :task=\"currentChatTask\"\n            :current-user-id=\"user.id\"\n            v-if=\"currentChatTask\" />\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\";\n    import Chat from '@/components/Chat.vue';\n    // 不再使用导入的formatDate函数，因为我们直接在过滤器中实现了日期格式化\n\n    export default {\n        name: \"Accepted\",\n        components: {\n            Chat\n        },\n        data() {\n            return {\n                activeNames: ['1'],\n                tasks: [],\n                // 当前选中的状态\n                activeStatus: '-1',\n                // drawer:false,\n\n                // 评价相关\n                remarkDialogVisible: false,\n                currentRemark: null,\n                currentTask: null,\n\n                // 聊天相关\n                chatVisible: false,\n                currentChatTask: null\n            };\n        },\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射 - 已接受任务只有服务中和已完成两种状态\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    '1': { name: '服务中', tasks: [] },\n                    '2': { name: '已完成', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.tasks;\n\n                // 按状态分组\n                this.tasks.forEach(task => {\n                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '1';\n                    if (statusMap[state]) {\n                        statusMap[state].tasks.push(task);\n                    }\n                });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.newList()\n        },\n        methods: {\n            del(id) {\n                this.$put('/task/takerCancel/' + id)\n                .then(() => {\n                    this.$notifyMsg('成功', '取消任务成功', 'success')\n                    this.newList()\n                })\n            },\n            newList() {\n                this.$get(\"/task/accepted\", {id: this.user.id})\n                .then(res => {\n                    // 确保每个任务对象都有必要的属性\n                    if (res.data.task && Array.isArray(res.data.task)) {\n                        // 过滤掉状态为0（待接单）的任务，因为已接受任务不应该包含待接单状态\n                        this.tasks = res.data.task\n                            .filter(task => task.state !== 0)\n                            .map(task => {\n                                // 确保publish对象存在\n                                if (!task.publish) {\n                                    task.publish = {};\n                                }\n\n                                // 确保publish.role对象存在\n                                if (!task.publish.role) {\n                                    task.publish.role = { name: '未知' };\n                                }\n\n                                // 确保publish.dept对象存在\n                                if (!task.publish.dept) {\n                                    task.publish.dept = { name: '未知' };\n                                }\n\n                                // 确保publish.type对象存在\n                                if (!task.publish.type) {\n                                    task.publish.type = { name: '未知' };\n                                }\n\n                                // 打印orderTime以便调试\n                                console.log('任务ID:', task.id, '接受时间:', task.orderTime, '状态:', task.state);\n\n                                return task;\n                            });\n                    } else {\n                        this.tasks = [];\n                    }\n                    console.log('获取到的任务列表:', this.tasks);\n                })\n                .catch(error => {\n                    console.error('获取任务列表失败:', error);\n                    this.tasks = [];\n                });\n            },\n\n            acceptTask(id) {\n                this.$put('task/takerAccept', { id: id }) // 仅传递任务ID\n                .then((res) => {\n                    if (res.data.status) {\n                        this.$msg(res.data.msg, \"success\");\n                    } else {\n                        console.error('错误详情:', res.data.msg);\n                    }\n                });\n            },\n\n            /**\n             * 查看任务评价\n             * @param {Object} task 任务对象\n             */\n            viewRemark(task) {\n                // 直接显示对话框，不需要确认\n                this.currentTask = task;\n                this.remarkDialogVisible = true;\n\n                // 根据任务ID查询评价信息\n                this.$get('/student/remark/list', { taskId: task.id })\n                .then(res => {\n                    console.log('评价查询结果:', res);\n                    if (res.data && res.data.rows && res.data.rows.length > 0) {\n                        // 找到评价\n                        this.currentRemark = res.data.rows[0];\n                    } else {\n                        // 没有评价\n                        this.currentRemark = null;\n                    }\n                })\n                .catch(error => {\n                    console.error('查询评价失败:', error);\n                    this.currentRemark = null;\n                    this.$message.error('查询评价信息失败，请稍后重试');\n                });\n            },\n\n            /**\n             * 打开在线交流\n             * @param {Object} task 任务对象\n             */\n            openChat(task) {\n                this.currentChatTask = task;\n                this.chatVisible = true;\n            }\n        },\n\n        filters: {\n            formatDate(time) {\n                if (!time) return '无';\n                try {\n                    // 直接使用字符串格式化\n                    const date = new Date(time);\n                    const year = date.getFullYear();\n                    const month = String(date.getMonth() + 1).padStart(2, '0');\n                    const day = String(date.getDate()).padStart(2, '0');\n                    const hours = String(date.getHours()).padStart(2, '0');\n                    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n                    return `${year}-${month}-${day} ${hours}:${minutes}`;\n                } catch (e) {\n                    console.error('日期格式化错误:', e, time);\n                    return '格式错误';\n                }\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n\n        .box_center table {\n            margin: 0 auto;\n            width: 100%;\n            border-collapse: collapse;\n            border: 1px solid #ebeef5;\n        }\n\n        .box_center th, td {\n            padding: 12px 15px;\n            text-align: center;\n            border: 1px solid #ebeef5;\n        }\n\n        .box_center th {\n            background-color: #F5F7FA;\n            color: #606266;\n            font-weight: bold;\n        }\n\n        .status-tabs {\n            margin-bottom: 20px;\n\n            /deep/ .el-tabs__header {\n                margin-bottom: 15px;\n            }\n\n            /deep/ .el-tabs__item {\n                height: 40px;\n                line-height: 40px;\n                font-size: 14px;\n                color: #606266;\n\n                &.is-active {\n                    color: #409EFF;\n                    font-weight: bold;\n                }\n            }\n        }\n    }\n\n    /* 评价对话框样式 */\n    .remark-info {\n        padding: 15px;\n\n        .task-info {\n            margin-bottom: 20px;\n            border: 1px solid #ebeef5;\n            border-radius: 4px;\n            overflow: hidden;\n\n            .task-header {\n                background-color: #f5f7fa;\n                padding: 10px 15px;\n                font-weight: bold;\n                color: #606266;\n                border-bottom: 1px solid #ebeef5;\n\n                i {\n                    color: #409EFF;\n                    margin-right: 5px;\n                }\n            }\n\n            .task-details {\n                padding: 15px;\n\n                p {\n                    margin: 5px 0;\n                    line-height: 1.6;\n\n                    span {\n                        font-weight: bold;\n                        margin-right: 5px;\n                        color: #606266;\n                    }\n                }\n            }\n        }\n\n        .star-rating {\n            margin-bottom: 15px;\n            display: flex;\n            align-items: center;\n\n            .rating-label {\n                font-weight: bold;\n                margin-right: 10px;\n                min-width: 70px;\n                color: #606266;\n            }\n        }\n\n        .remark-content {\n            margin-bottom: 15px;\n\n            .content-label {\n                font-weight: bold;\n                display: block;\n                margin-bottom: 10px;\n                color: #606266;\n            }\n\n            p {\n                background-color: #f5f7fa;\n                padding: 15px;\n                border-radius: 4px;\n                margin: 0;\n                line-height: 1.6;\n            }\n        }\n\n        .remark-time {\n            text-align: right;\n            color: #909399;\n            font-size: 12px;\n            margin-top: 10px;\n        }\n    }\n\n    .no-remark {\n        text-align: center;\n        padding: 20px 0;\n        color: #909399;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin-bottom: 20px;\n        }\n\n        .task-info {\n            margin-top: 20px;\n            text-align: left;\n            border: 1px solid #ebeef5;\n            border-radius: 4px;\n            overflow: hidden;\n\n            .task-header {\n                background-color: #f5f7fa;\n                padding: 10px 15px;\n                font-weight: bold;\n                color: #606266;\n                border-bottom: 1px solid #ebeef5;\n\n                i {\n                    font-size: 14px;\n                    display: inline;\n                    color: #409EFF;\n                    margin-right: 5px;\n                    margin-bottom: 0;\n                }\n            }\n\n            .task-details {\n                padding: 15px;\n\n                p {\n                    margin: 5px 0;\n                    line-height: 1.6;\n                    font-size: 14px;\n\n                    span {\n                        font-weight: bold;\n                        margin-right: 5px;\n                        color: #606266;\n                    }\n                }\n            }\n        }\n    }\n</style>"]}]}