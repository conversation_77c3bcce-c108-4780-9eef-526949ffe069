<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="maintain_help">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.45">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root|localhost|ALTER|G
|root||root|localhost|ALTER ROUTINE|G
|root||root|localhost|CREATE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE USER|G
|root||root|localhost|CREATE VIEW|G
|root||root|localhost|DELETE|G
|root||root|localhost|DROP|G
|root||root|localhost|EVENT|G
|root||root|localhost|EXECUTE|G
|root||root|localhost|FILE|G
|root||root|localhost|INDEX|G
|root||root|localhost|INSERT|G
|root||root|localhost|LOCK TABLES|G
|root||root|localhost|PROCESS|G
|root||root|localhost|REFERENCES|G
|root||root|localhost|RELOAD|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root|localhost|SELECT|G
|root||root|localhost|SHOW DATABASES|G
|root||root|localhost|SHOW VIEW|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root|localhost|SUPER|G
|root||root|localhost|TRIGGER|G
|root||root|localhost|UPDATE|G
|root||root|localhost|grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>5.7.37</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="224" parent="1" name="haiyang">
      <Current>1</Current>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="maintain_help">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="mybatis_plus">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="mysql">
      <Grants>user|||mysql.session|localhost|SELECT|G</Grants>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="obwiki">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="231" parent="1" name="psc">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="roos">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="233" parent="1" name="sports">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="234" parent="1" name="student">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="235" parent="1" name="studentmanager">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="236" parent="1" name="sys">
      <Grants>sys_config|||mysql.sys|localhost|SELECT|G</Grants>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <user id="237" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="238" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="239" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <table id="240" parent="226" name="admin">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="241" parent="226" name="chat_message">
      <Comment>聊天消息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="242" parent="226" name="dept">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="243" parent="226" name="forum_comment">
      <Comment>评论表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="244" parent="226" name="forum_like">
      <Comment>点赞表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="245" parent="226" name="forum_notification">
      <Comment>通知表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="246" parent="226" name="forum_post">
      <Comment>论坛帖子表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="247" parent="226" name="forum_post_backup">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="248" parent="226" name="notice">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="249" parent="226" name="notification">
      <Comment>统一通知表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="250" parent="226" name="recharge_record">
      <Comment>充值记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="251" parent="226" name="role">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="252" parent="226" name="sxc_remark">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="253" parent="226" name="task">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="254" parent="226" name="type">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="255" parent="226" name="user">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <column id="256" parent="240" name="id">
      <AutoIncrement>17</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="257" parent="240" name="account">
      <Comment>账号</Comment>
      <DasType>varchar(16)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="258" parent="240" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="259" parent="240" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(16)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="260" parent="240" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="261" parent="240" name="state">
      <Comment>状态</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="262" parent="240" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="263" parent="240" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="264" parent="241" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>消息ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="265" parent="241" name="task_id">
      <Comment>任务ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="266" parent="241" name="sender_id">
      <Comment>发送者ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="267" parent="241" name="receiver_id">
      <Comment>接收者ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="268" parent="241" name="content">
      <Comment>消息内容</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="269" parent="241" name="type">
      <Comment>消息类型：0-系统消息，1-用户消息，2-维修员消息</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="270" parent="241" name="is_read">
      <Comment>是否已读：0-未读，1-已读</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="271" parent="241" name="send_time">
      <Comment>发送时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="272" parent="241" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="273" parent="241" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="274" parent="241" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="275" parent="241" name="idx_task_id">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="276" parent="241" name="idx_sender_id">
      <ColNames>sender_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="277" parent="241" name="idx_receiver_id">
      <ColNames>receiver_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="278" parent="241" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="279" parent="242" name="id">
      <AutoIncrement>16</AutoIncrement>
      <Comment>系别id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="280" parent="242" name="name">
      <Comment>系别名称</Comment>
      <DasType>varchar(18)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="281" parent="242" name="role_id">
      <Comment>角色id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <foreign-key id="282" parent="242" name="FK_DEPT_ROLE">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <index id="283" parent="242" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="284" parent="242" name="FK_DEPT_ROLE">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="285" parent="242" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="286" parent="243" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>评论ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="287" parent="243" name="post_id">
      <Comment>帖子ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="288" parent="243" name="content">
      <Comment>评论内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="289" parent="243" name="author_id">
      <Comment>评论者ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="290" parent="243" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="291" parent="243" name="like_count">
      <Comment>点赞数</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="292" parent="243" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="293" parent="243" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="294" parent="243" name="idx_author_id">
      <ColNames>author_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="295" parent="243" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="296" parent="244" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>点赞ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="297" parent="244" name="type">
      <Comment>点赞类型：1-帖子，2-评论</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="298" parent="244" name="target_id">
      <Comment>目标ID（帖子ID或评论ID）</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="299" parent="244" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="300" parent="244" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="301" parent="244" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="302" parent="244" name="uk_type_target_user">
      <ColNames>type
target_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="303" parent="244" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="304" parent="244" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="305" parent="244" name="uk_type_target_user">
      <UnderlyingIndexName>uk_type_target_user</UnderlyingIndexName>
    </key>
    <column id="306" parent="245" name="id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>通知ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="307" parent="245" name="user_id">
      <Comment>接收用户ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="308" parent="245" name="type">
      <Comment>通知类型：1-点赞，2-评论，3-删除，4-审核未通过</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="309" parent="245" name="content">
      <Comment>通知内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="310" parent="245" name="related_id">
      <Comment>相关ID（帖子ID或评论ID）</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="311" parent="245" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="312" parent="245" name="is_read">
      <Comment>是否已读：0-未读，1-已读</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="313" parent="245" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="314" parent="245" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="315" parent="245" name="idx_is_read">
      <ColNames>is_read</ColNames>
      <Type>btree</Type>
    </index>
    <key id="316" parent="245" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="317" parent="246" name="id">
      <AutoIncrement>12</AutoIncrement>
      <Comment>帖子ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="318" parent="246" name="title">
      <Comment>帖子标题</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="319" parent="246" name="content">
      <Comment>帖子内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="320" parent="246" name="author_id">
      <Comment>作者ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="321" parent="246" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="322" parent="246" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="323" parent="246" name="status">
      <Comment>状态：0-待审核，1-已发布，2-已拒绝</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="324" parent="246" name="like_count">
      <Comment>点赞数</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="325" parent="246" name="comment_count">
      <Comment>评论数</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="326" parent="246" name="is_top">
      <Comment>是否置顶：0-否，1-是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <index id="327" parent="246" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="328" parent="246" name="idx_author_id">
      <ColNames>author_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="329" parent="246" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="330" parent="246" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="331" parent="247" name="id">
      <DasType>bigint(20)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="332" parent="247" name="user_id">
      <Comment>发帖用户ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="333" parent="247" name="title">
      <Comment>帖子标题</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="334" parent="247" name="content">
      <Comment>帖子内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="335" parent="247" name="author_id">
      <Comment>作者ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="336" parent="247" name="create_time">
      <Comment>发帖时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="337" parent="247" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="338" parent="247" name="likes">
      <Comment>点赞数</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="339" parent="247" name="is_top">
      <Comment>是否置顶</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="340" parent="247" name="is_admin">
      <Comment>是否管理员发帖</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="341" parent="248" name="notice_id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="342" parent="248" name="notice_title">
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="343" parent="248" name="notice_content">
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="344" parent="248" name="create_by">
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="345" parent="248" name="create_time">
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="346" parent="248" name="remark">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="347" parent="248" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="348" parent="248" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="349" parent="249" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>通知ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="350" parent="249" name="user_id">
      <Comment>接收用户ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="351" parent="249" name="type">
      <Comment>通知类型：1-点赞，2-评论，3-删除，4-审核未通过，5-任务接受，6-任务完成，7-任务评价</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="352" parent="249" name="content">
      <Comment>通知内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="353" parent="249" name="related_id">
      <Comment>相关ID（帖子ID或任务ID）</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="354" parent="249" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="355" parent="249" name="is_read">
      <Comment>是否已读：0-未读，1-已读</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="356" parent="249" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="357" parent="249" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="358" parent="249" name="idx_is_read">
      <ColNames>is_read</ColNames>
      <Type>btree</Type>
    </index>
    <key id="359" parent="249" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="360" parent="250" name="id">
      <AutoIncrement>40</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="361" parent="250" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="362" parent="250" name="order_no">
      <Comment>订单号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="363" parent="250" name="trade_no">
      <Comment>支付宝交易号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="364" parent="250" name="amount">
      <Comment>充值金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="365" parent="250" name="status">
      <Comment>支付状态：0-待支付，1-支付成功，2-支付失败，3-已取消</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="366" parent="250" name="payment_method">
      <Comment>支付方式：alipay-支付宝</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;alipay&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="367" parent="250" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="368" parent="250" name="pay_time">
      <Comment>支付时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="369" parent="250" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <foreign-key id="370" parent="250" name="fk_recharge_user">
      <ColNames>user_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="371" parent="250" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="372" parent="250" name="uk_order_no">
      <ColNames>order_no</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="373" parent="250" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="374" parent="250" name="idx_trade_no">
      <ColNames>trade_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="375" parent="250" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="376" parent="250" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="377" parent="250" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="378" parent="250" name="uk_order_no">
      <UnderlyingIndexName>uk_order_no</UnderlyingIndexName>
    </key>
    <column id="379" parent="251" name="id">
      <AutoIncrement>15</AutoIncrement>
      <Comment>角色id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="380" parent="251" name="name">
      <Comment>角色名称</Comment>
      <DasType>varchar(18)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="381" parent="251" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="382" parent="251" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="383" parent="252" name="id">
      <AutoIncrement>9</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="384" parent="252" name="star">
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="385" parent="252" name="remark">
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="386" parent="252" name="task_id">
      <DasType>int(11)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="387" parent="252" name="accept_id">
      <DasType>int(11)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="388" parent="252" name="publish_id">
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="389" parent="252" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="390" parent="252" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="391" parent="253" name="id">
      <AutoIncrement>54</AutoIncrement>
      <Comment>任务id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="392" parent="253" name="publish_user_id">
      <Comment>用户发布id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="393" parent="253" name="accept_user_id">
      <Comment>接受任务用户id</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="394" parent="253" name="user_role_id">
      <Comment>维修员角色id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="395" parent="253" name="reward">
      <Comment>任务奖励</Comment>
      <DasType>double|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="396" parent="253" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="397" parent="253" name="order_time">
      <Comment>接单时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="398" parent="253" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="399" parent="253" name="task_title">
      <Comment>任务标题</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="400" parent="253" name="task_context">
      <Comment>任务内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="401" parent="253" name="state">
      <Comment>状态</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="402" parent="253" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="403" parent="253" name="city">
      <Comment>城市</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="404" parent="253" name="district">
      <Comment>区县</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="405" parent="253" name="address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="406" parent="253" name="dept_id">
      <Comment>维修类别ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="407" parent="253" name="type_id">
      <Comment>维修子类别ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="408" parent="253" name="auto_assign">
      <Comment>是否自动派单：0-否，1-是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="409" parent="253" name="assign_priority">
      <Comment>派单优先级：1-地址优先，2-评分优先</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>19</Position>
    </column>
    <foreign-key id="410" parent="253" name="FK_TASK_PUBLISH_USER">
      <ColNames>publish_user_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="411" parent="253" name="FK_TASK_ACCEPT_USER">
      <ColNames>accept_user_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="412" parent="253" name="FK_TASK_USER_ROLE">
      <ColNames>user_role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <foreign-key id="413" parent="253" name="FK_TASK_TYPE">
      <ColNames>type_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>type</RefTableName>
    </foreign-key>
    <index id="414" parent="253" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="415" parent="253" name="FK_TASK_PUBLISH_USER">
      <ColNames>publish_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="416" parent="253" name="FK_TASK_ACCEPT_USER">
      <ColNames>accept_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="417" parent="253" name="FK_TASK_USER_ROLE">
      <ColNames>user_role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="418" parent="253" name="FK_TASK_TYPE">
      <ColNames>type_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="419" parent="253" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="420" parent="254" name="id">
      <AutoIncrement>18</AutoIncrement>
      <Comment>班级id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="421" parent="254" name="name">
      <Comment>班级名</Comment>
      <DasType>varchar(18)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="422" parent="254" name="role_id">
      <Comment>角色id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="423" parent="254" name="dept_id">
      <Comment>系别id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <foreign-key id="424" parent="254" name="FK_TYPE_ROLE">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <foreign-key id="425" parent="254" name="FK_TYPE_DEPT">
      <ColNames>dept_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>dept</RefTableName>
    </foreign-key>
    <index id="426" parent="254" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="427" parent="254" name="FK_TYPE_ROLE">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="428" parent="254" name="FK_TYPE_DEPT">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="429" parent="254" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="430" parent="255" name="id">
      <AutoIncrement>19</AutoIncrement>
      <Comment>用户id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="431" parent="255" name="student_id">
      <Comment>学号</Comment>
      <DasType>varchar(16)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="432" parent="255" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="433" parent="255" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(11)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="434" parent="255" name="role_id">
      <Comment>角色id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="435" parent="255" name="dept_id">
      <Comment>系别id</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="436" parent="255" name="type_id">
      <Comment>班级id</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="437" parent="255" name="sex">
      <Comment>性别</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="438" parent="255" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="439" parent="255" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="440" parent="255" name="balance">
      <Comment>余额</Comment>
      <DasType>double|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="441" parent="255" name="state">
      <Comment>状态</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="442" parent="255" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="443" parent="255" name="city">
      <Comment>城市</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="444" parent="255" name="district">
      <Comment>区县</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="445" parent="255" name="address">
      <Comment>详细地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>16</Position>
    </column>
    <foreign-key id="446" parent="255" name="FK_USER_ROLE">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <foreign-key id="447" parent="255" name="FK_USER_DEPT">
      <ColNames>dept_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>dept</RefTableName>
    </foreign-key>
    <foreign-key id="448" parent="255" name="FK_USER_TYPE">
      <ColNames>type_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>type</RefTableName>
    </foreign-key>
    <index id="449" parent="255" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="450" parent="255" name="FK_USER_ROLE">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="451" parent="255" name="FK_USER_DEPT">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="452" parent="255" name="FK_USER_TYPE">
      <ColNames>type_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="453" parent="255" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>