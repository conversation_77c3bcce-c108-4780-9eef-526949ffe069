{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\AdminForumPostList.vue", "mtime": 1748720501657}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AdminForumPostList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,eAAA;AACA,EAAA,IAAA,EAAA,oBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,WAAA,EAAA,CAHA;AAIA,MAAA,QAAA,EAAA,EAJA;AAKA,MAAA,KAAA,EAAA,CALA;AAMA,MAAA,OAAA,EAAA;AANA,KAAA;AAQA,GAXA;AAYA,EAAA,OAZA,qBAYA;AACA;AACA,QAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;;AAEA,SAAA,UAAA;AACA,GArBA;AAsBA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA,CADA,CAGA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACA,UAAA,MAAA,GAAA,KAAA,CAAA,EAAA,CALA,CAKA;;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,KAAA,WADA;AAEA,QAAA,QAAA,EAAA,KAAA,QAFA;AAGA,QAAA,MAAA,EAAA,MAHA;AAIA,QAAA,cAAA,EAAA,IAJA,CAIA;;AAJA,OAAA;;AAOA,UAAA,KAAA,OAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA;;AAEA,WAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA,MAGA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,OAZA,EAaA,OAbA,CAaA,YAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAfA;AAgBA,KAnCA;AAoCA,IAAA,gBApCA,4BAoCA,IApCA,EAoCA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,UAAA;AACA,KAvCA;AAwCA,IAAA,WAxCA,yBAwCA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,UAAA;AACA,KA3CA;AA4CA,IAAA,UA5CA,wBA4CA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,0BAAA;AACA,KA9CA;AA+CA,IAAA,SA/CA,qBA+CA,EA/CA,EA+CA,KA/CA,EA+CA;AAAA;;AACA,WAAA,IAAA,2BAAA,EAAA,oBAAA,KAAA,GACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,KAAA,GAAA,MAAA,GAAA,QAAA;;AACA,UAAA,MAAA,CAAA,UAAA,GAFA,CAEA;;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,OAZA;AAaA,KA7DA;AA8DA,IAAA,cA9DA,0BA8DA,EA9DA,EA8DA;AACA,WAAA,OAAA,CAAA,IAAA,kCAAA,EAAA;AACA,KAhEA;AAiEA,IAAA,UAjEA,sBAiEA,OAjEA,EAiEA;AACA,UAAA,CAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,OAAA,CAAA;AACA,uBAAA,IAAA,CAAA,WAAA,EAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA;AArEA;AAtBA,CAAA", "sourcesContent": ["<template>\n    <div class=\"forum-container\">\n        <div class=\"forum-header\">\n            <h2>维修员论坛</h2>\n            <div class=\"forum-actions\">\n                <el-input\n                    placeholder=\"搜索帖子\"\n                    v-model=\"keyword\"\n                    class=\"search-input\"\n                    @keyup.enter.native=\"searchPosts\"\n                >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchPosts\"></el-button>\n                </el-input>\n                <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"createPost\" class=\"create-btn\">发布帖子</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"post in posts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">\n                        <el-tag type=\"success\" v-if=\"post.isTop\">置顶</el-tag>\n                        {{ post.title }}\n                    </div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"warning\" v-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                        <el-button\n                            size=\"mini\"\n                            type=\"text\"\n                            @click=\"toggleTop(post.id, !post.isTop)\">\n                            {{ post.isTop ? '取消置顶' : '置顶' }}\n                        </el-button>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <el-pagination\n                    background\n                    layout=\"prev, pager, next\"\n                    :total=\"total\"\n                    :page-size=\"pageSize\"\n                    :current-page.sync=\"currentPage\"\n                    @current-change=\"handlePageChange\"\n                >\n                </el-pagination>\n            </div>\n\n            <el-empty v-if=\"posts.length === 0\" description=\"暂无帖子\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AdminForumPostList',\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0,\n            keyword: ''\n        };\n    },\n    created() {\n        // 检查管理员登录状态\n        if (!sessionStorage.getItem('admin')) {\n            this.$message.error('您没有权限访问此页面');\n            this.$router.push('/admin');\n            return;\n        }\n\n        this.fetchPosts();\n    },\n    methods: {\n        fetchPosts() {\n            this.loading = true;\n\n            // 使用实际的管理员ID\n            const admin = JSON.parse(sessionStorage.getItem('admin'));\n            const userId = admin.id; // 管理员ID\n\n            const params = {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: userId,\n                isAdminRequest: true // 标记这是管理员请求\n            };\n\n            if (this.keyword) {\n                params.keyword = this.keyword;\n            }\n\n            this.$get('/forum/post/list', params)\n                .then(res => {\n                    if (res.data.status) {\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePageChange(page) {\n            this.currentPage = page;\n            this.fetchPosts();\n        },\n        searchPosts() {\n            this.currentPage = 1;\n            this.fetchPosts();\n        },\n        createPost() {\n            this.$router.push('/admin/home/<USER>/create');\n        },\n        toggleTop(id, isTop) {\n            this.$put(`/forum/post/top/${id}?isTop=${isTop}`)\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');\n                        this.fetchPosts(); // 重新加载帖子列表\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('置顶操作失败:', err);\n                    this.$message.error('置顶操作失败');\n                });\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/admin/home/<USER>/post/${id}`);\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.forum-container {\n    padding: 20px;\n}\n\n.forum-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.forum-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.search-input {\n    width: 300px;\n    margin-right: 10px;\n}\n\n.create-btn {\n    margin-left: 10px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n    cursor: pointer;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n}\n\n.pagination-container {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}