{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\User.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\User.vue", "mtime": 1748511149390}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["User.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "User.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n\n        <div class=\"center\">\n            <el-input placeholder=\"请输入内容\"\n                      v-model=\"input\"\n                      class=\"input-with-select\"\n                      @keydown.enter.native=\"clickSearch\">\n                <el-select v-model=\"select\" slot=\"prepend\" placeholder=\"请选择\" value=\"1\">\n                    <el-option label=\"账号\" value=\"1\"></el-option>\n                    <el-option label=\"姓名\" value=\"2\"></el-option>\n                </el-select>\n                <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"clickSearch\"></el-button>\n            </el-input>\n        </div>\n\n        <div class=\"bottom\">\n            <el-table\n                    :data=\"users\"\n                    :resizable=\"false\"\n                    style=\"width: 100%  \">\n                <el-table-column\n                        prop=\"studentId\"\n                        label=\"账号\"\n                        min-width=\"140\">\n                </el-table-column>\n                <el-table-column\n                        prop=\"username\"\n                        label=\"姓名\"\n                        min-width=\"120\">\n                </el-table-column>\n                <el-table-column\n                        prop=\"phone\"\n                        label=\"手机号\"\n                        min-width=\"110\">\n                </el-table-column>\n\n                <el-table-column\n                        label=\"性别\"\n                        min-width=\"60\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.sex==0?'男':'女'}}\n                    </template>\n                </el-table-column>\n\n                <el-table-column\n                        prop=\"role.name\"\n                        label=\"角色\"\n                        min-width=\"200\">\n                </el-table-column>\n                <el-table-column\n                        prop=\"dept.name\"\n                        label=\"类别\"\n                        min-width=\"100\">\n                </el-table-column>\n                <el-table-column\n                        prop=\"type.name\"\n                        label=\"子类别\"\n                        min-width=\"150\">\n                </el-table-column>\n                <el-table-column\n                        prop=\"balance\"\n                        label=\"余额\"\n                        min-width=\"50\">\n                </el-table-column>\n                <el-table-column\n                        label=\"禁用用户\"\n                        min-width=\"90\">\n                    <template slot-scope=\"scope\">\n                        <el-switch\n                                :value=\"scope.row.state ==1\"\n                                active-color=\"#13ce66\"\n                                inactive-color=\"#ff4949\"\n                        @change=\"edit(scope.row)\">\n                        </el-switch>\n                    </template>\n                </el-table-column>\n\n                <el-table-column\n                        label=\"操作\"\n                        width=\"100\">\n                    <template slot-scope=\"scope\">\n                        <el-popconfirm\n                                confirm-button-text='好的'\n                                cancel-button-text='不用了'\n                                icon=\"el-icon-info\"\n                                icon-color=\"red\"\n                                title=\"确定删除该用户吗？\"\n                                @confirm=\"del(scope.row.id)\"\n                        >\n                            <el-button type=\"text\" size=\"small\" slot=\"reference\"><i class=\"el-icon-delete\" style=\"color: red\"></i></el-button>\n                        </el-popconfirm>\n                    </template>\n                </el-table-column>\n            </el-table>\n        </div>\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Student\",\n        methods: {\n            clickSearch() {\n                let parameter = {};\n                if (this.input) {\n                    if (this.select == 1) {\n                        parameter = {\"studentId\": this.input};\n                    } else {\n                        parameter = {\"username\": this.input};\n                    }\n                    this.$get(\"/user\", parameter)\n                    .then((res) => {\n                        this.users = res.data.user\n                        this.input = \"\"\n                    })\n                } else {\n                    this.newList()\n                }\n            },\n            del(id) {\n                console.log(id);\n                this.$del(\"/user/\"+id)\n                .then((res) => {\n                    this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                    this.newList()\n                })\n            },\n\n            edit(row) {\n                // console.log(row.state)\n                if (row.state == 0){\n                    row.state = 1;\n                }else {\n                    row.state = 0;\n                }\n                this.$put(\"/user\", {id:row.id,state: row.state})\n                .then((res) => {\n                    // this.$msg(res.data.msg, \"success\")\n                    this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                    this.newList()\n                })\n                .catch((err) => {\n                    this.$notifyMsg(\"失败\", res.data.msg, \"error\")\n                })\n            },\n            newList() {\n                this.$get(\"/user\")\n                .then((rs) => {\n                    this.users = rs.data.user\n                    // console.log(this.users)\n                })\n            },\n        },\n        data() {\n            return {\n                users: [],\n                input: '',\n                select: '1'\n            }\n        },\n        created() {\n            this.newList()\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n\n    }\n\n    .center {\n        width: 70%;\n        margin-bottom: 30px;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 80px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n\n\n    .form {\n        margin: 0 22px;\n    }\n</style>\n"]}]}