{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\src\\util\\util.js", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\util\\util.js", "mtime": 1737774014078}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3BsaXQuanMiOwpleHBvcnQgeyBzZXRDb29raWUsIGdldENvb2tpZSwgZGVsQ29va2llIH07Ci8qKgogKiDoqK3nva5Db29raWUKICog5Zu65a6aMzDlpKkKICogQHBhcmFtIHZhbHVlCiAqIEBwYXJhbSBuYW1lCiAqLwoKZnVuY3Rpb24gc2V0Q29va2llKG5hbWUsIHZhbHVlKSB7CiAgdmFyIGRheXMgPSAzMDsKICB2YXIgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpICsgZGF5cyAqIDI0ICogNjAgKiA2MCAqIDEwMDApOwogIHZhciBleHBpcmVzID0gIjsgZXhwaXJlcz0iICsgZGF0ZS50b0dNVFN0cmluZygpOwogIGRvY3VtZW50LmNvb2tpZSA9IG5hbWUgKyAiPSIgKyB2YWx1ZSArIGV4cGlyZXMgKyAiOyBwYXRoPS8iOwp9Ci8qKgogKiDnjbLlj5ZDb29raWUKICogQHBhcmFtIG5hbWUKICovCgoKZnVuY3Rpb24gZ2V0Q29va2llKG5hbWUpIHsKICB2YXIgbmFtZUVRID0gbmFtZSArICI9IjsKICB2YXIgY2EgPSBkb2N1bWVudC5jb29raWUuc3BsaXQoJzsnKTsKCiAgZm9yICh2YXIgaSA9IDA7IGkgPCBjYS5sZW5ndGg7IGkrKykgewogICAgdmFyIGMgPSBjYVtpXTsKCiAgICB3aGlsZSAoYy5jaGFyQXQoMCkgPT09ICcgJykgewogICAgICBjID0gYy5zdWJzdHJpbmcoMSwgYy5sZW5ndGgpOwogICAgfQoKICAgIGlmIChjLmluZGV4T2YobmFtZUVRKSA9PT0gMCkgcmV0dXJuIGMuc3Vic3RyaW5nKG5hbWVFUS5sZW5ndGgsIGMubGVuZ3RoKTsKICB9CgogIHJldHVybiBudWxsOwp9Ci8qKgogKiDmuIXpmaRDb29raWUKICogQHBhcmFtIG5hbWUKICovCgoKZnVuY3Rpb24gZGVsQ29va2llKG5hbWUpIHsKICB2YXIgZGF5cyA9IDA7CiAgdmFyIGRhdGUgPSBuZXcgRGF0ZSgpOwogIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIGRheXMpOwogIHZhciBleHBpcmVzID0gIjsgZXhwaXJlcz0iICsgZGF0ZS50b0dNVFN0cmluZygpOwogIGRvY3VtZW50LmNvb2tpZSA9IG5hbWUgKyAiPSIgKyBleHBpcmVzICsgIjsgcGF0aD0vIjsKfQ=="}, {"version": 3, "sources": ["D:/ending/250426/zfront/campus-web/src/util/util.js"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "name", "value", "days", "date", "Date", "setTime", "getTime", "expires", "toGMTString", "document", "cookie", "nameEQ", "ca", "split", "i", "length", "c", "char<PERSON>t", "substring", "indexOf"], "mappings": ";;AAAA,SACIA,SADJ,EAEIC,SAFJ,EAGIC,SAHJ;AAMA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASF,SAAT,CAAmBG,IAAnB,EAAyBC,KAAzB,EAAgC;AAC5B,MAAIC,IAAI,GAAG,EAAX;AACA,MAAIC,IAAI,GAAG,IAAIC,IAAJ,EAAX;AACAD,EAAAA,IAAI,CAACE,OAAL,CAAaF,IAAI,CAACG,OAAL,KAAkBJ,IAAI,GAAG,EAAP,GAAY,EAAZ,GAAiB,EAAjB,GAAsB,IAArD;AACA,MAAIK,OAAO,GAAG,eAAeJ,IAAI,CAACK,WAAL,EAA7B;AACAC,EAAAA,QAAQ,CAACC,MAAT,GAAkBV,IAAI,GAAG,GAAP,GAAaC,KAAb,GAAqBM,OAArB,GAA+B,UAAjD;AACH;AAED;AACA;AACA;AACA;;;AACA,SAAST,SAAT,CAAmBE,IAAnB,EAAyB;AACrB,MAAIW,MAAM,GAAGX,IAAI,GAAG,GAApB;AACA,MAAIY,EAAE,GAAGH,QAAQ,CAACC,MAAT,CAAgBG,KAAhB,CAAsB,GAAtB,CAAT;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,EAAE,CAACG,MAAvB,EAA+BD,CAAC,EAAhC,EAAoC;AAChC,QAAIE,CAAC,GAAGJ,EAAE,CAACE,CAAD,CAAV;;AACA,WAAOE,CAAC,CAACC,MAAF,CAAS,CAAT,MAAgB,GAAvB;AAA4BD,MAAAA,CAAC,GAAGA,CAAC,CAACE,SAAF,CAAY,CAAZ,EAAeF,CAAC,CAACD,MAAjB,CAAJ;AAA5B;;AACA,QAAIC,CAAC,CAACG,OAAF,CAAUR,MAAV,MAAsB,CAA1B,EAA6B,OAAOK,CAAC,CAACE,SAAF,CAAYP,MAAM,CAACI,MAAnB,EAA2BC,CAAC,CAACD,MAA7B,CAAP;AAChC;;AACD,SAAO,IAAP;AACH;AAED;AACA;AACA;AACA;;;AACA,SAAShB,SAAT,CAAmBC,IAAnB,EAAyB;AACrB,MAAIE,IAAI,GAAG,CAAX;AACA,MAAIC,IAAI,GAAG,IAAIC,IAAJ,EAAX;AACAD,EAAAA,IAAI,CAACE,OAAL,CAAaF,IAAI,CAACG,OAAL,KAAiBJ,IAA9B;AACA,MAAIK,OAAO,GAAG,eAAeJ,IAAI,CAACK,WAAL,EAA7B;AAEAC,EAAAA,QAAQ,CAACC,MAAT,GAAkBV,IAAI,GAAG,GAAP,GAAaO,OAAb,GAAuB,UAAzC;AACH", "sourcesContent": ["export {\n    setCookie,\n    get<PERSON><PERSON>ie,\n    delCookie,\n}\n\n/**\n * 設置Cookie\n * 固定30天\n * @param value\n * @param name\n */\nfunction setCookie(name, value) {\n    var days = 30;\n    var date = new Date();\n    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));\n    var expires = \"; expires=\" + date.toGMTString();\n    document.cookie = name + \"=\" + value + expires + \"; path=/\";\n}\n\n/**\n * 獲取Cookie\n * @param name\n */\nfunction getCookie(name) {\n    var nameEQ = name + \"=\";\n    var ca = document.cookie.split(';');\n    for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n}\n\n/**\n * 清除Cookie\n * @param name\n */\nfunction delCookie(name) {\n    var days = 0;\n    var date = new Date();\n    date.setTime(date.getTime() + days);\n    var expires = \"; expires=\" + date.toGMTString();\n\n    document.cookie = name + \"=\" + expires + \"; path=/\";\n}"]}]}