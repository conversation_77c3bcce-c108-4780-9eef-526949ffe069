{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\adviseuser.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\notice\\adviseuser.vue", "mtime": 1748720500260}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["adviseuser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "adviseuser.vue", "sourceRoot": "src/views/notice", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n        <el-input\n          v-model=\"queryParams.noticeTitle\"\n          placeholder=\"请输入公告标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n        <el-input\n          v-model=\"queryParams.noticeContent\"\n          placeholder=\"请输入公告内容\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n      </el-form-item>\n    </el-form>\n\n\n    <el-table v-loading=\"loading\" :data=\"adviseList\" >\n      <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n      <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n      <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ transform(scope.row.createTime) }}</span>\n        </template> \n      </el-table-column>\n\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n<!--          <el-button-->\n<!--            size=\"mini\"-->\n<!--            type=\"text\"-->\n<!--            icon=\"el-icon-edit\"-->\n<!--            @click=\"handleUpdate(scope.row)\"-->\n<!--          >查看公告</el-button>-->\n            <el-button\n                    size=\"mini\"\n                    type=\"text\"\n                    @click=\"handleUpdate(scope.row)\"\n            >查看公告</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <div class=\"pagination-wrapper\">\n      <common-pagination\n        :total=\"total\"\n        :current-page.sync=\"currentPage\"\n        :page-size.sync=\"pageSize\"\n        @pagination-change=\"handlePaginationChange\">\n      </common-pagination>\n    </div>\n\n    <!-- 添加或修改通知公告公告对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"743px\" append-to-body>\n\n        <!-- <el-table v-loading=\"loading\" :data=\"adviseList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column label=\"序号\" align=\"center\" prop=\"noticeId\" width=\"100\" />\n        <el-table-column label=\"公告标题\" align=\"center\" prop=\"noticeTitle\" />\n        <el-table-column label=\"创建者\" align=\"center\" prop=\"createBy\" width=\"100\" />\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n            <el-button\n                size=\"mini\"\n                type=\"text\"\n                icon=\"el-icon-edit\"\n                @click=\"handleUpdate(scope.row)\"\n            >查看公告</el-button>\n            </template>\n        </el-table-column>\n        </el-table> -->\n\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n            <el-form-item label=\"公告标题\" prop=\"noticeTitle\">\n            <el-input v-model=\"form.noticeTitle\" placeholder=\"请输入公告标题\" />\n            </el-form-item>\n            <el-form-item label=\"公告内容\" prop=\"noticeContent\">\n              <el-input\n                            resize=\"none\"\n                            type=\"textarea\"\n                            :autosize=\"{ minRows: 6, maxRows: 10}\"\n                            placeholder=\"请输入公告内容\"\n                            v-model=\"form.noticeContent\" style=\"padding: 0\">\n              </el-input>\n              <!-- <quill-editor v-model=\"form.noticeContent\" placeholder=\"请输入公告内容\" /> -->\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"remark\">\n            <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n            <el-form-item label=\"创建者\" prop=\"remark\">\n            <el-input v-model=\"form.createBy\" placeholder=\"请输入备注\" />\n            </el-form-item>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"cancel\">确 定</el-button>\n        </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listAdvise, getAdvise} from \"@/api/notice/advise\";\n  import {formatDate} from '@/util/date';\n  import CommonPagination from '@/components/CommonPagination.vue';\n\n  export default {\n    name: \"Advise\",\n    components: {\n      CommonPagination\n    },\n    data() {\n      return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 通知公告公告表格数据\n        adviseList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 当前页码\n        currentPage: 1,\n        // 每页显示条数\n        pageSize: 10,\n        // 查询参数\n        queryParams: {\n          noticeTitle: null,\n          noticeContent: null,\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n          noticeTitle: [\n            { required: true, message: \"公告标题不能为空\", trigger: \"blur\" }\n          ],\n        }\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询通知公告列表 */\n      getList() {\n        this.loading = true;\n        listAdvise({\n          pageNum: this.currentPage,\n          pageSize: this.pageSize,\n          ...this.queryParams\n        }).then(response => {\n          this.adviseList = response.data.rows;\n          this.total = response.data.total;\n          this.loading = false;\n        });\n      },\n\n      /** 修改按钮操作 */\n      handleUpdate(row) {\n        const noticeId = row.noticeId || this.ids\n        getAdvise(noticeId).then(response => {\n          this.form = response.data.data;\n          this.open = true;\n          this.title = \"查看通知公告\";\n        });\n      },\n      // 取消按钮\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.currentPage = 1;\n        this.getList();\n      },\n      transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd');\n      },\n      handlePaginationChange() {\n        this.getList();\n      },\n      reset() {\n        this.form = {};\n      }\n    }\n  };\n</script>\n\n<style scoped lang=\"less\">\n\n    /deep/ .el-input__inner{\n    text-align : center;\n    }\n\n    .pagination-wrapper {\n      margin-top: 20px;\n    }\n\n</style>\n"]}]}