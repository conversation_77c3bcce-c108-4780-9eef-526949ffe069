{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue", "mtime": 1748720502030}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MyPosts.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MyPosts.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"my-posts-container\">\n        <div class=\"page-header\">\n            <h2>我的帖子</h2>\n            <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"全部\" name=\"all\"></el-tab-pane>\n                <el-tab-pane label=\"已发布\" name=\"published\"></el-tab-pane>\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已发布</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost(post.id)\">编辑</el-button>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" description=\"暂无帖子\"></el-empty>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'MyPosts',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'all',\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'all') {\n                return this.posts;\n            } else if (this.activeTab === 'published') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        this.fetchMyPosts();\n    },\n    methods: {\n        fetchMyPosts() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n\n            // 使用authorId参数来过滤当前用户的帖子\n            this.$get('/forum/post/list', {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: userId,\n                authorId: userId // 现在后端支持按作者ID过滤\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        // 不需要再进行前端过滤，因为后端已经过滤了\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                        console.log('获取到我的帖子：', this.posts.length);\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/home/<USER>/post/${id}`);\n        },\n        editPost(id) {\n            this.$router.push(`/home/<USER>/edit/${id}`);\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const userId = JSON.parse(sessionStorage.getItem('user')).id;\n                this.$del(`/forum/post/${id}`, { operatorId: userId })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchMyPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        },\n        handlePaginationChange() {\n            this.fetchMyPosts();\n        }\n    }\n};\n</script>\n\n<style scoped>\n.my-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n</style>\n"]}]}