<template>
    <div class="forum-container">
        <div class="forum-header">
            <h2>维修员论坛</h2>
            <div class="forum-actions">
                <el-input
                    placeholder="搜索帖子"
                    v-model="keyword"
                    class="search-input"
                    @keyup.enter.native="searchPosts"
                >
                    <el-button slot="append" icon="el-icon-search" @click="searchPosts"></el-button>
                </el-input>
                <el-button type="primary" icon="el-icon-edit" @click="createPost" class="create-btn">发布帖子</el-button>
            </div>
        </div>

        <el-card v-if="loading" class="loading-card">
            <el-skeleton :rows="10" animated />
        </el-card>

        <div v-else>
            <el-card v-for="post in posts" :key="post.id" class="post-card" shadow="hover">
                <div class="post-header">
                    <div class="post-title" @click="viewPostDetail(post.id)">
                        <el-tag type="success" v-if="post.isTop">置顶</el-tag>
                        {{ post.title }}
                    </div>
                    <div class="post-status">
                        <el-tag type="warning" v-if="post.status === 0">待审核</el-tag>
                        <el-tag type="danger" v-else-if="post.status === 2">已拒绝</el-tag>
                        <el-button
                            size="mini"
                            type="text"
                            @click="toggleTop(post.id, !post.isTop)">
                            {{ post.isTop ? '取消置顶' : '置顶' }}
                        </el-button>
                    </div>
                </div>
                <div class="post-content">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>
                <div class="post-footer">
                    <div class="post-info">
                        <span>作者: {{ post.author ? post.author.username : '未知' }}</span>
                        <span>发布时间: {{ formatDate(post.createTime) }}</span>
                        <span>
                            <i class="el-icon-chat-dot-square"></i> {{ post.commentCount }}
                            <i class="el-icon-star-off"></i> {{ post.likeCount }}
                        </span>
                    </div>
                </div>
            </el-card>

            <div class="pagination-container">
                <el-pagination
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="pageSize"
                    :current-page.sync="currentPage"
                    @current-change="handlePageChange"
                >
                </el-pagination>
            </div>

            <el-empty v-if="posts.length === 0" description="暂无帖子"></el-empty>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AdminForumPostList',
    data() {
        return {
            posts: [],
            loading: true,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            keyword: ''
        };
    },
    created() {
        // 检查管理员登录状态
        if (!sessionStorage.getItem('admin')) {
            this.$message.error('您没有权限访问此页面');
            this.$router.push('/admin');
            return;
        }

        this.fetchPosts();
    },
    methods: {
        fetchPosts() {
            this.loading = true;

            // 使用实际的管理员ID
            const admin = JSON.parse(sessionStorage.getItem('admin'));
            const userId = admin.id; // 管理员ID

            const params = {
                pageNum: this.currentPage,
                pageSize: this.pageSize,
                userId: userId,
                isAdminRequest: true // 标记这是管理员请求
            };

            if (this.keyword) {
                params.keyword = this.keyword;
            }

            this.$get('/forum/post/list', params)
                .then(res => {
                    if (res.data.status) {
                        this.posts = res.data.page.records;
                        this.total = res.data.page.total;
                    } else {
                        this.$message.error(res.data.msg || '获取帖子列表失败');
                    }
                })
                .catch(err => {
                    console.error('获取帖子列表失败:', err);
                    this.$message.error('获取帖子列表失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handlePageChange(page) {
            this.currentPage = page;
            this.fetchPosts();
        },
        searchPosts() {
            this.currentPage = 1;
            this.fetchPosts();
        },
        createPost() {
            this.$router.push('/admin/home/<USER>/create');
        },
        toggleTop(id, isTop) {
            this.$put(`/forum/post/top/${id}?isTop=${isTop}`)
                .then(res => {
                    if (res.data.status) {
                        this.$message.success(isTop ? '置顶成功' : '取消置顶成功');
                        this.fetchPosts(); // 重新加载帖子列表
                    } else {
                        this.$message.error(res.data.msg || '操作失败');
                    }
                })
                .catch(err => {
                    console.error('置顶操作失败:', err);
                    this.$message.error('置顶操作失败');
                });
        },
        viewPostDetail(id) {
            this.$router.push(`/admin/home/<USER>/post/${id}`);
        },
        formatDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        }
    }
};
</script>

<style scoped>
.forum-container {
    padding: 20px;
}

.forum-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.forum-actions {
    display: flex;
    gap: 10px;
}

.search-input {
    width: 300px;
    margin-right: 10px;
}

.create-btn {
    margin-left: 10px;
}

.post-card {
    margin-bottom: 15px;
    cursor: pointer;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.post-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
}

.post-content {
    color: #606266;
    margin-bottom: 10px;
    line-height: 1.5;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    color: #909399;
    font-size: 14px;
}

.post-info {
    display: flex;
    gap: 15px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.loading-card {
    padding: 20px;
}
</style>
