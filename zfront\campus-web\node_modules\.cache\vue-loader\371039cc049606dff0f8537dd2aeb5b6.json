{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue?vue&type=style&index=0&id=a44c444e&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\Home.vue", "mtime": 1748450667594}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm1haW4gewogICAgZGlzcGxheTogZmxleDsKICAgIGhlaWdodDogMTAwJTsKCiAgICAubGVmdCB7CiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsKICAgICAgICBwb3NpdGlvbjogZml4ZWQ7CiAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgIC5sb2dvIHsKICAgICAgICAgICAgd2lkdGg6IDkwJTsKICAgICAgICAgICAgLypjb2xvcjogd2hpdGU7Ki8KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICAgIHBhZGRpbmc6IDhweCAwOwogICAgICAgICAgICAvKmJvcmRlcjogMXB4IHNvbGlkIHdoaXRlOyovCiAgICAgICAgICAgIG1hcmdpbjogOS4xcHggYXV0bzsKICAgICAgICB9CiAgICB9CgogICAgLnJpZ2h0IHsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgLnRvcCB7CiAgICAgICAgICAgIC8qY29sb3I6ICNmZmY7Ki8KICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOwoKICAgICAgICAgICAgLmljb24gewogICAgICAgICAgICAgICAgZm9udC1zaXplOiAyMHB4OwogICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC5ib3R0b20gewogICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgLypoZWlnaHQ6IDEwMCU7Ki8KICAgICAgICAgICAgLypiYWNrZ3JvdW5kOiAjZmZmOyovCgogICAgICAgICAgICAuYm90dG9tX3RvcCB7CiAgICAgICAgICAgICAgICBwYWRkaW5nOiAyMHB4OwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/admin", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"logo\">售后维修一站通</div>\n\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/admin/home\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>类别管理</span>\n                    </template>\n                    <el-menu-item index=\"/admin/home/<USER>\">\n                       <i class=\"el-icon-s-order\"></i>\n                            <span>类别信息</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"4\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>用户管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>用户信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"5\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>任务管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>任务信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"6\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>评价管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>评价信息</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"7\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-refresh\"></i>\n                        <span>公告管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>\">\n                            <i class=\"el-icon-s-order\"></i>\n                            <span>系统公告</span>\n                        </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"8\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>论坛管理</span>\n                    </template>\n                        <el-menu-item index=\"/admin/home/<USER>/create\">\n                            <i class=\"el-icon-edit\"></i>\n                            <span>发布帖子</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/audit\">\n                            <i class=\"el-icon-s-check\"></i>\n                            <span>帖子审核</span>\n                        </el-menu-item>\n                        <el-menu-item index=\"/admin/home/<USER>/posts\">\n                            <i class=\"el-icon-document\"></i>\n                            <span>帖子列表</span>\n                        </el-menu-item>\n                </el-submenu>\n\n            </el-menu>\n        </div>\n        <div class=\"right\" :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\" style=\"transition: .3s;\">\n            <div class=\"top\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\"></i>\n                </div>\n                <el-menu\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        @select=\"handleSelect\"\n                        menu-trigger=\"click\">\n                    <el-submenu index=\"2\">\n                        <template slot=\"title\">{{admin.account}}</template>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view></router-view>\n                </transition>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('admin',['setAdmin']),\n            handleSelect(key, keyPath) {\n                // console.log(key, keyPath);\n            },\n            getBreadcrumb(){\n                let matched = this.$route.matched;\n                if(matched[0].name != 'home'){\n                    matched = [{path:\"/admin/home\",meta:{title:'首页'}}].concat(matched)\n                }\n                // if (!this.isHome(matched[0])){\n                //     matched = [{path:\"/home\",meta:{title:'首页'}}].concat(matched);\n                // }\n                this.breadList = matched;\n            },\n            exit(){\n                sessionStorage.removeItem('admin');\n                this.$router.push(\"/admin\")\n            }\n        },\n        computed: {\n            ...mapState('admin',['admin'])\n        },\n        data() {\n            return {\n                //当前路由\n                breadList:[],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb();\n            }\n        },\n        created() {\n            // console.log(sessionStorage.getItem('admin'))\n            if (sessionStorage.getItem(\"admin\")){\n                this.setAdmin(JSON.parse(sessionStorage.getItem(\"admin\")));\n                this.getBreadcrumb();\n                /*let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)\n                console.log(flag)\n                if (flag) {\n                    this.$router.push(\"m/login\")\n                }*/\n            }else {\n                this.$msg(\"尚未登陆\",\"error\")\n                this.$router.push(\"/admin/\")\n            }\n\n        },\n        mounted() {\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n        },\n    }\n\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 100%;\n\n        .left {\n            background: #fff;\n            position: fixed;\n            height: 100%;\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto;\n            }\n        }\n\n        .right {\n            position: relative;\n            .top {\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                background: #fff;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                /*height: 100%;*/\n                /*background: #fff;*/\n\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n    }\n</style>"]}]}