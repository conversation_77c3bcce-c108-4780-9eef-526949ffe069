{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue", "mtime": 1748723799861}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Chat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Chat.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n    <el-dialog\n        :title=\"dialogTitle\"\n        :visible.sync=\"visible\"\n        width=\"600px\"\n        :before-close=\"handleClose\"\n        class=\"chat-dialog\">\n        \n        <div class=\"chat-container\">\n            <!-- 聊天消息区域 -->\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\n                <div \n                    v-for=\"message in messages\" \n                    :key=\"message.id\"\n                    :class=\"['message-item', message.senderId === currentUserId ? 'sent' : 'received']\">\n                    \n                    <div class=\"message-info\">\n                        <span class=\"sender-name\">\n                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}\n                        </span>\n                        <span class=\"send-time\">{{ formatTime(message.sendTime) }}</span>\n                    </div>\n                    \n                    <div class=\"message-content\">\n                        {{ message.content }}\n                    </div>\n                </div>\n                \n                <!-- 无消息提示 -->\n                <div v-if=\"messages.length === 0\" class=\"no-messages\">\n                    <i class=\"el-icon-chat-dot-round\"></i>\n                    <p>开始聊天吧！</p>\n                </div>\n            </div>\n            \n            <!-- 消息输入区域 -->\n            <div class=\"chat-input\">\n                <el-input\n                    v-model=\"newMessage\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    placeholder=\"输入消息...\"\n                    @keydown.enter.native=\"handleEnterKey\"\n                    :disabled=\"!connected\">\n                </el-input>\n                \n                <div class=\"input-actions\">\n                    <span v-if=\"!connected\" class=\"connection-status\">\n                        <i class=\"el-icon-loading\"></i> 连接中...\n                    </span>\n                    <el-button \n                        type=\"primary\" \n                        size=\"small\" \n                        @click=\"sendMessage\"\n                        :disabled=\"!newMessage.trim() || !connected\">\n                        发送\n                    </el-button>\n                </div>\n            </div>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nimport SockJS from 'sockjs-client'\nimport Stomp from 'stompjs'\n\nexport default {\n    name: 'Chat',\n    props: {\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        task: {\n            type: Object,\n            required: true\n        },\n        currentUserId: {\n            type: Number,\n            required: true\n        }\n    },\n    data() {\n        return {\n            stompClient: null,\n            connected: false,\n            messages: [],\n            newMessage: '',\n            subscription: null\n        }\n    },\n    computed: {\n        dialogTitle() {\n            if (!this.task) return '在线交流'\n            return `任务交流 - ${this.task.taskTitle}`\n        },\n        \n        // 获取对方用户ID\n        otherUserId() {\n            if (!this.task) return null\n            \n            // 如果当前用户是发布者，对方就是接受者\n            if (this.currentUserId === this.task.publishId) {\n                return this.task.acceptId\n            }\n            // 如果当前用户是接受者，对方就是发布者\n            else if (this.currentUserId === this.task.acceptId) {\n                return this.task.publishId\n            }\n            \n            return null\n        }\n    },\n    watch: {\n        visible(newVal) {\n            if (newVal) {\n                this.openChat()\n            } else {\n                this.closeChat()\n            }\n        }\n    },\n    methods: {\n        // 打开聊天\n        openChat() {\n            this.connectWebSocket()\n            this.loadChatHistory()\n        },\n        \n        // 关闭聊天\n        closeChat() {\n            this.disconnectWebSocket()\n            this.messages = []\n            this.newMessage = ''\n        },\n        \n        // 连接WebSocket\n        connectWebSocket() {\n            if (this.stompClient && this.connected) {\n                return\n            }\n            \n            try {\n                const socket = new SockJS('http://localhost:8080/ws')\n                this.stompClient = Stomp.over(socket)\n                \n                // 禁用调试日志\n                this.stompClient.debug = null\n                \n                this.stompClient.connect({}, \n                    (frame) => {\n                        console.log('WebSocket连接成功:', frame)\n                        this.connected = true\n                        \n                        // 订阅个人消息队列\n                        this.subscription = this.stompClient.subscribe(\n                            `/user/${this.currentUserId}/queue/messages`,\n                            (message) => {\n                                const chatMessage = JSON.parse(message.body)\n                                this.handleReceivedMessage(chatMessage)\n                            }\n                        )\n                        \n                        // 发送加入聊天的消息\n                        this.stompClient.send('/app/chat.addUser', {}, JSON.stringify({\n                            senderId: this.currentUserId,\n                            taskId: this.task.id\n                        }))\n                    },\n                    (error) => {\n                        console.error('WebSocket连接失败:', error)\n                        this.connected = false\n                        this.$message.error('连接聊天服务失败，请稍后重试')\n                    }\n                )\n            } catch (error) {\n                console.error('创建WebSocket连接时发生错误:', error)\n                this.$message.error('无法建立聊天连接')\n            }\n        },\n        \n        // 断开WebSocket连接\n        disconnectWebSocket() {\n            if (this.subscription) {\n                this.subscription.unsubscribe()\n                this.subscription = null\n            }\n            \n            if (this.stompClient && this.connected) {\n                this.stompClient.disconnect(() => {\n                    console.log('WebSocket连接已断开')\n                })\n            }\n            \n            this.stompClient = null\n            this.connected = false\n        },\n        \n        // 发送消息\n        sendMessage() {\n            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {\n                return\n            }\n            \n            const message = {\n                taskId: this.task.id,\n                senderId: this.currentUserId,\n                receiverId: this.otherUserId,\n                content: this.newMessage.trim(),\n                messageType: 'text'\n            }\n            \n            try {\n                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))\n                this.newMessage = ''\n            } catch (error) {\n                console.error('发送消息失败:', error)\n                this.$message.error('发送消息失败，请重试')\n            }\n        },\n        \n        // 处理接收到的消息\n        handleReceivedMessage(message) {\n            // 检查消息是否属于当前任务\n            if (message.taskId === this.task.id) {\n                // 避免重复添加消息\n                const exists = this.messages.find(m => m.id === message.id)\n                if (!exists) {\n                    this.messages.push(message)\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                }\n            }\n        },\n        \n        // 加载聊天历史记录\n        loadChatHistory() {\n            this.$get('http://localhost:8080/api/chat/history', {\n                taskId: this.task.id,\n                userId: this.currentUserId\n            }).then(res => {\n                if (res.data.status) {\n                    this.messages = res.data.messages || []\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                } else {\n                    console.error('加载聊天记录失败:', res.data.msg)\n                }\n            }).catch(error => {\n                console.error('加载聊天记录失败:', error)\n            })\n        },\n        \n        // 滚动到底部\n        scrollToBottom() {\n            const container = this.$refs.messagesContainer\n            if (container) {\n                container.scrollTop = container.scrollHeight\n            }\n        },\n        \n        // 处理Enter键\n        handleEnterKey(event) {\n            if (event.ctrlKey || event.shiftKey) {\n                // Ctrl+Enter 或 Shift+Enter 换行\n                return\n            } else {\n                // 单独Enter发送消息\n                event.preventDefault()\n                this.sendMessage()\n            }\n        },\n        \n        // 格式化时间\n        formatTime(time) {\n            if (!time) return ''\n            \n            const date = new Date(time)\n            const now = new Date()\n            const diff = now - date\n            \n            // 如果是今天\n            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n                return date.toLocaleTimeString('zh-CN', { \n                    hour: '2-digit', \n                    minute: '2-digit' \n                })\n            }\n            \n            // 如果是昨天或更早\n            return date.toLocaleString('zh-CN', {\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        },\n        \n        // 处理对话框关闭\n        handleClose() {\n            this.$emit('update:visible', false)\n        }\n    },\n    \n    beforeDestroy() {\n        this.disconnectWebSocket()\n    }\n}\n</script>\n\n<style scoped lang=\"less\">\n.chat-dialog {\n    /deep/ .el-dialog__body {\n        padding: 0;\n    }\n}\n\n.chat-container {\n    height: 500px;\n    display: flex;\n    flex-direction: column;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #ebeef5;\n\n    .message-item {\n        margin-bottom: 15px;\n\n        &.sent {\n            text-align: right;\n\n            .message-info {\n                justify-content: flex-end;\n            }\n\n            .message-content {\n                background-color: #409EFF;\n                color: white;\n                margin-left: auto;\n                margin-right: 0;\n            }\n        }\n\n        &.received {\n            text-align: left;\n\n            .message-info {\n                justify-content: flex-start;\n            }\n\n            .message-content {\n                background-color: white;\n                color: #303133;\n                margin-left: 0;\n                margin-right: auto;\n            }\n        }\n    }\n\n    .message-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 5px;\n        font-size: 12px;\n        color: #909399;\n\n        .sender-name {\n            font-weight: bold;\n            margin-right: 8px;\n        }\n\n        .send-time {\n            font-size: 11px;\n        }\n    }\n\n    .message-content {\n        display: inline-block;\n        max-width: 70%;\n        padding: 10px 15px;\n        border-radius: 10px;\n        word-wrap: break-word;\n        white-space: pre-wrap;\n        line-height: 1.4;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .no-messages {\n        text-align: center;\n        color: #909399;\n        margin-top: 50px;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin: 0;\n        }\n    }\n}\n\n.chat-input {\n    padding: 15px;\n    background-color: white;\n\n    .input-actions {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 10px;\n\n        .connection-status {\n            color: #909399;\n            font-size: 12px;\n\n            i {\n                margin-right: 5px;\n            }\n        }\n    }\n}\n\n/* 滚动条样式 */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n}\n</style>\n"]}]}