{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue", "mtime": 1748752648774}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Chat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Chat.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n    <el-dialog\n        :title=\"dialogTitle\"\n        :visible.sync=\"visible\"\n        width=\"600px\"\n        :before-close=\"handleClose\"\n        class=\"chat-dialog\">\n        \n        <div class=\"chat-container\">\n            <!-- 聊天消息区域 -->\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\n                <div \n                    v-for=\"message in messages\" \n                    :key=\"message.id\"\n                    :class=\"['message-item', message.senderId === currentUserId ? 'sent' : 'received']\">\n                    \n                    <div class=\"message-info\">\n                        <span class=\"sender-name\">\n                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}\n                        </span>\n                        <span class=\"send-time\">{{ formatTime(message.sendTime) }}</span>\n                    </div>\n                    \n                    <div class=\"message-content\">\n                        {{ message.content }}\n                    </div>\n                </div>\n                \n                <!-- 无消息提示 -->\n                <div v-if=\"messages.length === 0\" class=\"no-messages\">\n                    <i class=\"el-icon-chat-dot-round\"></i>\n                    <p>开始聊天吧！</p>\n                </div>\n            </div>\n            \n            <!-- 消息输入区域 -->\n            <div class=\"chat-input\">\n                <el-input\n                    v-model=\"newMessage\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    placeholder=\"输入消息...\"\n                    @keydown.enter.native=\"handleEnterKey\"\n                    :disabled=\"!connected\">\n                </el-input>\n                \n                <div class=\"input-actions\">\n                    <span v-if=\"!connected\" class=\"connection-status\">\n                        <i class=\"el-icon-loading\"></i> 连接中...\n                    </span>\n                    <el-button \n                        type=\"primary\" \n                        size=\"small\" \n                        @click=\"sendMessage\"\n                        :disabled=\"!newMessage.trim() || !connected\">\n                        发送\n                    </el-button>\n                </div>\n            </div>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nimport SockJS from 'sockjs-client'\nimport Stomp from 'stompjs'\n\nexport default {\n    name: 'Chat',\n    props: {\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        task: {\n            type: Object,\n            required: true\n        },\n        currentUserId: {\n            type: Number,\n            required: true\n        }\n    },\n    data() {\n        return {\n            stompClient: null,\n            connected: false,\n            messages: [],\n            newMessage: '',\n            subscription: null\n        }\n    },\n    computed: {\n        dialogTitle() {\n            if (!this.task) return '在线交流'\n            return `任务交流 - ${this.task.taskTitle}`\n        },\n        \n        // 获取对方用户ID\n        otherUserId() {\n            if (!this.task) return null\n            \n            // 如果当前用户是发布者，对方就是接受者\n            if (this.currentUserId === this.task.publishId) {\n                return this.task.acceptId\n            }\n            // 如果当前用户是接受者，对方就是发布者\n            else if (this.currentUserId === this.task.acceptId) {\n                return this.task.publishId\n            }\n            \n            return null\n        }\n    },\n    watch: {\n        visible(newVal) {\n            if (newVal) {\n                this.openChat()\n            } else {\n                this.closeChat()\n            }\n        }\n    },\n    methods: {\n        // 打开聊天\n        openChat() {\n            this.connectWebSocket()\n            this.loadChatHistory()\n        },\n        \n        // 关闭聊天\n        closeChat() {\n            this.disconnectWebSocket()\n            this.messages = []\n            this.newMessage = ''\n        },\n        \n        // 连接WebSocket\n        connectWebSocket() {\n            if (this.stompClient && this.connected) {\n                return\n            }\n            \n            try {\n                const socket = new SockJS('/api/ws')\n                this.stompClient = Stomp.over(socket)\n                \n                // 启用调试日志\n                this.stompClient.debug = function(str) {\n                    console.log('STOMP: ' + str)\n                }\n                \n                this.stompClient.connect({}, \n                    (frame) => {\n                        console.log('WebSocket连接成功:', frame)\n                        this.connected = true\n                        \n                        // 订阅个人消息队列\n                        this.subscription = this.stompClient.subscribe(\n                            `/user/${this.currentUserId}/queue/messages`,\n                            (message) => {\n                                const chatMessage = JSON.parse(message.body)\n                                this.handleReceivedMessage(chatMessage)\n                            }\n                        )\n                        \n                        // 发送加入聊天的消息\n                        this.stompClient.send('/app/chat.addUser', {}, JSON.stringify({\n                            senderId: this.currentUserId,\n                            taskId: this.task.id\n                        }))\n                    },\n                    (error) => {\n                        console.error('WebSocket连接失败:', error)\n                        this.connected = false\n                        this.$message.error('连接聊天服务失败，请稍后重试')\n                    }\n                )\n            } catch (error) {\n                console.error('创建WebSocket连接时发生错误:', error)\n                this.$message.error('无法建立聊天连接')\n            }\n        },\n        \n        // 断开WebSocket连接\n        disconnectWebSocket() {\n            if (this.subscription) {\n                this.subscription.unsubscribe()\n                this.subscription = null\n            }\n            \n            if (this.stompClient && this.connected) {\n                this.stompClient.disconnect(() => {\n                    console.log('WebSocket连接已断开')\n                })\n            }\n            \n            this.stompClient = null\n            this.connected = false\n        },\n        \n        // 发送消息\n        sendMessage() {\n            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {\n                return\n            }\n            \n            const message = {\n                taskId: this.task.id,\n                senderId: this.currentUserId,\n                receiverId: this.otherUserId,\n                content: this.newMessage.trim(),\n                messageType: 'text'\n            }\n            \n            try {\n                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))\n                this.newMessage = ''\n            } catch (error) {\n                console.error('发送消息失败:', error)\n                this.$message.error('发送消息失败，请重试')\n            }\n        },\n        \n        // 处理接收到的消息\n        handleReceivedMessage(message) {\n            console.log('收到消息:', message)\n\n            // 检查消息是否属于当前任务\n            if (message.taskId === this.task.id) {\n                // 避免重复添加消息（如果消息有ID的话）\n                let exists = false\n                if (message.id) {\n                    exists = this.messages.find(m => m.id === message.id)\n                }\n\n                if (!exists) {\n                    // 确保消息有必要的字段\n                    if (!message.sendTime) {\n                        message.sendTime = new Date()\n                    }\n\n                    this.messages.push(message)\n                    console.log('消息已添加到列表，当前消息数量:', this.messages.length)\n\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                }\n            }\n        },\n        \n        // 加载聊天历史记录\n        loadChatHistory() {\n            this.$get('/api/chat/history', {\n                taskId: this.task.id,\n                userId: this.currentUserId\n            }).then(res => {\n                if (res.data.status) {\n                    this.messages = res.data.messages || []\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                } else {\n                    console.error('加载聊天记录失败:', res.data.msg)\n                }\n            }).catch(error => {\n                console.error('加载聊天记录失败:', error)\n            })\n        },\n        \n        // 滚动到底部\n        scrollToBottom() {\n            const container = this.$refs.messagesContainer\n            if (container) {\n                container.scrollTop = container.scrollHeight\n            }\n        },\n        \n        // 处理Enter键\n        handleEnterKey(event) {\n            if (event.ctrlKey || event.shiftKey) {\n                // Ctrl+Enter 或 Shift+Enter 换行\n                return\n            } else {\n                // 单独Enter发送消息\n                event.preventDefault()\n                this.sendMessage()\n            }\n        },\n        \n        // 格式化时间\n        formatTime(time) {\n            if (!time) return ''\n            \n            const date = new Date(time)\n            const now = new Date()\n            const diff = now - date\n            \n            // 如果是今天\n            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n                return date.toLocaleTimeString('zh-CN', { \n                    hour: '2-digit', \n                    minute: '2-digit' \n                })\n            }\n            \n            // 如果是昨天或更早\n            return date.toLocaleString('zh-CN', {\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        },\n        \n        // 处理对话框关闭\n        handleClose() {\n            this.$emit('update:visible', false)\n        }\n    },\n    \n    beforeDestroy() {\n        this.disconnectWebSocket()\n    }\n}\n</script>\n\n<style scoped lang=\"less\">\n.chat-dialog {\n    /deep/ .el-dialog__body {\n        padding: 0;\n    }\n}\n\n.chat-container {\n    height: 500px;\n    display: flex;\n    flex-direction: column;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #ebeef5;\n\n    .message-item {\n        margin-bottom: 15px;\n\n        &.sent {\n            text-align: right;\n\n            .message-info {\n                justify-content: flex-end;\n            }\n\n            .message-content {\n                background-color: #409EFF;\n                color: white;\n                margin-left: auto;\n                margin-right: 0;\n            }\n        }\n\n        &.received {\n            text-align: left;\n\n            .message-info {\n                justify-content: flex-start;\n            }\n\n            .message-content {\n                background-color: white;\n                color: #303133;\n                margin-left: 0;\n                margin-right: auto;\n            }\n        }\n    }\n\n    .message-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 5px;\n        font-size: 12px;\n        color: #909399;\n\n        .sender-name {\n            font-weight: bold;\n            margin-right: 8px;\n        }\n\n        .send-time {\n            font-size: 11px;\n        }\n    }\n\n    .message-content {\n        display: inline-block;\n        max-width: 70%;\n        padding: 10px 15px;\n        border-radius: 10px;\n        word-wrap: break-word;\n        white-space: pre-wrap;\n        line-height: 1.4;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .no-messages {\n        text-align: center;\n        color: #909399;\n        margin-top: 50px;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin: 0;\n        }\n    }\n}\n\n.chat-input {\n    padding: 15px;\n    background-color: white;\n\n    .input-actions {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 10px;\n\n        .connection-status {\n            color: #909399;\n            font-size: 12px;\n\n            i {\n                margin-right: 5px;\n            }\n        }\n    }\n}\n\n/* 滚动条样式 */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n}\n</style>\n"]}]}