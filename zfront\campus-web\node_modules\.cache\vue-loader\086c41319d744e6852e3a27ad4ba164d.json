{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue?vue&type=style&index=0&id=421a0362&scoped=true&lang=css&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\MyPosts.vue", "mtime": 1748720502030}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubXktcG9zdHMtY29udGFpbmVyIHsKICAgIHBhZGRpbmc6IDIwcHg7Cn0KCi5wYWdlLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5wb3N0LWNhcmQgewogICAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLnBvc3QtaGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLnBvc3QtdGl0bGUgewogICAgZm9udC1zaXplOiAxOHB4OwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBjb2xvcjogIzMwMzEzMzsKICAgIGN1cnNvcjogcG9pbnRlcjsKfQoKLnBvc3QtY29udGVudCB7CiAgICBjb2xvcjogIzYwNjI2NjsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICBsaW5lLWhlaWdodDogMS41Owp9CgoucG9zdC1mb290ZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5wb3N0LWluZm8gewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMTVweDsKICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgZm9udC1zaXplOiAxNHB4Owp9CgoucG9zdC1hY3Rpb25zIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBnYXA6IDEwcHg7Cn0KCi5sb2FkaW5nLWNhcmQgewogICAgcGFkZGluZzogMjBweDsKfQoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIHBhZGRpbmctcmlnaHQ6IDIwcHg7Cn0K"}, {"version": 3, "sources": ["MyPosts.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "MyPosts.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"my-posts-container\">\n        <div class=\"page-header\">\n            <h2>我的帖子</h2>\n            <el-button type=\"primary\" @click=\"createPost\">发布帖子</el-button>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n                <el-tab-pane label=\"全部\" name=\"all\"></el-tab-pane>\n                <el-tab-pane label=\"已发布\" name=\"published\"></el-tab-pane>\n                <el-tab-pane label=\"待审核\" name=\"pending\"></el-tab-pane>\n                <el-tab-pane label=\"已拒绝\" name=\"rejected\"></el-tab-pane>\n            </el-tabs>\n\n            <el-card v-for=\"post in filteredPosts\" :key=\"post.id\" class=\"post-card\" shadow=\"hover\">\n                <div class=\"post-header\">\n                    <div class=\"post-title\" @click=\"viewPostDetail(post.id)\">{{ post.title }}</div>\n                    <div class=\"post-status\">\n                        <el-tag type=\"success\" v-if=\"post.status === 1\">已发布</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"post.status === 0\">待审核</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"post.status === 2\">已拒绝</el-tag>\n                    </div>\n                </div>\n                <div class=\"post-content\">{{ post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content }}</div>\n                <div class=\"post-footer\">\n                    <div class=\"post-info\">\n                        <span>发布时间: {{ formatDate(post.createTime) }}</span>\n                        <span v-if=\"post.updateTime\">更新时间: {{ formatDate(post.updateTime) }}</span>\n                        <span>\n                            <i class=\"el-icon-chat-dot-square\"></i> {{ post.commentCount }}\n                            <i class=\"el-icon-star-off\"></i> {{ post.likeCount }}\n                        </span>\n                    </div>\n                    <div class=\"post-actions\">\n                        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-edit\" @click=\"editPost(post.id)\">编辑</el-button>\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"deletePost(post.id)\">删除</el-button>\n                    </div>\n                </div>\n            </el-card>\n\n            <el-empty v-if=\"filteredPosts.length === 0\" description=\"暂无帖子\"></el-empty>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'MyPosts',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            posts: [],\n            loading: true,\n            activeTab: 'all',\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    computed: {\n        filteredPosts() {\n            if (this.activeTab === 'all') {\n                return this.posts;\n            } else if (this.activeTab === 'published') {\n                return this.posts.filter(post => post.status === 1);\n            } else if (this.activeTab === 'pending') {\n                return this.posts.filter(post => post.status === 0);\n            } else if (this.activeTab === 'rejected') {\n                return this.posts.filter(post => post.status === 2);\n            }\n            return this.posts;\n        }\n    },\n    created() {\n        this.fetchMyPosts();\n    },\n    methods: {\n        fetchMyPosts() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n\n            // 使用authorId参数来过滤当前用户的帖子\n            this.$get('/forum/post/list', {\n                pageNum: this.currentPage,\n                pageSize: this.pageSize,\n                userId: userId,\n                authorId: userId // 现在后端支持按作者ID过滤\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        // 不需要再进行前端过滤，因为后端已经过滤了\n                        this.posts = res.data.page.records;\n                        this.total = res.data.page.total;\n                        console.log('获取到我的帖子：', this.posts.length);\n                    } else {\n                        this.$message.error(res.data.msg || '获取帖子列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取帖子列表失败:', err);\n                    this.$message.error('获取帖子列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handleTabClick() {\n            // Tab切换时不需要重新加载数据，只需要通过计算属性过滤\n        },\n        createPost() {\n            this.$router.push('/home/<USER>/create');\n        },\n        viewPostDetail(id) {\n            this.$router.push(`/home/<USER>/post/${id}`);\n        },\n        editPost(id) {\n            this.$router.push(`/home/<USER>/edit/${id}`);\n        },\n        deletePost(id) {\n            this.$confirm('确定要删除这个帖子吗？此操作不可恢复', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n            }).then(() => {\n                const userId = JSON.parse(sessionStorage.getItem('user')).id;\n                this.$del(`/forum/post/${id}`, { operatorId: userId })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.fetchMyPosts(); // 重新加载帖子列表\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                    .catch(err => {\n                        console.error('删除帖子失败:', err);\n                        this.$message.error('删除帖子失败');\n                    });\n            }).catch(() => {\n                // 取消删除\n            });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        },\n        handlePaginationChange() {\n            this.fetchMyPosts();\n        }\n    }\n};\n</script>\n\n<style scoped>\n.my-posts-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.post-card {\n    margin-bottom: 15px;\n}\n\n.post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.post-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #303133;\n    cursor: pointer;\n}\n\n.post-content {\n    color: #606266;\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.post-info {\n    display: flex;\n    gap: 15px;\n    color: #909399;\n    font-size: 14px;\n}\n\n.post-actions {\n    display: flex;\n    gap: 10px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n</style>\n"]}]}