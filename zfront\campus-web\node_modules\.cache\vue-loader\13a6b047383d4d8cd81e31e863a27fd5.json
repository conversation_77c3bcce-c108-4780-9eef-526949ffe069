{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue", "mtime": 1748722940092}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGV9IGZyb20gInZ1ZXgiCmltcG9ydCB7Zm9ybWF0RGF0ZX0gZnJvbSAnQC91dGlsL2RhdGUnOwppbXBvcnQgeyBhZGRSZW1hcmssIH0gZnJvbSAiQC9hcGkvcmVtYXJrL3JlbWFyayI7CmltcG9ydCBDaGF0IGZyb20gJ0AvY29tcG9uZW50cy9DaGF0LnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiUHVibGlzaGVkIiwKICAgIGNvbXBvbmVudHM6IHsKICAgICAgICBDaGF0CiAgICB9LAogICAgZGF0YSgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgICAgIGFjdGl2ZU5hbWVzOiBbJzEnLCAnMicsICczJywgJzQnLCAnNScsICc2JywgJzcnXSwKICAgICAgICAgICAgZHJhd2VyTmFtZXM6IFsnMScsICcyJywgJzMnLCAnNCcsICc1J10sCiAgICAgICAgICAgIHRhc2tzOiBbXSwKICAgICAgICAgICAgZHJhd2VyOiBmYWxzZSwKICAgICAgICAgICAgcmVjaXBpZW50SW5mb3JtYXRpb246IFtdLAogICAgICAgICAgICAvLyDlvZPliY3pgInkuK3nmoTnirbmgIEKICAgICAgICAgICAgYWN0aXZlU3RhdHVzOiAnLTEnLAogICAgICAgICAgICAvLyDooajljZXlj4LmlbAKICAgICAgICAgICAgZm9ybToge30sCiAgICAgICAgICAgIC8vIOihqOWNleagoemqjAogICAgICAgICAgICBydWxlczogewogICAgICAgICAgICB9LAogICAgICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgICAgICAgdGl0bGU6ICIiLAogICAgICAgICAgICAvLyDlvZPliY3pgInkuK3nmoTku7vliqEKICAgICAgICAgICAgY3VycmVudFRhc2s6IG51bGwsCiAgICAgICAgICAgIC8vIOiBiuWkqeebuOWFswogICAgICAgICAgICBjaGF0VmlzaWJsZTogZmFsc2UsCiAgICAgICAgICAgIGN1cnJlbnRDaGF0VGFzazogbnVsbCwKICAgICAgICB9OwogICAgfSwKICAgIGNvbXB1dGVkOiB7CiAgICAgICAgLi4ubWFwU3RhdGUoJ3VzZXInLCBbJ3VzZXInXSksCgogICAgICAgIC8vIOaMieeKtuaAgeWIhue7hOeahOS7u+WKoQogICAgICAgIHRhc2tzQnlTdGF0dXMoKSB7CiAgICAgICAgICAgIC8vIOWumuS5ieeKtuaAgeaYoOWwhAogICAgICAgICAgICBjb25zdCBzdGF0dXNNYXAgPSB7CiAgICAgICAgICAgICAgICAnLTEnOiB7IG5hbWU6ICflhajpg6gnLCB0YXNrczogW10gfSwKICAgICAgICAgICAgICAgICcwJzogeyBuYW1lOiAn5b6F5o6l5Y2VJywgdGFza3M6IFtdIH0sCiAgICAgICAgICAgICAgICAnMSc6IHsgbmFtZTogJ+acjeWKoeS4rScsIHRhc2tzOiBbXSB9LAogICAgICAgICAgICAgICAgJzInOiB7IG5hbWU6ICflt7LlrozmiJAnLCB0YXNrczogW10gfQogICAgICAgICAgICB9OwoKICAgICAgICAgICAgLy8g5re75YqgIuWFqOmDqCLliIbnsbsKICAgICAgICAgICAgc3RhdHVzTWFwWyctMSddLnRhc2tzID0gdGhpcy50YXNrczsKCiAgICAgICAgICAgIC8vIOaMieeKtuaAgeWIhue7hAogICAgICAgICAgICB0aGlzLnRhc2tzLmZvckVhY2godGFzayA9PiB7CiAgICAgICAgICAgICAgICBjb25zdCBzdGF0ZSA9IHRhc2suc3RhdGUgIT09IG51bGwgJiYgdGFzay5zdGF0ZSAhPT0gdW5kZWZpbmVkID8gdGFzay5zdGF0ZS50b1N0cmluZygpIDogJzAnOwogICAgICAgICAgICAgICAgaWYgKHN0YXR1c01hcFtzdGF0ZV0pIHsKICAgICAgICAgICAgICAgICAgICBzdGF0dXNNYXBbc3RhdGVdLnRhc2tzLnB1c2godGFzayk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgLy8g6L2s5o2i5Li65pWw57uE5qC85byP77yM5pa55L6/5Zyo5qih5p2/5Lit5L2/55SoCiAgICAgICAgICAgIHJldHVybiBPYmplY3QuZW50cmllcyhzdGF0dXNNYXApLm1hcCgoW3N0YXR1cywgZGF0YV0pID0+ICh7CiAgICAgICAgICAgICAgICBzdGF0dXM6IHBhcnNlSW50KHN0YXR1cyksCiAgICAgICAgICAgICAgICBzdGF0dXNOYW1lOiBkYXRhLm5hbWUsCiAgICAgICAgICAgICAgICB0YXNrczogZGF0YS50YXNrcwogICAgICAgICAgICB9KSk7CiAgICAgICAgfQogICAgfSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgdGhpcy5yZXRyaWV2ZURhdGEoKQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgICByZXRyaWV2ZURhdGEoKSB7CiAgICAgICAgICAgIHRoaXMuJGdldCgiL3Rhc2svcHVibGlzaGVkIiwge2lkOiB0aGlzLnVzZXIuaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuZGF0YS50YXNrKQogICAgICAgICAgICAgICAgdGhpcy50YXNrcyA9IHJlcy5kYXRhLnRhc2sKICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIHJlY2VpdmVyKHZhbCkgewogICAgICAgICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgICAgICAgIHRoaXMucmVjaXBpZW50SW5mb3JtYXRpb24gPSB2YWwuYWNjZXB0OwogICAgICAgICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLnJlY2lwaWVudEluZm9ybWF0aW9uKQogICAgICAgICAgICB0aGlzLmRyYXdlciA9IHRydWUKICAgICAgICB9LAoKICAgICAgICB0cmFuc2Zvcm0odGltZSkgewogICAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKHRpbWUpOwogICAgICAgICAgICByZXR1cm4gZm9ybWF0RGF0ZShkYXRlLCAneXl5eS1NTS1kZCBoaDptbScpOwogICAgICAgIH0sCgogICAgICAgIGNhbmNlbChpZCkgewogICAgICAgICAgICB0aGlzLiRkZWwoIi90YXNrLyIgKyBpZCkKICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXRyaWV2ZURhdGEoKQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygn5oiQ5YqfJywgcmVzLmRhdGEubXNnLCAic3VjY2VzcyIpOwogICAgICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIGNvbXBsZXRlVGFzayhpZCkgewogICAgICAgICAgICB0aGlzLiRtc2dib3goewogICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ehruWumuivpee7tOS/ruWRmOWujOaIkOatpOS7u+WKoeS6huWQl++8nycsCiAgICAgICAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiB0cnVlLAogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBiZWZvcmVDbG9zZTogKChhY3Rpb24sIGluc3RhbmNlLCBkb25lKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgaWYgKGFjdGlvbiA9PSAnY29uZmlybScpIHsKICAgICAgICAgICAgICAgICAgICAgICAgLy8gaW5zdGFuY2UuY29uZmlybUJ1dHRvbkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgICBpbnN0YW5jZS5jb25maXJtQnV0dG9uVGV4dCA9ICfmiafooYzkuK0uLi4nOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRwdXQoJ3Rhc2svJyArIGlkKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvbmUoKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnN0YW5jZS5jb25maXJtQnV0dG9uTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZyhyZXMuZGF0YS5tc2csICJzdWNjZXNzIik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXRyaWV2ZURhdGEoKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c6L+U5Zue5LqG5pu05paw5ZCO55qE55So5oi35L+h5oGv77yM5pu05paw5b2T5YmN55So5oi35pWw5o2uCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmRhdGEgJiYgcmVzLmRhdGEuZGF0YS5pZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmo4Dmn6Xov5Tlm57nmoTnlKjmiLfmmK/lkKbmmK/lvZPliY3nmbvlvZXnlKjmiLcKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmRhdGEuaWQgPT09IHRoaXMudXNlci5pZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+abtOaWsOW9k+WJjeeUqOaIt+S9memineS/oeaBrzonLCByZXMuZGF0YS5kYXRhKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgndXNlci9zZXRVc2VyJywgcmVzLmRhdGEuZGF0YSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGRvbmUoKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICAvLyDor4Tku7fnlKjmiLcKICAgICAgICByZW1hcmsodGFzayl7CiAgICAgICAgICAgIHRoaXMuY3VycmVudFRhc2sgPSB0YXNrOwogICAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOivhOS7tyI7CiAgICAgICAgfSwKCiAgICAgICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgICAgICAgIGlmKHRoaXMuZm9ybS5zdGFyPT1udWxsKXsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoIuivt+i+k+WFpeaYn+e6pyIpOwogICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CgogICAgICAgICAgICBpZih0aGlzLmZvcm0ucmVtYXJrPT1udWxsIHx8IHRoaXMuZm9ybS5yZW1hcmsudHJpbSgpID09PSAnJyl7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKCLor7fovpPlhaXor4Tku7flhoXlrrkiKTsKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgaWYoIXRoaXMuY3VycmVudFRhc2sgfHwgIXRoaXMuY3VycmVudFRhc2suYWNjZXB0KXsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS7u+WKoeS/oeaBr+S4jeWujOaVtO+8jOaXoOazleaPkOS6pOivhOS7tyIpOwogICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CgogICAgICAgICAgICBjb25zdCBhaWQgPSB0aGlzLmN1cnJlbnRUYXNrLmFjY2VwdC5pZDsKICAgICAgICAgICAgY29uc3QgdGFza2lkID0gdGhpcy5jdXJyZW50VGFzay5pZDsKICAgICAgICAgICAgY29uc3QgcGlkID0gdGhpcy5jdXJyZW50VGFzay5wdWJsaXNoLmlkOwoKICAgICAgICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOivhOS7tzonLCBhaWQsIHRhc2tpZCwgcGlkKTsKCiAgICAgICAgICAgIGFkZFJlbWFyayh7CiAgICAgICAgICAgICAgICAic3RhciI6IHRoaXMuZm9ybS5zdGFyLAogICAgICAgICAgICAgICAgInJlbWFyayI6IHRoaXMuZm9ybS5yZW1hcmssCiAgICAgICAgICAgICAgICAiYWNjZXB0SWQiOiBhaWQsCiAgICAgICAgICAgICAgICAicHVibGlzaElkIjogcGlkLAogICAgICAgICAgICAgICAgInRhc2tJZCI6IHRhc2tpZCwKICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuivhOS7t+aPkOS6pOaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLnJlc2V0KCk7CiAgICAgICAgICAgICAgICB0aGlzLnJldHJpZXZlRGF0YSgpOyAvLyDliLfmlrDku7vliqHliJfooagKICAgICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6K+E5Lu35o+Q5Lqk5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivhOS7t+aPkOS6pOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAoKICAgICAgICAvLyDlj5bmtojmjInpkq4KICAgICAgICBleGl0KCkgewogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5yZXNldCgpOwogICAgICAgICAgICB0aGlzLmN1cnJlbnRUYXNrID0gbnVsbDsKICAgICAgICB9LAoKICAgICAgICAvLyDooajljZXph43nva4KICAgICAgICByZXNldCgpIHsKICAgICAgICAgICAgdGhpcy5mb3JtID0gewogICAgICAgICAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgICAgICAgICBzdGFyOiBudWxsLAogICAgICAgICAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgICAgICB9OwogICAgICAgIH0sCgogICAgICAgIC8qKgogICAgICAgICAqIOaJk+W8gOWcqOe6v+S6pOa1gQogICAgICAgICAqIEBwYXJhbSB7T2JqZWN0fSB0YXNrIOS7u+WKoeWvueixoQogICAgICAgICAqLwogICAgICAgIG9wZW5DaGF0KHRhc2spIHsKICAgICAgICAgICAgdGhpcy5jdXJyZW50Q2hhdFRhc2sgPSB0YXNrOwogICAgICAgICAgICB0aGlzLmNoYXRWaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9LAogICAgfSwKICAgIGZpbHRlcnM6IHsKICAgICAgICBmb3JtYXREYXRlKHRpbWUpIHsKICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsKICAgICAgICAgICAgcmV0dXJuIGZvcm1hdERhdGUoZGF0ZSwgJ3l5eXktTU0tZGQgaGg6bW0nKTsKICAgICAgICB9CiAgICB9Cn0K"}, {"version": 3, "sources": ["Published.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8JA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Published.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>已发布任务</span>\n            </div>\n\n            <!-- 状态分类标签 -->\n            <div class=\"status-tabs\">\n                <el-tabs v-model=\"activeStatus\" type=\"card\">\n                    <el-tab-pane\n                        v-for=\"group in tasksByStatus\"\n                        :key=\"group.status\"\n                        :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                        :name=\"group.status.toString()\"\n                    >\n                        <el-card\n                            class=\"box-card\"\n                            v-for=\"item in group.tasks\"\n                            :key=\"item.id\"\n                            style=\"margin-top: 20px\"\n                        >\n                <div slot=\"header\" class=\"clearfix\"\n                     style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span style=\"display: flex;align-items: center\">\n                            <el-tag :type=\"item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')\"\n                                    style=\"margin-right: 5px\">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>\n                            {{item.taskTitle}}\n                        </span>\n\n                    <!-- 评价按钮 -->\n                    <el-button v-show=\"item.state == 2\"\n                    style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"remark(item)\">订单评价</el-button>\n\n                    <!-- 在线交流按钮 -->\n                    <el-button v-show=\"item.state == 1\"\n                    style=\"float: right; padding: 3px 0; margin-right: 10px\" type=\"text\" @click=\"openChat(item)\">\n                        <i class=\"el-icon-chat-dot-round\"></i> 在线交流\n                    </el-button>\n\n                    <el-button style=\"float: right; padding: 3px 0\" type=\"text\" v-show=\"item.state != 0\"\n                               @click=\"receiver(item)\">查看维修员信息\n                    </el-button>\n                    <template>\n<!--                        <i class=\"el-icon-edit\" style=\"cursor: pointer; color: #66b1ff\" v-show=\"item.state == 0\"/>-->\n                        <el-popconfirm title=\"确定取消任务吗？\" @confirm=\"cancel(item.id)\" v-show=\"item.state == 0\">\n                            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" slot=\"reference\">取消任务\n                            </el-button>\n                        </el-popconfirm>\n                    </template>\n                </div>\n\n                <el-steps :active=\"item.state + 1\" finish-status=\"success\">\n                    <el-step title=\"发布成功\" :description=\"item.createTime | formatDate\"></el-step>\n                    <el-step title=\"服务中\" :description=\"item.orderTime ? transform(item.orderTime):'暂时没人服务'\"></el-step>\n                    <el-step title=\"完成时间\" :description=\"item.endTime ? transform(item.endTime):''\"></el-step>\n                </el-steps>\n\n                <el-collapse style=\"margin-top: 20px\" v-model=\"activeNames\">\n                    <el-collapse-item title=\"任务内容\" name=\"1\">\n                        <div>{{item.taskContext}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"任务金额\" name=\"2\">\n                        <div><i class=\"el-icon-money\" style=\"color: red;\"> {{item.reward}}元</i></div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"发布时间\" name=\"3\">\n                        <div>{{item.createTime | formatDate}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"类别\" name=\"4\">\n                        <div>{{item.dept ? item.dept.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"子类别\" name=\"5\">\n                        <div>{{item.type ? item.type.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"地址\" name=\"6\" v-if=\"item.province\">\n                        <div>{{item.province}} {{item.city}} {{item.district}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"详细地址\" name=\"7\" v-if=\"item.address\">\n                        <div>{{item.address}}</div>\n                    </el-collapse-item>\n                </el-collapse>\n\n                <el-button type=\"primary\" style=\"float: right;margin:10px 0;\" @click=\"completeTask(item.id)\"\n                           v-show=\"item.state==1\">完成任务\n                </el-button>\n\n                        </el-card>\n\n                        <!-- 当前状态下没有任务时显示 -->\n                        <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                            <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无发布任务</span>\n                        </div>\n                    </el-tab-pane>\n                </el-tabs>\n            </div>\n\n            <!-- 没有任何任务时显示 -->\n            <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                <span><i class=\"el-icon-refresh-right\"></i>暂无发布任务</span>\n            </div>\n        </el-card>\n\n        <el-drawer\n                title=\"维修员信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\">\n            <div class=\"content_drawer\">\n                <el-card class=\"box-card\" v-if=\"recipientInformation != ''\">\n                    <el-collapse v-model=\"drawerNames\">\n                        <el-collapse-item title=\"姓名\" name=\"1\">\n                            <div>{{recipientInformation.username}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"电话\" name=\"2\">\n                            <div>{{recipientInformation.phone}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"角色\" name=\"3\">\n                            <div>{{recipientInformation.role.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"类别\" name=\"4\">\n                            <div>{{recipientInformation.dept.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"子类别\" name=\"5\">\n                            <div>{{recipientInformation.type.name}}</div>\n                        </el-collapse-item>\n                    </el-collapse>\n                </el-card>\n            </div>\n        </el-drawer>\n\n        <!-- 添加或修改remark对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\"  :rules=\"rules\" label-width=\"80px\" >\n            <el-form-item label=\"星级\" prop=\"star\">\n                <el-rate\n                    v-model=\"form.star\"\n                    show-text>\n                </el-rate>\n            </el-form-item>\n            <el-form-item label=\"评价内容\" prop=\"remark\">\n            <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n            </el-form-item>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"exit\">取 消</el-button>\n        </div>\n        </el-dialog>\n\n        <!-- 聊天组件 -->\n        <Chat\n            :visible.sync=\"chatVisible\"\n            :task=\"currentChatTask\"\n            :current-user-id=\"user.id\"\n            v-if=\"currentChatTask\" />\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\"\n    import {formatDate} from '@/util/date';\n    import { addRemark, } from \"@/api/remark/remark\";\n    import Chat from '@/components/Chat.vue';\n\n    export default {\n        name: \"Published\",\n        components: {\n            Chat\n        },\n        data() {\n            return {\n                // 是否显示弹出层\n                open: false,\n                activeNames: ['1', '2', '3', '4', '5', '6', '7'],\n                drawerNames: ['1', '2', '3', '4', '5'],\n                tasks: [],\n                drawer: false,\n                recipientInformation: [],\n                // 当前选中的状态\n                activeStatus: '-1',\n                // 表单参数\n                form: {},\n                // 表单校验\n                rules: {\n                },\n                // 弹出层标题\n                title: \"\",\n                // 当前选中的任务\n                currentTask: null,\n                // 聊天相关\n                chatVisible: false,\n                currentChatTask: null,\n            };\n        },\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    '0': { name: '待接单', tasks: [] },\n                    '1': { name: '服务中', tasks: [] },\n                    '2': { name: '已完成', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.tasks;\n\n                // 按状态分组\n                this.tasks.forEach(task => {\n                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '0';\n                    if (statusMap[state]) {\n                        statusMap[state].tasks.push(task);\n                    }\n                });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.retrieveData()\n        },\n        methods: {\n            retrieveData() {\n                this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n                    console.log(res.data.task)\n                    this.tasks = res.data.task\n                })\n            },\n            receiver(val) {\n                console.log(val)\n                this.recipientInformation = val.accept;\n                // console.log(this.recipientInformation)\n                this.drawer = true\n            },\n\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n\n            cancel(id) {\n                this.$del(\"/task/\" + id)\n                    .then(res => {\n                        this.retrieveData()\n                        this.$notifyMsg('成功', res.data.msg, \"success\");\n                    })\n            },\n            completeTask(id) {\n                this.$msgbox({\n                    title: '提示',\n                    message: '确定该维修员完成此任务了吗？',\n                    showCancelButton: true,\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    beforeClose: ((action, instance, done) => {\n                        if (action == 'confirm') {\n                            // instance.confirmButtonLoading = true;\n                            instance.confirmButtonText = '执行中...';\n                            this.$put('task/' + id)\n                                .then((res) => {\n                                    done();\n                                    instance.confirmButtonLoading = false;\n                                    this.$msg(res.data.msg, \"success\");\n                                    this.retrieveData();\n\n                                    // 如果返回了更新后的用户信息，更新当前用户数据\n                                    if (res.data.data && res.data.data.id) {\n                                        // 检查返回的用户是否是当前登录用户\n                                        if (res.data.data.id === this.user.id) {\n                                            console.log('更新当前用户余额信息:', res.data.data);\n                                            this.$store.commit('user/setUser', res.data.data);\n                                        }\n                                    }\n                                })\n                        } else {\n                            done();\n                        }\n                    })\n                }).catch(() => {\n                })\n            },\n            // 评价用户\n            remark(task){\n                this.currentTask = task;\n                this.open = true;\n                this.title = \"添加评价\";\n            },\n\n            /** 提交按钮 */\n            submitForm() {\n                if(this.form.star==null){\n                    this.$message(\"请输入星级\");\n                    return;\n                }\n\n                if(this.form.remark==null || this.form.remark.trim() === ''){\n                    this.$message(\"请输入评价内容\");\n                    return;\n                }\n\n                if(!this.currentTask || !this.currentTask.accept){\n                    this.$message.error(\"任务信息不完整，无法提交评价\");\n                    return;\n                }\n\n                const aid = this.currentTask.accept.id;\n                const taskid = this.currentTask.id;\n                const pid = this.currentTask.publish.id;\n\n                console.log('提交评价:', aid, taskid, pid);\n\n                addRemark({\n                    \"star\": this.form.star,\n                    \"remark\": this.form.remark,\n                    \"acceptId\": aid,\n                    \"publishId\": pid,\n                    \"taskId\": taskid,\n                }).then(() => {\n                    this.$message.success(\"评价提交成功\");\n                    this.open = false;\n                    this.reset();\n                    this.retrieveData(); // 刷新任务列表\n                }).catch(error => {\n                    console.error('评价提交失败:', error);\n                    this.$message.error(\"评价提交失败，请稍后重试\");\n                });\n            },\n\n            // 取消按钮\n            exit() {\n                this.open = false;\n                this.reset();\n                this.currentTask = null;\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    star: null,\n                    remark: null,\n                };\n            },\n\n            /**\n             * 打开在线交流\n             * @param {Object} task 任务对象\n             */\n            openChat(task) {\n                this.currentChatTask = task;\n                this.chatVisible = true;\n            },\n        },\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n    }\n\n    .status-tabs {\n        margin-bottom: 20px;\n\n        /deep/ .el-tabs__header {\n            margin-bottom: 15px;\n        }\n\n        /deep/ .el-tabs__item {\n            height: 40px;\n            line-height: 40px;\n            font-size: 14px;\n            color: #606266;\n\n            &.is-active {\n                color: #409EFF;\n                font-weight: bold;\n            }\n        }\n    }\n</style>\n"]}]}