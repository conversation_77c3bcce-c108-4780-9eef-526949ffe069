{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Index.vue", "mtime": 1746178617679}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovZW5kaW5nLzI1MDQyNi96ZnJvbnQvY2FtcHVzLXdlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMiI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCmltcG9ydCB7IG1hcFN0YXRlIH0gZnJvbSAidnVleCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSW5kZXgiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gey8vIOS4jeWGjemcgOimgeWtmOWCqOS7u+WKoeWSjOeUqOaIt+aVsOaNrgogICAgfTsKICB9LAogIG1ldGhvZHM6IHt9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKHt9LCBtYXBTdGF0ZSgndXNlcicsIFsndXNlciddKSksCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsvLyDkuI3lho3pnIDopoHojrflj5bku7vliqHlkoznlKjmiLfmlbDmja4KICB9Cn07"}, {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,SAAA,QAAA,QAAA,MAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA,CACA;AADA,KAAA;AAGA,GANA;AAOA,EAAA,OAAA,EAAA,EAPA;AASA,EAAA,QAAA,oBACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA,CATA;AAYA,EAAA,OAZA,qBAYA,CACA;AACA;AAdA,CAAA", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-row>\n        <el-col :span=\"24\"><div class=\"grid-content bg-purple-dark\">欢迎登录---售后维修管理系统</div></el-col>\n        </el-row>\n        <el-alert\n                :title=\"user.role.name + '- - -欢迎使用售后维修管理系统'\"\n                :closable=\"false\"\n                type=\"info\">\n        </el-alert>\n        <div class=\"welcome-image\">\n            <img src=\"@/assets/img/matiner.jpg\" alt=\"维修员图片\">\n        </div>\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\";\n\n    export default {\n        name: \"Index\",\n        data() {\n            return {\n                // 不再需要存储任务和用户数据\n            }\n        },\n        methods: {\n        },\n        computed: {\n            ...mapState('user', ['user'])\n        },\n        created() {\n            // 不再需要获取任务和用户数据\n        },\n\n    }\n</script>\n\n<style scoped lang=\"less\">\n\n    .el-row {\n        margin-bottom: 20px;\n        &:last-child {\n        margin-bottom: 0;\n        }\n    }\n    .el-col {\n        border-radius: 4px;\n    }\n\n      .bg-purple {\n    background: #d3dce6;\n  }\n\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n\n        .card h2 {\n            font-weight: normal;\n            font-size: 18px;\n\n            span {\n                font-size: 12px;\n                display: inline-block;\n                border: 1px solid red;\n                padding: 1px 3px;\n            }\n        }\n\n        .welcome-image {\n            margin: 20px auto;\n            text-align: center;\n            height: calc(100vh - 200px); /* 计算高度，减去顶部和底部的空间 */\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            img {\n                width: 100%;\n                height: 100%;\n                object-fit: contain; /* 保持图片比例，确保完全显示 */\n                border-radius: 8px;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n                transition: transform 0.3s ease;\n\n                &:hover {\n                    transform: scale(1.02);\n                }\n            }\n        }\n\n    }\n\n</style>"], "sourceRoot": "src/views/user/children"}]}