{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue?vue&type=template&id=08cdd44b&scoped=true&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue", "mtime": 1748720502030}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}