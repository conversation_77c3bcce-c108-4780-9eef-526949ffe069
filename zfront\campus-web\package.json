{"name": "maintain_help", "version": "0.1.0", "private": true, "scripts": {"serve": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "boqii-modal": "^1.1.4", "core-js": "^3.6.5", "echarts": "^5.6.0", "element-ui": "^2.15.8", "js-md5": "^0.7.3", "nprogress": "^0.2.0", "sockjs-client": "^1.5.2", "stompjs": "^2.3.3", "vant": "^2.12.18", "vue": "^2.6.11", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}