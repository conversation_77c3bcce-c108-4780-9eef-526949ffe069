{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\forum\\Notifications.vue", "mtime": 1748720501687}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Notifications.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Notifications.vue", "sourceRoot": "src/views/forum", "sourcesContent": ["<template>\n    <div class=\"notifications-container\">\n        <div class=\"page-header\">\n            <h2>消息通知</h2>\n            <div class=\"header-actions\">\n                <el-button type=\"text\" @click=\"markAllAsRead\" :disabled=\"notifications.length === 0\">全部标为已读</el-button>\n            </div>\n        </div>\n\n        <el-card v-if=\"loading\" class=\"loading-card\">\n            <el-skeleton :rows=\"10\" animated />\n        </el-card>\n\n        <div v-else>\n            <el-card v-for=\"notification in notifications\" :key=\"notification.id\" class=\"notification-card\" :class=\"{ 'unread': !notification.isRead }\">\n                <div class=\"notification-header\">\n                    <div class=\"notification-type\">\n                        <el-tag type=\"primary\" v-if=\"notification.type === 1\">点赞</el-tag>\n                        <el-tag type=\"success\" v-else-if=\"notification.type === 2\">评论</el-tag>\n                        <el-tag type=\"danger\" v-else-if=\"notification.type === 3\">删除</el-tag>\n                        <el-tag type=\"warning\" v-else-if=\"notification.type === 4\">审核未通过</el-tag>\n                    </div>\n                    <div class=\"notification-time\">{{ formatDate(notification.createTime) }}</div>\n                </div>\n                <div class=\"notification-content\">{{ notification.content }}</div>\n                <div class=\"notification-actions\">\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"viewRelatedPost(notification)\"\n                        v-if=\"notification.type !== 3\"\n                    >\n                        查看详情\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"markAsRead(notification)\"\n                        v-if=\"!notification.isRead\"\n                    >\n                        标为已读\n                    </el-button>\n                    <el-button \n                        size=\"mini\" \n                        type=\"text\" \n                        @click=\"deleteNotification(notification.id)\"\n                    >\n                        删除\n                    </el-button>\n                </div>\n            </el-card>\n\n            <div class=\"pagination-container\">\n                <common-pagination\n                    :total=\"total\"\n                    :current-page.sync=\"currentPage\"\n                    :page-size.sync=\"pageSize\"\n                    @pagination-change=\"handlePaginationChange\">\n                </common-pagination>\n            </div>\n\n            <el-empty v-if=\"notifications.length === 0\" description=\"暂无通知\"></el-empty>\n        </div>\n    </div>\n</template>\n\n<script>\nimport CommonPagination from '@/components/CommonPagination.vue';\n\nexport default {\n    name: 'Notifications',\n    components: {\n        CommonPagination\n    },\n    data() {\n        return {\n            notifications: [],\n            loading: true,\n            currentPage: 1,\n            pageSize: 10,\n            total: 0\n        };\n    },\n    created() {\n        this.fetchNotifications();\n    },\n    methods: {\n        fetchNotifications() {\n            this.loading = true;\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$get('/forum/notification/list', {\n                userId: userId,\n                pageNum: this.currentPage,\n                pageSize: this.pageSize\n            })\n                .then(res => {\n                    if (res.data.status) {\n                        this.notifications = res.data.page.records;\n                        this.total = res.data.page.total;\n                    } else {\n                        this.$message.error(res.data.msg || '获取通知列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取通知列表失败:', err);\n                    this.$message.error('获取通知列表失败');\n                })\n                .finally(() => {\n                    this.loading = false;\n                });\n        },\n        handlePaginationChange() {\n            this.fetchNotifications();\n        },\n        viewRelatedPost(notification) {\n            if (notification.relatedId && notification.type !== 3) {\n                // 如果是点赞或评论通知，跳转到相关帖子\n                this.$router.push(`/home/<USER>/post/${notification.relatedId}`);\n                \n                // 如果通知未读，标记为已读\n                if (!notification.isRead) {\n                    this.markAsRead(notification);\n                }\n            }\n        },\n        markAsRead(notification) {\n            this.$put(`/forum/notification/read/${notification.id}`)\n                .then(res => {\n                    if (res.data.status) {\n                        notification.isRead = true;\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '标记已读失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记已读失败:', err);\n                    this.$message.error('标记已读失败');\n                });\n        },\n        markAllAsRead() {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$put('/forum/notification/read/all', { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('全部标记为已读');\n                        // 更新通知状态\n                        this.notifications.forEach(notification => {\n                            notification.isRead = true;\n                        });\n                        // 更新未读通知数量\n                        this.$parent.getUnreadNotificationCount();\n                    } else {\n                        this.$message.error(res.data.msg || '操作失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('标记全部已读失败:', err);\n                    this.$message.error('标记全部已读失败');\n                });\n        },\n        deleteNotification(id) {\n            const userId = JSON.parse(sessionStorage.getItem('user')).id;\n            \n            this.$del(`/forum/notification/${id}`, { userId })\n                .then(res => {\n                    if (res.data.status) {\n                        this.$message.success('删除成功');\n                        // 从列表中移除\n                        this.notifications = this.notifications.filter(n => n.id !== id);\n                        // 如果当前页没有数据了，且不是第一页，则跳转到上一页\n                        if (this.notifications.length === 0 && this.currentPage > 1) {\n                            this.currentPage--;\n                            this.fetchNotifications();\n                        }\n                    } else {\n                        this.$message.error(res.data.msg || '删除失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('删除通知失败:', err);\n                    this.$message.error('删除通知失败');\n                });\n        },\n        formatDate(dateStr) {\n            if (!dateStr) return '';\n            const date = new Date(dateStr);\n            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n        }\n    }\n};\n</script>\n\n<style scoped>\n.notifications-container {\n    padding: 20px;\n}\n\n.page-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n}\n\n.notification-card {\n    margin-bottom: 15px;\n    transition: background-color 0.3s;\n}\n\n.notification-card.unread {\n    background-color: #f0f9ff;\n}\n\n.notification-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 10px;\n}\n\n.notification-content {\n    margin-bottom: 10px;\n    line-height: 1.5;\n}\n\n.notification-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n}\n\n.pagination-container {\n    margin-top: 20px;\n    display: flex;\n    justify-content: flex-end;\n    padding-right: 20px;\n}\n\n.loading-card {\n    padding: 20px;\n}\n</style>\n"]}]}