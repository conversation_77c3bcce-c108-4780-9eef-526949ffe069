package com.yqn.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yqn.pojo.ChatMessage;

import java.util.List;

/**
 * 聊天服务接口
 * 
 * <AUTHOR>
 */
public interface ChatService extends IService<ChatMessage> {
    
    /**
     * 保存聊天消息
     * @param chatMessage 聊天消息
     * @return 是否保存成功
     */
    boolean saveMessage(ChatMessage chatMessage);
    
    /**
     * 获取聊天历史记录
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 聊天历史记录
     */
    List<ChatMessage> getChatHistory(Long taskId, Long userId);
    
    /**
     * 标记消息为已读
     * @param messageIds 消息ID列表
     * @return 是否操作成功
     */
    boolean markMessagesAsRead(List<Long> messageIds);
    
    /**
     * 获取用户未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getUnreadCount(Long userId);
    
    /**
     * 获取任务相关的聊天参与者
     * @param taskId 任务ID
     * @return 参与者用户ID列表
     */
    List<Long> getChatParticipants(Long taskId);
}
