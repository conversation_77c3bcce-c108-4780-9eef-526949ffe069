{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue", "mtime": 1748752648774}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Chat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,OAAA,MAAA,MAAA,eAAA;AACA,OAAA,KAAA,MAAA,SAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA,KALA;AASA,IAAA,aAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA;AATA,GAFA;AAgBA,EAAA,IAhBA,kBAgBA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,KAFA;AAGA,MAAA,QAAA,EAAA,EAHA;AAIA,MAAA,UAAA,EAAA,EAJA;AAKA,MAAA,YAAA,EAAA;AALA,KAAA;AAOA,GAxBA;AAyBA,EAAA,QAAA,EAAA;AACA,IAAA,WADA,yBACA;AACA,UAAA,CAAA,KAAA,IAAA,EAAA,OAAA,MAAA;AACA,kDAAA,KAAA,IAAA,CAAA,SAAA;AACA,KAJA;AAMA;AACA,IAAA,WAPA,yBAOA;AACA,UAAA,CAAA,KAAA,IAAA,EAAA,OAAA,IAAA,CADA,CAGA;;AACA,UAAA,KAAA,aAAA,KAAA,KAAA,IAAA,CAAA,SAAA,EAAA;AACA,eAAA,KAAA,IAAA,CAAA,QAAA;AACA,OAFA,CAGA;AAHA,WAIA,IAAA,KAAA,aAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,KAAA,IAAA,CAAA,SAAA;AACA;;AAEA,aAAA,IAAA;AACA;AApBA,GAzBA;AA+CA,EAAA,KAAA,EAAA;AACA,IAAA,OADA,mBACA,MADA,EACA;AACA,UAAA,MAAA,EAAA;AACA,aAAA,QAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA;AACA;AACA;AAPA,GA/CA;AAwDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,sBAEA;AACA,WAAA,gBAAA;AACA,WAAA,eAAA;AACA,KALA;AAOA;AACA,IAAA,SARA,uBAQA;AACA,WAAA,mBAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,EAAA;AACA,KAZA;AAcA;AACA,IAAA,gBAfA,8BAeA;AAAA;;AACA,UAAA,KAAA,WAAA,IAAA,KAAA,SAAA,EAAA;AACA;AACA;;AAEA,UAAA;AACA,YAAA,MAAA,GAAA,IAAA,MAAA,CAAA,SAAA,CAAA;AACA,aAAA,WAAA,GAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAFA,CAIA;;AACA,aAAA,WAAA,CAAA,KAAA,GAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,GAAA;AACA,SAFA;;AAIA,aAAA,WAAA,CAAA,OAAA,CAAA,EAAA,EACA,UAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,KAAA;AACA,UAAA,KAAA,CAAA,SAAA,GAAA,IAAA,CAFA,CAIA;;AACA,UAAA,KAAA,CAAA,YAAA,GAAA,KAAA,CAAA,WAAA,CAAA,SAAA,iBACA,KAAA,CAAA,aADA,sBAEA,UAAA,OAAA,EAAA;AACA,gBAAA,WAAA,GAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA;;AACA,YAAA,KAAA,CAAA,qBAAA,CAAA,WAAA;AACA,WALA,CAAA,CALA,CAaA;;AACA,UAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,QAAA,EAAA,KAAA,CAAA,aADA;AAEA,YAAA,MAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AAFA,WAAA,CAAA;AAIA,SAnBA,EAoBA,UAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA,UAAA,KAAA,CAAA,SAAA,GAAA,KAAA;;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,SAxBA;AA0BA,OAnCA,CAmCA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,KA3DA;AA6DA;AACA,IAAA,mBA9DA,iCA8DA;AACA,UAAA,KAAA,YAAA,EAAA;AACA,aAAA,YAAA,CAAA,WAAA;AACA,aAAA,YAAA,GAAA,IAAA;AACA;;AAEA,UAAA,KAAA,WAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AACA,SAFA;AAGA;;AAEA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,KA5EA;AA8EA;AACA,IAAA,WA/EA,yBA+EA;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,SAAA,IAAA,CAAA,KAAA,WAAA,EAAA;AACA;AACA;;AAEA,UAAA,OAAA,GAAA;AACA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,KAAA,aAFA;AAGA,QAAA,UAAA,EAAA,KAAA,WAHA;AAIA,QAAA,OAAA,EAAA,KAAA,UAAA,CAAA,IAAA,EAJA;AAKA,QAAA,WAAA,EAAA;AALA,OAAA;;AAQA,UAAA;AACA,aAAA,WAAA,CAAA,IAAA,CAAA,uBAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA;AACA,aAAA,UAAA,GAAA,EAAA;AACA,OAHA,CAGA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA;AACA,KAnGA;AAqGA;AACA,IAAA,qBAtGA,iCAsGA,OAtGA,EAsGA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,EADA,CAGA;;AACA,UAAA,OAAA,CAAA,MAAA,KAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,EAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,EAAA,KAAA,OAAA,CAAA,EAAA;AAAA,WAAA,CAAA;AACA;;AAEA,YAAA,CAAA,MAAA,EAAA;AACA;AACA,cAAA,CAAA,OAAA,CAAA,QAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,GAAA,IAAA,IAAA,EAAA;AACA;;AAEA,eAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,KAAA,QAAA,CAAA,MAAA;AAEA,eAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,cAAA;AACA,WAFA;AAGA;AACA;AACA,KA/HA;AAiIA;AACA,IAAA,eAlIA,6BAkIA;AAAA;;AACA,WAAA,IAAA,CAAA,mBAAA,EAAA;AACA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,MAAA,EAAA,KAAA;AAFA,OAAA,EAGA,IAHA,CAGA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,QAAA,IAAA,EAAA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,cAAA;AACA,WAFA;AAGA,SALA,MAKA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAZA,EAYA,KAZA,CAYA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,OAdA;AAeA,KAlJA;AAoJA;AACA,IAAA,cArJA,4BAqJA;AACA,UAAA,SAAA,GAAA,KAAA,KAAA,CAAA,iBAAA;;AACA,UAAA,SAAA,EAAA;AACA,QAAA,SAAA,CAAA,SAAA,GAAA,SAAA,CAAA,YAAA;AACA;AACA,KA1JA;AA4JA;AACA,IAAA,cA7JA,0BA6JA,KA7JA,EA6JA;AACA,UAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AACA;AACA;AACA,OAHA,MAGA;AACA;AACA,QAAA,KAAA,CAAA,cAAA;AACA,aAAA,WAAA;AACA;AACA,KAtKA;AAwKA;AACA,IAAA,UAzKA,sBAyKA,IAzKA,EAyKA;AACA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA;AAEA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CALA,CAOA;;AACA,UAAA,IAAA,GAAA,KAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,CAAA,OAAA,OAAA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,kBAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,MAAA,EAAA;AAFA,SAAA,CAAA;AAIA,OAbA,CAeA;;;AACA,aAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAAA,CAAA;AAMA,KA/LA;AAiMA;AACA,IAAA,WAlMA,yBAkMA;AACA,WAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA;AApMA,GAxDA;AA+PA,EAAA,aA/PA,2BA+PA;AACA,SAAA,mBAAA;AACA;AAjQA,CAAA", "sourcesContent": ["<template>\n    <el-dialog\n        :title=\"dialogTitle\"\n        :visible.sync=\"visible\"\n        width=\"600px\"\n        :before-close=\"handleClose\"\n        class=\"chat-dialog\">\n        \n        <div class=\"chat-container\">\n            <!-- 聊天消息区域 -->\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\n                <div \n                    v-for=\"message in messages\" \n                    :key=\"message.id\"\n                    :class=\"['message-item', message.senderId === currentUserId ? 'sent' : 'received']\">\n                    \n                    <div class=\"message-info\">\n                        <span class=\"sender-name\">\n                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}\n                        </span>\n                        <span class=\"send-time\">{{ formatTime(message.sendTime) }}</span>\n                    </div>\n                    \n                    <div class=\"message-content\">\n                        {{ message.content }}\n                    </div>\n                </div>\n                \n                <!-- 无消息提示 -->\n                <div v-if=\"messages.length === 0\" class=\"no-messages\">\n                    <i class=\"el-icon-chat-dot-round\"></i>\n                    <p>开始聊天吧！</p>\n                </div>\n            </div>\n            \n            <!-- 消息输入区域 -->\n            <div class=\"chat-input\">\n                <el-input\n                    v-model=\"newMessage\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    placeholder=\"输入消息...\"\n                    @keydown.enter.native=\"handleEnterKey\"\n                    :disabled=\"!connected\">\n                </el-input>\n                \n                <div class=\"input-actions\">\n                    <span v-if=\"!connected\" class=\"connection-status\">\n                        <i class=\"el-icon-loading\"></i> 连接中...\n                    </span>\n                    <el-button \n                        type=\"primary\" \n                        size=\"small\" \n                        @click=\"sendMessage\"\n                        :disabled=\"!newMessage.trim() || !connected\">\n                        发送\n                    </el-button>\n                </div>\n            </div>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nimport SockJS from 'sockjs-client'\nimport Stomp from 'stompjs'\n\nexport default {\n    name: 'Chat',\n    props: {\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        task: {\n            type: Object,\n            required: true\n        },\n        currentUserId: {\n            type: Number,\n            required: true\n        }\n    },\n    data() {\n        return {\n            stompClient: null,\n            connected: false,\n            messages: [],\n            newMessage: '',\n            subscription: null\n        }\n    },\n    computed: {\n        dialogTitle() {\n            if (!this.task) return '在线交流'\n            return `任务交流 - ${this.task.taskTitle}`\n        },\n        \n        // 获取对方用户ID\n        otherUserId() {\n            if (!this.task) return null\n            \n            // 如果当前用户是发布者，对方就是接受者\n            if (this.currentUserId === this.task.publishId) {\n                return this.task.acceptId\n            }\n            // 如果当前用户是接受者，对方就是发布者\n            else if (this.currentUserId === this.task.acceptId) {\n                return this.task.publishId\n            }\n            \n            return null\n        }\n    },\n    watch: {\n        visible(newVal) {\n            if (newVal) {\n                this.openChat()\n            } else {\n                this.closeChat()\n            }\n        }\n    },\n    methods: {\n        // 打开聊天\n        openChat() {\n            this.connectWebSocket()\n            this.loadChatHistory()\n        },\n        \n        // 关闭聊天\n        closeChat() {\n            this.disconnectWebSocket()\n            this.messages = []\n            this.newMessage = ''\n        },\n        \n        // 连接WebSocket\n        connectWebSocket() {\n            if (this.stompClient && this.connected) {\n                return\n            }\n            \n            try {\n                const socket = new SockJS('/api/ws')\n                this.stompClient = Stomp.over(socket)\n                \n                // 启用调试日志\n                this.stompClient.debug = function(str) {\n                    console.log('STOMP: ' + str)\n                }\n                \n                this.stompClient.connect({}, \n                    (frame) => {\n                        console.log('WebSocket连接成功:', frame)\n                        this.connected = true\n                        \n                        // 订阅个人消息队列\n                        this.subscription = this.stompClient.subscribe(\n                            `/user/${this.currentUserId}/queue/messages`,\n                            (message) => {\n                                const chatMessage = JSON.parse(message.body)\n                                this.handleReceivedMessage(chatMessage)\n                            }\n                        )\n                        \n                        // 发送加入聊天的消息\n                        this.stompClient.send('/app/chat.addUser', {}, JSON.stringify({\n                            senderId: this.currentUserId,\n                            taskId: this.task.id\n                        }))\n                    },\n                    (error) => {\n                        console.error('WebSocket连接失败:', error)\n                        this.connected = false\n                        this.$message.error('连接聊天服务失败，请稍后重试')\n                    }\n                )\n            } catch (error) {\n                console.error('创建WebSocket连接时发生错误:', error)\n                this.$message.error('无法建立聊天连接')\n            }\n        },\n        \n        // 断开WebSocket连接\n        disconnectWebSocket() {\n            if (this.subscription) {\n                this.subscription.unsubscribe()\n                this.subscription = null\n            }\n            \n            if (this.stompClient && this.connected) {\n                this.stompClient.disconnect(() => {\n                    console.log('WebSocket连接已断开')\n                })\n            }\n            \n            this.stompClient = null\n            this.connected = false\n        },\n        \n        // 发送消息\n        sendMessage() {\n            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {\n                return\n            }\n            \n            const message = {\n                taskId: this.task.id,\n                senderId: this.currentUserId,\n                receiverId: this.otherUserId,\n                content: this.newMessage.trim(),\n                messageType: 'text'\n            }\n            \n            try {\n                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))\n                this.newMessage = ''\n            } catch (error) {\n                console.error('发送消息失败:', error)\n                this.$message.error('发送消息失败，请重试')\n            }\n        },\n        \n        // 处理接收到的消息\n        handleReceivedMessage(message) {\n            console.log('收到消息:', message)\n\n            // 检查消息是否属于当前任务\n            if (message.taskId === this.task.id) {\n                // 避免重复添加消息（如果消息有ID的话）\n                let exists = false\n                if (message.id) {\n                    exists = this.messages.find(m => m.id === message.id)\n                }\n\n                if (!exists) {\n                    // 确保消息有必要的字段\n                    if (!message.sendTime) {\n                        message.sendTime = new Date()\n                    }\n\n                    this.messages.push(message)\n                    console.log('消息已添加到列表，当前消息数量:', this.messages.length)\n\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                }\n            }\n        },\n        \n        // 加载聊天历史记录\n        loadChatHistory() {\n            this.$get('/api/chat/history', {\n                taskId: this.task.id,\n                userId: this.currentUserId\n            }).then(res => {\n                if (res.data.status) {\n                    this.messages = res.data.messages || []\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                } else {\n                    console.error('加载聊天记录失败:', res.data.msg)\n                }\n            }).catch(error => {\n                console.error('加载聊天记录失败:', error)\n            })\n        },\n        \n        // 滚动到底部\n        scrollToBottom() {\n            const container = this.$refs.messagesContainer\n            if (container) {\n                container.scrollTop = container.scrollHeight\n            }\n        },\n        \n        // 处理Enter键\n        handleEnterKey(event) {\n            if (event.ctrlKey || event.shiftKey) {\n                // Ctrl+Enter 或 Shift+Enter 换行\n                return\n            } else {\n                // 单独Enter发送消息\n                event.preventDefault()\n                this.sendMessage()\n            }\n        },\n        \n        // 格式化时间\n        formatTime(time) {\n            if (!time) return ''\n            \n            const date = new Date(time)\n            const now = new Date()\n            const diff = now - date\n            \n            // 如果是今天\n            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n                return date.toLocaleTimeString('zh-CN', { \n                    hour: '2-digit', \n                    minute: '2-digit' \n                })\n            }\n            \n            // 如果是昨天或更早\n            return date.toLocaleString('zh-CN', {\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        },\n        \n        // 处理对话框关闭\n        handleClose() {\n            this.$emit('update:visible', false)\n        }\n    },\n    \n    beforeDestroy() {\n        this.disconnectWebSocket()\n    }\n}\n</script>\n\n<style scoped lang=\"less\">\n.chat-dialog {\n    /deep/ .el-dialog__body {\n        padding: 0;\n    }\n}\n\n.chat-container {\n    height: 500px;\n    display: flex;\n    flex-direction: column;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #ebeef5;\n\n    .message-item {\n        margin-bottom: 15px;\n\n        &.sent {\n            text-align: right;\n\n            .message-info {\n                justify-content: flex-end;\n            }\n\n            .message-content {\n                background-color: #409EFF;\n                color: white;\n                margin-left: auto;\n                margin-right: 0;\n            }\n        }\n\n        &.received {\n            text-align: left;\n\n            .message-info {\n                justify-content: flex-start;\n            }\n\n            .message-content {\n                background-color: white;\n                color: #303133;\n                margin-left: 0;\n                margin-right: auto;\n            }\n        }\n    }\n\n    .message-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 5px;\n        font-size: 12px;\n        color: #909399;\n\n        .sender-name {\n            font-weight: bold;\n            margin-right: 8px;\n        }\n\n        .send-time {\n            font-size: 11px;\n        }\n    }\n\n    .message-content {\n        display: inline-block;\n        max-width: 70%;\n        padding: 10px 15px;\n        border-radius: 10px;\n        word-wrap: break-word;\n        white-space: pre-wrap;\n        line-height: 1.4;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .no-messages {\n        text-align: center;\n        color: #909399;\n        margin-top: 50px;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin: 0;\n        }\n    }\n}\n\n.chat-input {\n    padding: 15px;\n    background-color: white;\n\n    .input-actions {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 10px;\n\n        .connection-status {\n            color: #909399;\n            font-size: 12px;\n\n            i {\n                margin-right: 5px;\n            }\n        }\n    }\n}\n\n/* 滚动条样式 */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n}\n</style>\n"], "sourceRoot": "src/components"}]}