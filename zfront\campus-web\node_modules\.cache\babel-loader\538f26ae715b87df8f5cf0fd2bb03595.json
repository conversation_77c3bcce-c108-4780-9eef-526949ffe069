{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue", "mtime": 1748751786301}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Chat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,OAAA,MAAA,MAAA,eAAA;AACA,OAAA,KAAA,MAAA,SAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA,KALA;AASA,IAAA,aAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA;AATA,GAFA;AAgBA,EAAA,IAhBA,kBAgBA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,KAFA;AAGA,MAAA,QAAA,EAAA,EAHA;AAIA,MAAA,UAAA,EAAA,EAJA;AAKA,MAAA,YAAA,EAAA;AALA,KAAA;AAOA,GAxBA;AAyBA,EAAA,QAAA,EAAA;AACA,IAAA,WADA,yBACA;AACA,UAAA,CAAA,KAAA,IAAA,EAAA,OAAA,MAAA;AACA,kDAAA,KAAA,IAAA,CAAA,SAAA;AACA,KAJA;AAMA;AACA,IAAA,WAPA,yBAOA;AACA,UAAA,CAAA,KAAA,IAAA,EAAA,OAAA,IAAA,CADA,CAGA;;AACA,UAAA,KAAA,aAAA,KAAA,KAAA,IAAA,CAAA,SAAA,EAAA;AACA,eAAA,KAAA,IAAA,CAAA,QAAA;AACA,OAFA,CAGA;AAHA,WAIA,IAAA,KAAA,aAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,KAAA,IAAA,CAAA,SAAA;AACA;;AAEA,aAAA,IAAA;AACA;AApBA,GAzBA;AA+CA,EAAA,KAAA,EAAA;AACA,IAAA,OADA,mBACA,MADA,EACA;AACA,UAAA,MAAA,EAAA;AACA,aAAA,QAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA;AACA;AACA;AAPA,GA/CA;AAwDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,sBAEA;AACA,WAAA,gBAAA;AACA,WAAA,eAAA;AACA,KALA;AAOA;AACA,IAAA,SARA,uBAQA;AACA,WAAA,mBAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,EAAA;AACA,KAZA;AAcA;AACA,IAAA,gBAfA,8BAeA;AAAA;;AACA,UAAA,KAAA,WAAA,IAAA,KAAA,SAAA,EAAA;AACA;AACA;;AAEA,UAAA;AACA,YAAA,MAAA,GAAA,IAAA,MAAA,CAAA,KAAA,CAAA;AACA,aAAA,WAAA,GAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAFA,CAIA;;AACA,aAAA,WAAA,CAAA,KAAA,GAAA,IAAA;AAEA,aAAA,WAAA,CAAA,OAAA,CAAA,EAAA,EACA,UAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,KAAA;AACA,UAAA,KAAA,CAAA,SAAA,GAAA,IAAA,CAFA,CAIA;;AACA,UAAA,KAAA,CAAA,YAAA,GAAA,KAAA,CAAA,WAAA,CAAA,SAAA,iBACA,KAAA,CAAA,aADA,sBAEA,UAAA,OAAA,EAAA;AACA,gBAAA,WAAA,GAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA;;AACA,YAAA,KAAA,CAAA,qBAAA,CAAA,WAAA;AACA,WALA,CAAA,CALA,CAaA;;AACA,UAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,QAAA,EAAA,KAAA,CAAA,aADA;AAEA,YAAA,MAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AAFA,WAAA,CAAA;AAIA,SAnBA,EAoBA,UAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA,UAAA,KAAA,CAAA,SAAA,GAAA,KAAA;;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,SAxBA;AA0BA,OAjCA,CAiCA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,KAzDA;AA2DA;AACA,IAAA,mBA5DA,iCA4DA;AACA,UAAA,KAAA,YAAA,EAAA;AACA,aAAA,YAAA,CAAA,WAAA;AACA,aAAA,YAAA,GAAA,IAAA;AACA;;AAEA,UAAA,KAAA,WAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AACA,SAFA;AAGA;;AAEA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,KA1EA;AA4EA;AACA,IAAA,WA7EA,yBA6EA;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,SAAA,IAAA,CAAA,KAAA,WAAA,EAAA;AACA;AACA;;AAEA,UAAA,OAAA,GAAA;AACA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,KAAA,aAFA;AAGA,QAAA,UAAA,EAAA,KAAA,WAHA;AAIA,QAAA,OAAA,EAAA,KAAA,UAAA,CAAA,IAAA,EAJA;AAKA,QAAA,WAAA,EAAA;AALA,OAAA;;AAQA,UAAA;AACA,aAAA,WAAA,CAAA,IAAA,CAAA,uBAAA,EAAA,EAAA,EAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA;AACA,aAAA,UAAA,GAAA,EAAA;AACA,OAHA,CAGA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA;AACA,KAjGA;AAmGA;AACA,IAAA,qBApGA,iCAoGA,OApGA,EAoGA;AAAA;;AACA;AACA,UAAA,OAAA,CAAA,MAAA,KAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,EAAA,KAAA,OAAA,CAAA,EAAA;AAAA,SAAA,CAAA;;AACA,YAAA,CAAA,MAAA,EAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,eAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,cAAA;AACA,WAFA;AAGA;AACA;AACA,KAhHA;AAkHA;AACA,IAAA,eAnHA,6BAmHA;AAAA;;AACA,WAAA,IAAA,CAAA,mBAAA,EAAA;AACA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,MAAA,EAAA,KAAA;AAFA,OAAA,EAGA,IAHA,CAGA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,QAAA,IAAA,EAAA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,cAAA;AACA,WAFA;AAGA,SALA,MAKA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAZA,EAYA,KAZA,CAYA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,OAdA;AAeA,KAnIA;AAqIA;AACA,IAAA,cAtIA,4BAsIA;AACA,UAAA,SAAA,GAAA,KAAA,KAAA,CAAA,iBAAA;;AACA,UAAA,SAAA,EAAA;AACA,QAAA,SAAA,CAAA,SAAA,GAAA,SAAA,CAAA,YAAA;AACA;AACA,KA3IA;AA6IA;AACA,IAAA,cA9IA,0BA8IA,KA9IA,EA8IA;AACA,UAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AACA;AACA;AACA,OAHA,MAGA;AACA;AACA,QAAA,KAAA,CAAA,cAAA;AACA,aAAA,WAAA;AACA;AACA,KAvJA;AAyJA;AACA,IAAA,UA1JA,sBA0JA,IA1JA,EA0JA;AACA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA;AAEA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CALA,CAOA;;AACA,UAAA,IAAA,GAAA,KAAA,EAAA,GAAA,EAAA,GAAA,IAAA,IAAA,IAAA,CAAA,OAAA,OAAA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,kBAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,MAAA,EAAA;AAFA,SAAA,CAAA;AAIA,OAbA,CAeA;;;AACA,aAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAAA,CAAA;AAMA,KAhLA;AAkLA;AACA,IAAA,WAnLA,yBAmLA;AACA,WAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA;AArLA,GAxDA;AAgPA,EAAA,aAhPA,2BAgPA;AACA,SAAA,mBAAA;AACA;AAlPA,CAAA", "sourcesContent": ["<template>\n    <el-dialog\n        :title=\"dialogTitle\"\n        :visible.sync=\"visible\"\n        width=\"600px\"\n        :before-close=\"handleClose\"\n        class=\"chat-dialog\">\n        \n        <div class=\"chat-container\">\n            <!-- 聊天消息区域 -->\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\n                <div \n                    v-for=\"message in messages\" \n                    :key=\"message.id\"\n                    :class=\"['message-item', message.senderId === currentUserId ? 'sent' : 'received']\">\n                    \n                    <div class=\"message-info\">\n                        <span class=\"sender-name\">\n                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}\n                        </span>\n                        <span class=\"send-time\">{{ formatTime(message.sendTime) }}</span>\n                    </div>\n                    \n                    <div class=\"message-content\">\n                        {{ message.content }}\n                    </div>\n                </div>\n                \n                <!-- 无消息提示 -->\n                <div v-if=\"messages.length === 0\" class=\"no-messages\">\n                    <i class=\"el-icon-chat-dot-round\"></i>\n                    <p>开始聊天吧！</p>\n                </div>\n            </div>\n            \n            <!-- 消息输入区域 -->\n            <div class=\"chat-input\">\n                <el-input\n                    v-model=\"newMessage\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    placeholder=\"输入消息...\"\n                    @keydown.enter.native=\"handleEnterKey\"\n                    :disabled=\"!connected\">\n                </el-input>\n                \n                <div class=\"input-actions\">\n                    <span v-if=\"!connected\" class=\"connection-status\">\n                        <i class=\"el-icon-loading\"></i> 连接中...\n                    </span>\n                    <el-button \n                        type=\"primary\" \n                        size=\"small\" \n                        @click=\"sendMessage\"\n                        :disabled=\"!newMessage.trim() || !connected\">\n                        发送\n                    </el-button>\n                </div>\n            </div>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nimport SockJS from 'sockjs-client'\nimport Stomp from 'stompjs'\n\nexport default {\n    name: 'Chat',\n    props: {\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        task: {\n            type: Object,\n            required: true\n        },\n        currentUserId: {\n            type: Number,\n            required: true\n        }\n    },\n    data() {\n        return {\n            stompClient: null,\n            connected: false,\n            messages: [],\n            newMessage: '',\n            subscription: null\n        }\n    },\n    computed: {\n        dialogTitle() {\n            if (!this.task) return '在线交流'\n            return `任务交流 - ${this.task.taskTitle}`\n        },\n        \n        // 获取对方用户ID\n        otherUserId() {\n            if (!this.task) return null\n            \n            // 如果当前用户是发布者，对方就是接受者\n            if (this.currentUserId === this.task.publishId) {\n                return this.task.acceptId\n            }\n            // 如果当前用户是接受者，对方就是发布者\n            else if (this.currentUserId === this.task.acceptId) {\n                return this.task.publishId\n            }\n            \n            return null\n        }\n    },\n    watch: {\n        visible(newVal) {\n            if (newVal) {\n                this.openChat()\n            } else {\n                this.closeChat()\n            }\n        }\n    },\n    methods: {\n        // 打开聊天\n        openChat() {\n            this.connectWebSocket()\n            this.loadChatHistory()\n        },\n        \n        // 关闭聊天\n        closeChat() {\n            this.disconnectWebSocket()\n            this.messages = []\n            this.newMessage = ''\n        },\n        \n        // 连接WebSocket\n        connectWebSocket() {\n            if (this.stompClient && this.connected) {\n                return\n            }\n            \n            try {\n                const socket = new SockJS('/ws')\n                this.stompClient = Stomp.over(socket)\n                \n                // 禁用调试日志\n                this.stompClient.debug = null\n                \n                this.stompClient.connect({}, \n                    (frame) => {\n                        console.log('WebSocket连接成功:', frame)\n                        this.connected = true\n                        \n                        // 订阅个人消息队列\n                        this.subscription = this.stompClient.subscribe(\n                            `/user/${this.currentUserId}/queue/messages`,\n                            (message) => {\n                                const chatMessage = JSON.parse(message.body)\n                                this.handleReceivedMessage(chatMessage)\n                            }\n                        )\n                        \n                        // 发送加入聊天的消息\n                        this.stompClient.send('/app/chat.addUser', {}, JSON.stringify({\n                            senderId: this.currentUserId,\n                            taskId: this.task.id\n                        }))\n                    },\n                    (error) => {\n                        console.error('WebSocket连接失败:', error)\n                        this.connected = false\n                        this.$message.error('连接聊天服务失败，请稍后重试')\n                    }\n                )\n            } catch (error) {\n                console.error('创建WebSocket连接时发生错误:', error)\n                this.$message.error('无法建立聊天连接')\n            }\n        },\n        \n        // 断开WebSocket连接\n        disconnectWebSocket() {\n            if (this.subscription) {\n                this.subscription.unsubscribe()\n                this.subscription = null\n            }\n            \n            if (this.stompClient && this.connected) {\n                this.stompClient.disconnect(() => {\n                    console.log('WebSocket连接已断开')\n                })\n            }\n            \n            this.stompClient = null\n            this.connected = false\n        },\n        \n        // 发送消息\n        sendMessage() {\n            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {\n                return\n            }\n            \n            const message = {\n                taskId: this.task.id,\n                senderId: this.currentUserId,\n                receiverId: this.otherUserId,\n                content: this.newMessage.trim(),\n                messageType: 'text'\n            }\n            \n            try {\n                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))\n                this.newMessage = ''\n            } catch (error) {\n                console.error('发送消息失败:', error)\n                this.$message.error('发送消息失败，请重试')\n            }\n        },\n        \n        // 处理接收到的消息\n        handleReceivedMessage(message) {\n            // 检查消息是否属于当前任务\n            if (message.taskId === this.task.id) {\n                // 避免重复添加消息\n                const exists = this.messages.find(m => m.id === message.id)\n                if (!exists) {\n                    this.messages.push(message)\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                }\n            }\n        },\n        \n        // 加载聊天历史记录\n        loadChatHistory() {\n            this.$get('/api/chat/history', {\n                taskId: this.task.id,\n                userId: this.currentUserId\n            }).then(res => {\n                if (res.data.status) {\n                    this.messages = res.data.messages || []\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                } else {\n                    console.error('加载聊天记录失败:', res.data.msg)\n                }\n            }).catch(error => {\n                console.error('加载聊天记录失败:', error)\n            })\n        },\n        \n        // 滚动到底部\n        scrollToBottom() {\n            const container = this.$refs.messagesContainer\n            if (container) {\n                container.scrollTop = container.scrollHeight\n            }\n        },\n        \n        // 处理Enter键\n        handleEnterKey(event) {\n            if (event.ctrlKey || event.shiftKey) {\n                // Ctrl+Enter 或 Shift+Enter 换行\n                return\n            } else {\n                // 单独Enter发送消息\n                event.preventDefault()\n                this.sendMessage()\n            }\n        },\n        \n        // 格式化时间\n        formatTime(time) {\n            if (!time) return ''\n            \n            const date = new Date(time)\n            const now = new Date()\n            const diff = now - date\n            \n            // 如果是今天\n            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n                return date.toLocaleTimeString('zh-CN', { \n                    hour: '2-digit', \n                    minute: '2-digit' \n                })\n            }\n            \n            // 如果是昨天或更早\n            return date.toLocaleString('zh-CN', {\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        },\n        \n        // 处理对话框关闭\n        handleClose() {\n            this.$emit('update:visible', false)\n        }\n    },\n    \n    beforeDestroy() {\n        this.disconnectWebSocket()\n    }\n}\n</script>\n\n<style scoped lang=\"less\">\n.chat-dialog {\n    /deep/ .el-dialog__body {\n        padding: 0;\n    }\n}\n\n.chat-container {\n    height: 500px;\n    display: flex;\n    flex-direction: column;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #ebeef5;\n\n    .message-item {\n        margin-bottom: 15px;\n\n        &.sent {\n            text-align: right;\n\n            .message-info {\n                justify-content: flex-end;\n            }\n\n            .message-content {\n                background-color: #409EFF;\n                color: white;\n                margin-left: auto;\n                margin-right: 0;\n            }\n        }\n\n        &.received {\n            text-align: left;\n\n            .message-info {\n                justify-content: flex-start;\n            }\n\n            .message-content {\n                background-color: white;\n                color: #303133;\n                margin-left: 0;\n                margin-right: auto;\n            }\n        }\n    }\n\n    .message-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 5px;\n        font-size: 12px;\n        color: #909399;\n\n        .sender-name {\n            font-weight: bold;\n            margin-right: 8px;\n        }\n\n        .send-time {\n            font-size: 11px;\n        }\n    }\n\n    .message-content {\n        display: inline-block;\n        max-width: 70%;\n        padding: 10px 15px;\n        border-radius: 10px;\n        word-wrap: break-word;\n        white-space: pre-wrap;\n        line-height: 1.4;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .no-messages {\n        text-align: center;\n        color: #909399;\n        margin-top: 50px;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin: 0;\n        }\n    }\n}\n\n.chat-input {\n    padding: 15px;\n    background-color: white;\n\n    .input-actions {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 10px;\n\n        .connection-status {\n            color: #909399;\n            font-size: 12px;\n\n            i {\n                margin-right: 5px;\n            }\n        }\n    }\n}\n\n/* 滚动条样式 */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n}\n</style>\n"], "sourceRoot": "src/components"}]}