{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue?vue&type=style&index=0&id=2bc3d388&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\components\\Chat.vue", "mtime": 1748752217811}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY2hhdC1kaWFsb2cgewogICAgL2RlZXAvIC5lbC1kaWFsb2dfX2JvZHkgewogICAgICAgIHBhZGRpbmc6IDA7CiAgICB9Cn0KCi5jaGF0LWNvbnRhaW5lciB7CiAgICBoZWlnaHQ6IDUwMHB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi5jaGF0LW1lc3NhZ2VzIHsKICAgIGZsZXg6IDE7CiAgICBvdmVyZmxvdy15OiBhdXRvOwogICAgcGFkZGluZzogMTVweDsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsKCiAgICAubWVzc2FnZS1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAgICAgICAmLnNlbnQgewogICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsKCiAgICAgICAgICAgIC5tZXNzYWdlLWluZm8gewogICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLm1lc3NhZ2UtY29udGVudCB7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgICAgY29sb3I6IHdoaXRlOwogICAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IGF1dG87CiAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDA7CiAgICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgICYucmVjZWl2ZWQgewogICAgICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0OwoKICAgICAgICAgICAgLm1lc3NhZ2UtaW5mbyB7CiAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC5tZXNzYWdlLWNvbnRlbnQgewogICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7CiAgICAgICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAwOwogICAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiBhdXRvOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfQoKICAgIC5tZXNzYWdlLWluZm8gewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgIGNvbG9yOiAjOTA5Mzk5OwoKICAgICAgICAuc2VuZGVyLW5hbWUgewogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgICAgfQoKICAgICAgICAuc2VuZC10aW1lIHsKICAgICAgICAgICAgZm9udC1zaXplOiAxMXB4OwogICAgICAgIH0KICAgIH0KCiAgICAubWVzc2FnZS1jb250ZW50IHsKICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgICAgbWF4LXdpZHRoOiA3MCU7CiAgICAgICAgcGFkZGluZzogMTBweCAxNXB4OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOwogICAgICAgIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsKICAgICAgICBsaW5lLWhlaWdodDogMS40OwogICAgICAgIGJveC1zaGFkb3c6IDAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgICB9CgogICAgLm5vLW1lc3NhZ2VzIHsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgY29sb3I6ICM5MDkzOTk7CiAgICAgICAgbWFyZ2luLXRvcDogNTBweDsKCiAgICAgICAgaSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgfQoKICAgICAgICBwIHsKICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgICBtYXJnaW46IDA7CiAgICAgICAgfQogICAgfQp9CgouY2hhdC1pbnB1dCB7CiAgICBwYWRkaW5nOiAxNXB4OwogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7CgogICAgLmlucHV0LWFjdGlvbnMgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKCiAgICAgICAgLmNvbm5lY3Rpb24tc3RhdHVzIHsKICAgICAgICAgICAgY29sb3I6ICM5MDkzOTk7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKCiAgICAgICAgICAgIGkgewogICAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgICAgIH0KICAgICAgICB9CiAgICB9Cn0KCi8qIOa7muWKqOadoeagt+W8jyAqLwouY2hhdC1tZXNzYWdlczo6LXdlYmtpdC1zY3JvbGxiYXIgewogICAgd2lkdGg6IDZweDsKfQoKLmNoYXQtbWVzc2FnZXM6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICAgIGJhY2tncm91bmQ6ICNmMWYxZjE7CiAgICBib3JkZXItcmFkaXVzOiAzcHg7Cn0KCi5jaGF0LW1lc3NhZ2VzOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxOwogICAgYm9yZGVyLXJhZGl1czogM3B4Owp9CgouY2hhdC1tZXNzYWdlczo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgewogICAgYmFja2dyb3VuZDogI2E4YThhODsKfQo="}, {"version": 3, "sources": ["Chat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0TA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "Chat.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n    <el-dialog\n        :title=\"dialogTitle\"\n        :visible.sync=\"visible\"\n        width=\"600px\"\n        :before-close=\"handleClose\"\n        class=\"chat-dialog\">\n        \n        <div class=\"chat-container\">\n            <!-- 聊天消息区域 -->\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\n                <div \n                    v-for=\"message in messages\" \n                    :key=\"message.id\"\n                    :class=\"['message-item', message.senderId === currentUserId ? 'sent' : 'received']\">\n                    \n                    <div class=\"message-info\">\n                        <span class=\"sender-name\">\n                            {{ message.senderId === currentUserId ? '我' : (message.sender ? message.sender.username : '对方') }}\n                        </span>\n                        <span class=\"send-time\">{{ formatTime(message.sendTime) }}</span>\n                    </div>\n                    \n                    <div class=\"message-content\">\n                        {{ message.content }}\n                    </div>\n                </div>\n                \n                <!-- 无消息提示 -->\n                <div v-if=\"messages.length === 0\" class=\"no-messages\">\n                    <i class=\"el-icon-chat-dot-round\"></i>\n                    <p>开始聊天吧！</p>\n                </div>\n            </div>\n            \n            <!-- 消息输入区域 -->\n            <div class=\"chat-input\">\n                <el-input\n                    v-model=\"newMessage\"\n                    type=\"textarea\"\n                    :rows=\"3\"\n                    placeholder=\"输入消息...\"\n                    @keydown.enter.native=\"handleEnterKey\"\n                    :disabled=\"!connected\">\n                </el-input>\n                \n                <div class=\"input-actions\">\n                    <span v-if=\"!connected\" class=\"connection-status\">\n                        <i class=\"el-icon-loading\"></i> 连接中...\n                    </span>\n                    <el-button \n                        type=\"primary\" \n                        size=\"small\" \n                        @click=\"sendMessage\"\n                        :disabled=\"!newMessage.trim() || !connected\">\n                        发送\n                    </el-button>\n                </div>\n            </div>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nimport SockJS from 'sockjs-client'\nimport Stomp from 'stompjs'\n\nexport default {\n    name: 'Chat',\n    props: {\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        task: {\n            type: Object,\n            required: true\n        },\n        currentUserId: {\n            type: Number,\n            required: true\n        }\n    },\n    data() {\n        return {\n            stompClient: null,\n            connected: false,\n            messages: [],\n            newMessage: '',\n            subscription: null\n        }\n    },\n    computed: {\n        dialogTitle() {\n            if (!this.task) return '在线交流'\n            return `任务交流 - ${this.task.taskTitle}`\n        },\n        \n        // 获取对方用户ID\n        otherUserId() {\n            if (!this.task) return null\n            \n            // 如果当前用户是发布者，对方就是接受者\n            if (this.currentUserId === this.task.publishId) {\n                return this.task.acceptId\n            }\n            // 如果当前用户是接受者，对方就是发布者\n            else if (this.currentUserId === this.task.acceptId) {\n                return this.task.publishId\n            }\n            \n            return null\n        }\n    },\n    watch: {\n        visible(newVal) {\n            if (newVal) {\n                this.openChat()\n            } else {\n                this.closeChat()\n            }\n        }\n    },\n    methods: {\n        // 打开聊天\n        openChat() {\n            this.connectWebSocket()\n            this.loadChatHistory()\n        },\n        \n        // 关闭聊天\n        closeChat() {\n            this.disconnectWebSocket()\n            this.messages = []\n            this.newMessage = ''\n        },\n        \n        // 连接WebSocket\n        connectWebSocket() {\n            if (this.stompClient && this.connected) {\n                return\n            }\n            \n            try {\n                const socket = new SockJS('/ws')\n                this.stompClient = Stomp.over(socket)\n                \n                // 禁用调试日志\n                this.stompClient.debug = null\n                \n                this.stompClient.connect({}, \n                    (frame) => {\n                        console.log('WebSocket连接成功:', frame)\n                        this.connected = true\n                        \n                        // 订阅个人消息队列\n                        this.subscription = this.stompClient.subscribe(\n                            `/user/${this.currentUserId}/queue/messages`,\n                            (message) => {\n                                const chatMessage = JSON.parse(message.body)\n                                this.handleReceivedMessage(chatMessage)\n                            }\n                        )\n                        \n                        // 发送加入聊天的消息\n                        this.stompClient.send('/app/chat.addUser', {}, JSON.stringify({\n                            senderId: this.currentUserId,\n                            taskId: this.task.id\n                        }))\n                    },\n                    (error) => {\n                        console.error('WebSocket连接失败:', error)\n                        this.connected = false\n                        this.$message.error('连接聊天服务失败，请稍后重试')\n                    }\n                )\n            } catch (error) {\n                console.error('创建WebSocket连接时发生错误:', error)\n                this.$message.error('无法建立聊天连接')\n            }\n        },\n        \n        // 断开WebSocket连接\n        disconnectWebSocket() {\n            if (this.subscription) {\n                this.subscription.unsubscribe()\n                this.subscription = null\n            }\n            \n            if (this.stompClient && this.connected) {\n                this.stompClient.disconnect(() => {\n                    console.log('WebSocket连接已断开')\n                })\n            }\n            \n            this.stompClient = null\n            this.connected = false\n        },\n        \n        // 发送消息\n        sendMessage() {\n            if (!this.newMessage.trim() || !this.connected || !this.otherUserId) {\n                return\n            }\n            \n            const message = {\n                taskId: this.task.id,\n                senderId: this.currentUserId,\n                receiverId: this.otherUserId,\n                content: this.newMessage.trim(),\n                messageType: 'text'\n            }\n            \n            try {\n                this.stompClient.send('/app/chat.sendMessage', {}, JSON.stringify(message))\n                this.newMessage = ''\n            } catch (error) {\n                console.error('发送消息失败:', error)\n                this.$message.error('发送消息失败，请重试')\n            }\n        },\n        \n        // 处理接收到的消息\n        handleReceivedMessage(message) {\n            // 检查消息是否属于当前任务\n            if (message.taskId === this.task.id) {\n                // 避免重复添加消息\n                const exists = this.messages.find(m => m.id === message.id)\n                if (!exists) {\n                    this.messages.push(message)\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                }\n            }\n        },\n        \n        // 加载聊天历史记录\n        loadChatHistory() {\n            this.$get('/api/chat/history', {\n                taskId: this.task.id,\n                userId: this.currentUserId\n            }).then(res => {\n                if (res.data.status) {\n                    this.messages = res.data.messages || []\n                    this.$nextTick(() => {\n                        this.scrollToBottom()\n                    })\n                } else {\n                    console.error('加载聊天记录失败:', res.data.msg)\n                }\n            }).catch(error => {\n                console.error('加载聊天记录失败:', error)\n            })\n        },\n        \n        // 滚动到底部\n        scrollToBottom() {\n            const container = this.$refs.messagesContainer\n            if (container) {\n                container.scrollTop = container.scrollHeight\n            }\n        },\n        \n        // 处理Enter键\n        handleEnterKey(event) {\n            if (event.ctrlKey || event.shiftKey) {\n                // Ctrl+Enter 或 Shift+Enter 换行\n                return\n            } else {\n                // 单独Enter发送消息\n                event.preventDefault()\n                this.sendMessage()\n            }\n        },\n        \n        // 格式化时间\n        formatTime(time) {\n            if (!time) return ''\n            \n            const date = new Date(time)\n            const now = new Date()\n            const diff = now - date\n            \n            // 如果是今天\n            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n                return date.toLocaleTimeString('zh-CN', { \n                    hour: '2-digit', \n                    minute: '2-digit' \n                })\n            }\n            \n            // 如果是昨天或更早\n            return date.toLocaleString('zh-CN', {\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        },\n        \n        // 处理对话框关闭\n        handleClose() {\n            this.$emit('update:visible', false)\n        }\n    },\n    \n    beforeDestroy() {\n        this.disconnectWebSocket()\n    }\n}\n</script>\n\n<style scoped lang=\"less\">\n.chat-dialog {\n    /deep/ .el-dialog__body {\n        padding: 0;\n    }\n}\n\n.chat-container {\n    height: 500px;\n    display: flex;\n    flex-direction: column;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #ebeef5;\n\n    .message-item {\n        margin-bottom: 15px;\n\n        &.sent {\n            text-align: right;\n\n            .message-info {\n                justify-content: flex-end;\n            }\n\n            .message-content {\n                background-color: #409EFF;\n                color: white;\n                margin-left: auto;\n                margin-right: 0;\n            }\n        }\n\n        &.received {\n            text-align: left;\n\n            .message-info {\n                justify-content: flex-start;\n            }\n\n            .message-content {\n                background-color: white;\n                color: #303133;\n                margin-left: 0;\n                margin-right: auto;\n            }\n        }\n    }\n\n    .message-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 5px;\n        font-size: 12px;\n        color: #909399;\n\n        .sender-name {\n            font-weight: bold;\n            margin-right: 8px;\n        }\n\n        .send-time {\n            font-size: 11px;\n        }\n    }\n\n    .message-content {\n        display: inline-block;\n        max-width: 70%;\n        padding: 10px 15px;\n        border-radius: 10px;\n        word-wrap: break-word;\n        white-space: pre-wrap;\n        line-height: 1.4;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .no-messages {\n        text-align: center;\n        color: #909399;\n        margin-top: 50px;\n\n        i {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        p {\n            font-size: 16px;\n            margin: 0;\n        }\n    }\n}\n\n.chat-input {\n    padding: 15px;\n    background-color: white;\n\n    .input-actions {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 10px;\n\n        .connection-status {\n            color: #909399;\n            font-size: 12px;\n\n            i {\n                margin-right: 5px;\n            }\n        }\n    }\n}\n\n/* 滚动条样式 */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n}\n\n.chat-messages::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n}\n</style>\n"]}]}